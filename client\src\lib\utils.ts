import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

export function copyToClipboard(text: string): Promise<void> {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text)
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'absolute'
    textArea.style.left = '-999999px'
    document.body.prepend(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
    } catch (error) {
      console.error('Failed to copy text: ', error)
      throw error
    } finally {
      textArea.remove()
    }
    return Promise.resolve()
  }
}

export function downloadFile(content: string, filename: string, mimeType = 'text/plain'): void {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validateYamlKey(key: string): boolean {
  // YAML key validation: alphanumeric, underscore, hyphen
  const keyRegex = /^[a-zA-Z_][a-zA-Z0-9_-]*$/
  return keyRegex.test(key)
}

export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`
  return `${(ms / 3600000).toFixed(1)}h`
}

export function parseDataReference(reference: string): {
  isValid: boolean
  object: string
  path: string[]
} {
  if (!reference.startsWith('data.')) {
    return { isValid: false, object: '', path: [] }
  }
  
  const parts = reference.substring(5).split('.')
  if (parts.length === 0 || parts[0] === '') {
    return { isValid: false, object: '', path: [] }
  }
  
  return {
    isValid: true,
    object: parts[0],
    path: parts.slice(1)
  }
}

export function buildDataReference(object: string, path: string[] = []): string {
  if (path.length === 0) {
    return `data.${object}`
  }
  return `data.${object}.${path.join('.')}`
}

export function getStepTypeColor(type: string): string {
  const colors: Record<string, string> = {
    action: 'bg-primary-50 border-primary-200 text-primary-700',
    script: 'bg-warning-50 border-warning-200 text-warning-700',
    switch: 'bg-purple-50 border-purple-200 text-purple-700',
    for: 'bg-green-50 border-green-200 text-green-700',
    parallel: 'bg-blue-50 border-blue-200 text-blue-700',
    try_catch: 'bg-red-50 border-red-200 text-red-700',
    raise: 'bg-orange-50 border-orange-200 text-orange-700',
    return: 'bg-gray-50 border-gray-200 text-gray-700',
  }
  return colors[type] || colors.action
}

export function getValidationSeverityColor(type: 'error' | 'warning'): string {
  return type === 'error' 
    ? 'text-error-600 bg-error-50 border-error-200'
    : 'text-warning-600 bg-warning-50 border-warning-200'
}
