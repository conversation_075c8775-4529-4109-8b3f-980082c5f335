/**
 * Workflow API Routes
 * Handles workflow creation, validation, and YAML generation
 */

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { YamlGeneratorService } from '../services/yamlGenerator';
import { ValidationService } from '../services/validationService';
import { KnowledgeBaseService } from '../services/knowledgeBase';
import {
  GenerateYamlResponse
} from '../types/moveworks';

const router = Router();
const yamlGenerator = new YamlGeneratorService();
const validator = new ValidationService();
const knowledgeBase = new KnowledgeBaseService();

// Validation schemas
const WorkflowStepSchema = z.object({
  id: z.string(),
  type: z.enum(['action', 'script', 'switch', 'for', 'parallel', 'try_catch', 'raise', 'return']),
  position: z.object({
    x: z.number(),
    y: z.number()
  }),
  config: z.any().default({}),
  connections: z.array(z.string())
});

const WorkflowDefinitionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  input_schema: z.any().optional(),
  steps: z.array(WorkflowStepSchema),
  created_at: z.string().transform(str => new Date(str)),
  updated_at: z.string().transform(str => new Date(str))
});

const GenerateYamlRequestSchema = z.object({
  workflow: WorkflowDefinitionSchema,
  options: z.object({
    format: z.enum(['yaml', 'json']).default('yaml'),
    validate: z.boolean().default(true),
    include_comments: z.boolean().default(false)
  }).default({})
});

/**
 * POST /api/workflow/generate-yaml
 * Generate YAML from workflow definition
 */
router.post('/generate-yaml', async (req: Request, res: Response) => {
  try {
    const requestData = GenerateYamlRequestSchema.parse(req.body);
    const { workflow, options } = requestData;

    // Validate workflow first
    const validationResult = validator.validateWorkflow(workflow);

    if (!validationResult.isValid && options.validate !== false) {
      return res.status(400).json({
        error: 'Workflow validation failed',
        validation: validationResult
      });
    }

    // Generate YAML
    const yamlContent = yamlGenerator.generateYaml(
      workflow,
      options.include_comments || false
    );

    // Validate generated YAML syntax
    const yamlValidation = validator.validateYamlSyntax(yamlContent);

    // Calculate metadata
    const complexityScore = yamlGenerator.calculateComplexityScore(workflow);
    const estimatedExecutionTime = yamlGenerator.estimateExecutionTime(workflow);

    const response: GenerateYamlResponse = {
      yaml: yamlContent,
      validation: {
        isValid: validationResult.isValid && yamlValidation.isValid,
        errors: [...validationResult.errors, ...yamlValidation.errors],
        warnings: [...validationResult.warnings, ...yamlValidation.warnings]
      },
      metadata: {
        step_count: workflow.steps.length,
        complexity_score: complexityScore,
        estimated_execution_time: estimatedExecutionTime
      }
    };

    return res.json(response);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: error.errors
      });
    }

    console.error('Error generating YAML:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/workflow/validate
 * Validate workflow definition
 */
router.post('/validate', async (req: Request, res: Response) => {
  try {
    const workflow = WorkflowDefinitionSchema.parse(req.body);
    const validationResult = validator.validateWorkflow(workflow);

    return res.json(validationResult);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid workflow data',
        details: error.errors
      });
    }

    console.error('Error validating workflow:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/workflow/validate-yaml
 * Validate YAML syntax and structure
 */
router.post('/validate-yaml', async (req: Request, res: Response) => {
  try {
    const { yaml: yamlContent } = req.body;

    if (!yamlContent || typeof yamlContent !== 'string') {
      return res.status(400).json({
        error: 'YAML content is required'
      });
    }

    const validationResult = validator.validateYamlSyntax(yamlContent);
    return res.json(validationResult);

  } catch (error) {
    console.error('Error validating YAML:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/workflow/builtin-actions
 * Get list of Moveworks built-in actions
 */
router.get('/builtin-actions', (_req: Request, res: Response) => {
  try {
    const actions = knowledgeBase.getBuiltinActions();
    return res.json(actions);
  } catch (error) {
    console.error('Error fetching built-in actions:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/workflow/builtin-actions/:name
 * Get specific built-in action details
 */
router.get('/builtin-actions/:name', (req: Request, res: Response) => {
  try {
    const { name } = req.params;
    if (!name) {
      return res.status(400).json({
        error: 'Action name is required'
      });
    }

    const action = knowledgeBase.getBuiltinAction(name);

    if (!action) {
      return res.status(404).json({
        error: 'Built-in action not found'
      });
    }

    return res.json(action);
  } catch (error) {
    console.error('Error fetching built-in action:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/workflow/dsl-functions
 * Get list of DSL functions
 */
router.get('/dsl-functions', (_req: Request, res: Response) => {
  try {
    const functions = knowledgeBase.getDSLFunctions();
    return res.json(functions);
  } catch (error) {
    console.error('Error fetching DSL functions:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/workflow/action-suggestions
 * Get action suggestions based on context
 */
router.post('/action-suggestions', (req: Request, res: Response) => {
  try {
    const { context } = req.body;

    if (!context || typeof context !== 'string') {
      return res.status(400).json({
        error: 'Context is required'
      });
    }

    const suggestions = knowledgeBase.getActionSuggestions(context);
    return res.json(suggestions);

  } catch (error) {
    console.error('Error getting action suggestions:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
