/**
 * Smart Input Component with DSL syntax highlighting and auto-completion
 */

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Info, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DSLFunction {
  name: string
  description: string
  syntax: string
  example: string
  category: 'string' | 'array' | 'object' | 'conditional' | 'utility'
}

const DSL_FUNCTIONS: DSLFunction[] = [
  {
    name: '$CONCAT',
    description: 'Concatenate strings or arrays',
    syntax: '$CONCAT(value1, value2, ...)',
    example: '$CONCAT("Hello ", user_result.display_name, "!")',
    category: 'string'
  },
  {
    name: '$MAP',
    description: 'Transform each element in an array',
    syntax: '$MAP(array, expression)',
    example: '$MAP(users, item.email)',
    category: 'array'
  },
  {
    name: '$FILTER',
    description: 'Filter array elements based on condition',
    syntax: '$FILTER(array, condition)',
    example: '$FILTER(tickets, item.priority == "high")',
    category: 'array'
  },
  {
    name: '$GET',
    description: 'Get value from object with optional default',
    syntax: '$GET(object, path, default)',
    example: '$GET(user_result, "profile.department", "Unknown")',
    category: 'object'
  },
  {
    name: '$IF',
    description: 'Conditional expression',
    syntax: '$IF(condition, true_value, false_value)',
    example: '$IF(data.amount > 1000, "high", "normal")',
    category: 'conditional'
  },
  {
    name: '$LENGTH',
    description: 'Get length of string or array',
    syntax: '$LENGTH(value)',
    example: '$LENGTH(approval_list)',
    category: 'utility'
  }
]

interface SmartInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  label?: string
  description?: string
  dataContext?: Record<string, any>
  expectedType?: 'string' | 'number' | 'boolean' | 'object' | 'array'
  className?: string
}

export default function SmartInput({
  value,
  onChange,
  placeholder,
  label,
  description,
  dataContext = {},
  expectedType,
  className
}: SmartInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [cursorPosition, setCursorPosition] = useState(0)
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    message?: string
    type: 'success' | 'warning' | 'error'
  }>({ isValid: true, type: 'success' })

  const inputRef = useRef<HTMLTextAreaElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Get suggestions based on current input
  const getSuggestions = () => {
    const currentWord = getCurrentWord()
    const suggestions: Array<{
      type: 'dsl' | 'data' | 'template'
      label: string
      description: string
      insertText: string
    }> = []

    // DSL function suggestions
    if (currentWord.startsWith('$') || currentWord === '') {
      DSL_FUNCTIONS.forEach(func => {
        if (func.name.toLowerCase().includes(currentWord.toLowerCase().replace('$', ''))) {
          suggestions.push({
            type: 'dsl',
            label: func.name,
            description: func.description,
            insertText: func.syntax
          })
        }
      })
    }

    // Data context suggestions
    if (currentWord.includes('.') || Object.keys(dataContext).some(key => 
      key.toLowerCase().includes(currentWord.toLowerCase())
    )) {
      Object.keys(dataContext).forEach(key => {
        if (key.toLowerCase().includes(currentWord.toLowerCase())) {
          suggestions.push({
            type: 'data',
            label: key,
            description: `Available data: ${typeof dataContext[key]}`,
            insertText: key
          })
        }
      })
    }

    // Template suggestions based on expected type
    if (expectedType && currentWord === '') {
      const templates = getTemplatesForType(expectedType)
      templates.forEach(template => {
        suggestions.push({
          type: 'template',
          label: template.label,
          description: template.description,
          insertText: template.value
        })
      })
    }

    return suggestions.slice(0, 8) // Limit to 8 suggestions
  }

  const getCurrentWord = () => {
    const text = value.substring(0, cursorPosition)
    const words = text.split(/[\s,()[\]{}]/)
    return words[words.length - 1] || ''
  }

  const getTemplatesForType = (type: string) => {
    const templates = {
      string: [
        { label: 'Simple string', description: 'Plain text value', value: '"example text"' },
        { label: 'Data reference', description: 'Reference to workflow data', value: 'data.field_name' },
        { label: 'Concatenation', description: 'Combine multiple values', value: '$CONCAT("Hello ", data.name)' }
      ],
      object: [
        { label: 'Simple object', description: 'Key-value pairs', value: '{\n  "key": "value"\n}' },
        { label: 'Data mapping', description: 'Map from workflow data', value: '{\n  "field": data.source_field\n}' }
      ],
      array: [
        { label: 'Simple array', description: 'List of values', value: '["item1", "item2"]' },
        { label: 'Data array', description: 'Reference to array data', value: 'data.items' },
        { label: 'Mapped array', description: 'Transform array elements', value: '$MAP(data.items, item.name)' }
      ]
    }
    return templates[type as keyof typeof templates] || []
  }

  // Validate input in real-time
  useEffect(() => {
    const validateInput = () => {
      if (!value.trim()) {
        setValidationResult({ isValid: true, type: 'success' })
        return
      }

      try {
        // Check for DSL syntax
        if (value.includes('$')) {
          const dslPattern = /\$[A-Z_]+\(/g
          const matches = value.match(dslPattern)
          if (matches) {
            const invalidFunctions = matches.filter(match => 
              !DSL_FUNCTIONS.some(func => match.startsWith(func.name + '('))
            )
            if (invalidFunctions.length > 0) {
              setValidationResult({
                isValid: false,
                type: 'error',
                message: `Unknown DSL function: ${invalidFunctions[0].replace('(', '')}`
              })
              return
            }
          }
        }

        // Check for data references
        if (value.includes('data.')) {
          const dataRefs = value.match(/data\.[a-zA-Z_][a-zA-Z0-9_.]*\b/g) || []
          const invalidRefs = dataRefs.filter(ref => {
            const path = ref.replace('data.', '')
            return !hasNestedProperty(dataContext, path)
          })
          if (invalidRefs.length > 0) {
            setValidationResult({
              isValid: false,
              type: 'warning',
              message: `Data reference may not exist: ${invalidRefs[0]}`
            })
            return
          }
        }

        // Try to parse as JSON if it looks like JSON
        if ((value.startsWith('{') && value.endsWith('}')) || 
            (value.startsWith('[') && value.endsWith(']'))) {
          JSON.parse(value)
        }

        setValidationResult({ isValid: true, type: 'success' })
      } catch (error) {
        setValidationResult({
          isValid: false,
          type: 'error',
          message: 'Invalid JSON syntax'
        })
      }
    }

    const timeoutId = setTimeout(validateInput, 300)
    return () => clearTimeout(timeoutId)
  }, [value, dataContext])

  const hasNestedProperty = (obj: any, path: string): boolean => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj) !== undefined
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    const newCursorPosition = e.target.selectionStart
    onChange(newValue)
    setCursorPosition(newCursorPosition)
    setShowSuggestions(newValue.length > 0)
  }

  const handleSuggestionClick = (suggestion: any) => {
    const beforeCursor = value.substring(0, cursorPosition)
    const afterCursor = value.substring(cursorPosition)
    const currentWord = getCurrentWord()
    
    const beforeWord = beforeCursor.substring(0, beforeCursor.length - currentWord.length)
    const newValue = beforeWord + suggestion.insertText + afterCursor
    
    onChange(newValue)
    setShowSuggestions(false)
    
    // Focus back to input
    setTimeout(() => {
      inputRef.current?.focus()
      const newPosition = beforeWord.length + suggestion.insertText.length
      inputRef.current?.setSelectionRange(newPosition, newPosition)
    }, 0)
  }

  const suggestions = showSuggestions ? getSuggestions() : []

  return (
    <div className={cn('relative', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {description && (
            <span className="ml-2 text-xs text-gray-500">
              <Info className="inline h-3 w-3 mr-1" />
              {description}
            </span>
          )}
        </label>
      )}
      
      <div className="relative">
        <textarea
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onFocus={() => setShowSuggestions(value.length > 0)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          placeholder={placeholder}
          className={cn(
            'w-full px-3 py-2 border rounded-md text-sm font-mono resize-none',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent',
            validationResult.isValid 
              ? 'border-gray-300' 
              : validationResult.type === 'error'
                ? 'border-red-300'
                : 'border-yellow-300'
          )}
          rows={value.split('\n').length || 1}
          style={{ minHeight: '40px', maxHeight: '200px' }}
        />
        
        {/* Validation indicator */}
        <div className="absolute right-2 top-2">
          {validationResult.isValid ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className={cn(
              'h-4 w-4',
              validationResult.type === 'error' ? 'text-red-500' : 'text-yellow-500'
            )} />
          )}
        </div>
      </div>

      {/* Validation message */}
      {!validationResult.isValid && validationResult.message && (
        <div className={cn(
          'mt-1 text-xs',
          validationResult.type === 'error' ? 'text-red-600' : 'text-yellow-600'
        )}>
          {validationResult.message}
        </div>
      )}

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      'text-xs px-1.5 py-0.5 rounded text-white font-medium',
                      suggestion.type === 'dsl' ? 'bg-blue-500' :
                      suggestion.type === 'data' ? 'bg-green-500' : 'bg-purple-500'
                    )}>
                      {suggestion.type.toUpperCase()}
                    </span>
                    <span className="font-medium text-sm">{suggestion.label}</span>
                  </div>
                  <div className="text-xs text-gray-600 mt-1">{suggestion.description}</div>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-400 transform -rotate-90" />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
