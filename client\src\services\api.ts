/**
 * API Service
 * Handles communication with the backend API
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { 
  WorkflowDefinition, 
  GenerateYamlRequest, 
  GenerateYamlResponse,
  ValidationResult,
  MoveworksBuiltinAction,
  DSLFunction
} from '@/types'

class ApiService {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL || '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message)
        return Promise.reject(error)
      }
    )
  }

  /**
   * Generate YAML from workflow definition
   */
  async generateYaml(request: GenerateYamlRequest): Promise<GenerateYamlResponse> {
    const response: AxiosResponse<GenerateYamlResponse> = await this.client.post(
      '/workflow/generate-yaml',
      request
    )
    return response.data
  }

  /**
   * Validate workflow definition
   */
  async validateWorkflow(workflow: WorkflowDefinition): Promise<ValidationResult> {
    const response: AxiosResponse<ValidationResult> = await this.client.post(
      '/workflow/validate',
      workflow
    )
    return response.data
  }

  /**
   * Validate YAML syntax and structure
   */
  async validateYaml(yaml: string): Promise<ValidationResult> {
    const response: AxiosResponse<ValidationResult> = await this.client.post(
      '/workflow/validate-yaml',
      { yaml }
    )
    return response.data
  }

  /**
   * Get list of Moveworks built-in actions
   */
  async getBuiltinActions(): Promise<MoveworksBuiltinAction[]> {
    const response: AxiosResponse<MoveworksBuiltinAction[]> = await this.client.get(
      '/workflow/builtin-actions'
    )
    return response.data
  }

  /**
   * Get specific built-in action details
   */
  async getBuiltinAction(name: string): Promise<MoveworksBuiltinAction> {
    const response: AxiosResponse<MoveworksBuiltinAction> = await this.client.get(
      `/workflow/builtin-actions/${encodeURIComponent(name)}`
    )
    return response.data
  }

  /**
   * Get list of DSL functions
   */
  async getDSLFunctions(): Promise<DSLFunction[]> {
    const response: AxiosResponse<DSLFunction[]> = await this.client.get(
      '/workflow/dsl-functions'
    )
    return response.data
  }

  /**
   * Get action suggestions based on context
   */
  async getActionSuggestions(context: string): Promise<MoveworksBuiltinAction[]> {
    const response: AxiosResponse<MoveworksBuiltinAction[]> = await this.client.post(
      '/workflow/action-suggestions',
      { context }
    )
    return response.data
  }

  /**
   * Check API health
   */
  async checkHealth(): Promise<{ status: string; timestamp: string; version: string }> {
    const response = await this.client.get('/health')
    return response.data
  }
}

// Create singleton instance
export const apiService = new ApiService()

// Export individual methods for convenience
export const {
  generateYaml,
  validateWorkflow,
  validateYaml,
  getBuiltinActions,
  getBuiltinAction,
  getDSLFunctions,
  getActionSuggestions,
  checkHealth,
} = apiService

export default apiService
