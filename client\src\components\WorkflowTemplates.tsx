/**
 * Workflow Templates Component
 * Provides pre-built workflow templates for common use cases
 */

import { useState } from 'react'
import { Plus } from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { WorkflowDefinition, WorkflowStep } from '@/types'
import { generateId } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  icon: string
  steps: Omit<WorkflowStep, 'id'>[]
}

const WORKFLOW_TEMPLATES: WorkflowTemplate[] = [
  {
    id: 'user-lookup',
    name: 'User Lookup',
    description: 'Look up a user by email and send a notification',
    category: 'Basic',
    icon: '👤',
    steps: [
      {
        type: 'action',
        position: { x: 100, y: 100 },
        config: {
          action_name: 'mw.get_user_by_email',
          output_key: 'user_result',
          input_args: {
            email: 'data.user_email'
          }
        },
        connections: []
      },
      {
        type: 'action',
        position: { x: 100, y: 220 },
        config: {
          action_name: 'mw.send_plaintext_chat_notification',
          output_key: 'notification_result',
          input_args: {
            user_id: 'user_result.user_id',
            message: '$CONCAT("Hello ", user_result.display_name, "!")'
          }
        },
        connections: []
      }
    ]
  },
  {
    id: 'approval-workflow',
    name: 'Approval Workflow',
    description: 'Create an approval request and check its status',
    category: 'Approval',
    icon: '✅',
    steps: [
      {
        type: 'action',
        position: { x: 100, y: 100 },
        config: {
          action_name: 'mw.create_generic_approval_request',
          output_key: 'approval_request',
          input_args: {
            approver_email: 'data.approver_email',
            request_title: 'data.request_title',
            request_body: 'data.request_body'
          }
        },
        connections: []
      },
      {
        type: 'action',
        position: { x: 100, y: 220 },
        config: {
          action_name: 'mw.get_approval_request_status',
          output_key: 'approval_status',
          input_args: {
            request_id: 'approval_request.request_id'
          }
        },
        connections: []
      }
    ]
  },
  {
    id: 'conditional-processing',
    name: 'Conditional Processing',
    description: 'Process data differently based on conditions',
    category: 'Control Flow',
    icon: '🔀',
    steps: [
      {
        type: 'switch',
        position: { x: 100, y: 100 },
        config: {
          output_key: 'conditional_result',
          cases: [
            {
              condition: 'data.priority == "high"',
              steps: [
                {
                  type: 'action',
                  config: {
                    action_name: 'mw.send_email',
                    output_key: 'urgent_email',
                    input_args: {
                      to: 'data.manager_email',
                      subject: 'Urgent: High Priority Request',
                      body: 'data.message'
                    }
                  }
                }
              ]
            }
          ],
          default: {
            steps: [
              {
                type: 'script',
                config: {
                  output_key: 'normal_processing',
                  code: 'return {"status": "processed_normally"}'
                }
              }
            ]
          }
        },
        connections: []
      }
    ]
  },
  {
    id: 'batch-processing',
    name: 'Batch Processing',
    description: 'Process multiple items with error handling',
    category: 'Advanced',
    icon: '🔄',
    steps: [
      {
        type: 'for',
        position: { x: 100, y: 100 },
        config: {
          output_key: 'batch_results',
          each: 'item',
          index: 'index',
          in: 'data.items',
          steps: [
            {
              type: 'try_catch',
              config: {
                output_key: 'item_result',
                try: {
                  steps: [
                    {
                      type: 'script',
                      config: {
                        output_key: 'processed_item',
                        code: 'return {"item_id": item.id, "status": "success"}'
                      }
                    }
                  ]
                },
                catch: {
                  steps: [
                    {
                      type: 'script',
                      config: {
                        output_key: 'error_result',
                        code: 'return {"item_id": item.id, "status": "error", "error": "Processing failed"}'
                      }
                    }
                  ]
                }
              }
            }
          ]
        },
        connections: []
      }
    ]
  }
]

interface WorkflowTemplatesProps {
  onClose: () => void
}

export default function WorkflowTemplates({ onClose }: WorkflowTemplatesProps) {
  const { createWorkflow } = useWorkflowStore()
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  const categories = ['All', ...Array.from(new Set(WORKFLOW_TEMPLATES.map(t => t.category)))]

  const filteredTemplates = selectedCategory === 'All'
    ? WORKFLOW_TEMPLATES
    : WORKFLOW_TEMPLATES.filter(t => t.category === selectedCategory)

  const handleUseTemplate = (template: WorkflowTemplate) => {
    // Create workflow with template steps
    const workflow: WorkflowDefinition = {
      id: generateId(),
      name: template.name,
      description: template.description,
      steps: template.steps.map(step => ({
        ...step,
        id: generateId()
      })),
      created_at: new Date(),
      updated_at: new Date(),
    }

    // Use the store's createWorkflow but then update with template data
    createWorkflow(template.name, template.description)

    // Update with template steps
    const store = useWorkflowStore.getState()
    if (store.currentWorkflow) {
      store.updateWorkflow({
        steps: workflow.steps
      })
    }

    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-secondary-900">
                Workflow Templates
              </h2>
              <p className="text-secondary-600 mt-1">
                Start with a pre-built template or create from scratch
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              ✕
            </button>
          </div>

          {/* Category Filter */}
          <div className="flex space-x-2 mt-4">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  'px-3 py-1 rounded-full text-sm transition-colors',
                  selectedCategory === category
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-secondary-100 text-secondary-600 hover:bg-secondary-200'
                )}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="p-6 overflow-y-auto max-h-96">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTemplates.map(template => (
              <div
                key={template.id}
                className="border border-secondary-200 rounded-lg p-4 hover:border-primary-300 transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{template.icon}</div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-secondary-900">
                      {template.name}
                    </h3>
                    <p className="text-sm text-secondary-600 mt-1">
                      {template.description}
                    </p>
                    <div className="flex items-center justify-between mt-3">
                      <span className="text-xs bg-secondary-100 text-secondary-600 px-2 py-1 rounded">
                        {template.category}
                      </span>
                      <button
                        onClick={() => handleUseTemplate(template)}
                        className="btn btn-primary btn-sm"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Use Template
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-secondary-200 bg-secondary-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-secondary-600">
              Choose a template to get started quickly, or close to create from scratch.
            </div>
            <button
              onClick={onClose}
              className="btn btn-secondary"
            >
              Create from Scratch
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
