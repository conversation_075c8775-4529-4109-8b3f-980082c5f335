/**
 * Workflow Store
 * Manages workflow state using Zustand
 */

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { 
  WorkflowDefinition, 
  WorkflowStep, 
  ValidationResult,
  MoveworksBuiltinAction,
  DSLFunction,
  GenerateYamlResponse
} from '@/types'
import { generateId } from '@/lib/utils'
import { apiService } from '@/services/api'

interface WorkflowState {
  // Current workflow
  currentWorkflow: WorkflowDefinition | null
  
  // Workflow list
  workflows: WorkflowDefinition[]
  
  // UI state
  selectedStepId: string | null
  isGenerating: boolean
  showValidation: boolean
  sidebarCollapsed: boolean
  yamlPreviewVisible: boolean
  
  // Generated YAML and validation
  generatedYaml: string | null
  validationResult: ValidationResult | null
  yamlMetadata: GenerateYamlResponse['metadata'] | null
  
  // Knowledge base
  builtinActions: MoveworksBuiltinAction[]
  dslFunctions: DSLFunction[]
  
  // Actions
  createWorkflow: (name: string, description?: string) => void
  loadWorkflow: (id: string) => void
  saveWorkflow: () => void
  updateWorkflow: (updates: Partial<WorkflowDefinition>) => void
  deleteWorkflow: (id: string) => void
  
  // Step management
  addStep: (type: WorkflowStep['type'], position: { x: number; y: number }) => string
  updateStep: (id: string, updates: Partial<WorkflowStep>) => void
  deleteStep: (id: string) => void
  selectStep: (id: string | null) => void
  connectSteps: (sourceId: string, targetId: string) => void
  disconnectSteps: (sourceId: string, targetId: string) => void
  
  // YAML generation
  generateYaml: (options?: { include_comments?: boolean }) => Promise<void>
  validateWorkflow: () => Promise<void>
  
  // Knowledge base
  loadBuiltinActions: () => Promise<void>
  loadDSLFunctions: () => Promise<void>
  
  // UI actions
  toggleSidebar: () => void
  toggleYamlPreview: () => void
  toggleValidation: () => void
}

export const useWorkflowStore = create<WorkflowState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentWorkflow: null,
        workflows: [],
        selectedStepId: null,
        isGenerating: false,
        showValidation: false,
        sidebarCollapsed: false,
        yamlPreviewVisible: true,
        generatedYaml: null,
        validationResult: null,
        yamlMetadata: null,
        builtinActions: [],
        dslFunctions: [],

        // Workflow management
        createWorkflow: (name: string, description?: string) => {
          const workflow: WorkflowDefinition = {
            id: generateId(),
            name,
            description,
            steps: [],
            created_at: new Date(),
            updated_at: new Date(),
          }
          
          set((state) => ({
            currentWorkflow: workflow,
            workflows: [...state.workflows, workflow],
            selectedStepId: null,
            generatedYaml: null,
            validationResult: null,
            yamlMetadata: null,
          }))
        },

        loadWorkflow: (id: string) => {
          const workflow = get().workflows.find(w => w.id === id)
          if (workflow) {
            set({
              currentWorkflow: workflow,
              selectedStepId: null,
              generatedYaml: null,
              validationResult: null,
              yamlMetadata: null,
            })
          }
        },

        saveWorkflow: () => {
          const { currentWorkflow, workflows } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
          }))
        },

        updateWorkflow: (updates: Partial<WorkflowDefinition>) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            ...updates,
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
          }))
        },

        deleteWorkflow: (id: string) => {
          set((state) => ({
            workflows: state.workflows.filter(w => w.id !== id),
            currentWorkflow: state.currentWorkflow?.id === id ? null : state.currentWorkflow,
          }))
        },

        // Step management
        addStep: (type: WorkflowStep['type'], position: { x: number; y: number }) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return ''

          const stepId = generateId()
          const newStep: WorkflowStep = {
            id: stepId,
            type,
            position,
            config: {},
            connections: [],
          }

          const updatedWorkflow = {
            ...currentWorkflow,
            steps: [...currentWorkflow.steps, newStep],
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
            selectedStepId: stepId,
          }))

          return stepId
        },

        updateStep: (id: string, updates: Partial<WorkflowStep>) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            steps: currentWorkflow.steps.map(step =>
              step.id === id ? { ...step, ...updates } : step
            ),
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
          }))
        },

        deleteStep: (id: string) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            steps: currentWorkflow.steps.filter(step => step.id !== id),
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
            selectedStepId: state.selectedStepId === id ? null : state.selectedStepId,
          }))
        },

        selectStep: (id: string | null) => {
          set({ selectedStepId: id })
        },

        connectSteps: (sourceId: string, targetId: string) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            steps: currentWorkflow.steps.map(step =>
              step.id === sourceId 
                ? { ...step, connections: [...step.connections, targetId] }
                : step
            ),
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
          }))
        },

        disconnectSteps: (sourceId: string, targetId: string) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          const updatedWorkflow = {
            ...currentWorkflow,
            steps: currentWorkflow.steps.map(step =>
              step.id === sourceId 
                ? { ...step, connections: step.connections.filter(id => id !== targetId) }
                : step
            ),
            updated_at: new Date(),
          }

          set((state) => ({
            currentWorkflow: updatedWorkflow,
            workflows: state.workflows.map(w => 
              w.id === updatedWorkflow.id ? updatedWorkflow : w
            ),
          }))
        },

        // YAML generation
        generateYaml: async (options = {}) => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          set({ isGenerating: true })

          try {
            const response = await apiService.generateYaml({
              workflow: currentWorkflow,
              options: {
                format: 'yaml',
                validate: true,
                include_comments: options.include_comments || false,
              },
            })

            set({
              generatedYaml: response.yaml,
              validationResult: response.validation,
              yamlMetadata: response.metadata,
              isGenerating: false,
            })
          } catch (error) {
            console.error('Failed to generate YAML:', error)
            set({ isGenerating: false })
            throw error
          }
        },

        validateWorkflow: async () => {
          const { currentWorkflow } = get()
          if (!currentWorkflow) return

          try {
            const validationResult = await apiService.validateWorkflow(currentWorkflow)
            set({ validationResult })
          } catch (error) {
            console.error('Failed to validate workflow:', error)
            throw error
          }
        },

        // Knowledge base
        loadBuiltinActions: async () => {
          try {
            const builtinActions = await apiService.getBuiltinActions()
            set({ builtinActions })
          } catch (error) {
            console.error('Failed to load built-in actions:', error)
            throw error
          }
        },

        loadDSLFunctions: async () => {
          try {
            const dslFunctions = await apiService.getDSLFunctions()
            set({ dslFunctions })
          } catch (error) {
            console.error('Failed to load DSL functions:', error)
            throw error
          }
        },

        // UI actions
        toggleSidebar: () => {
          set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }))
        },

        toggleYamlPreview: () => {
          set((state) => ({ yamlPreviewVisible: !state.yamlPreviewVisible }))
        },

        toggleValidation: () => {
          set((state) => ({ showValidation: !state.showValidation }))
        },
      }),
      {
        name: 'workflow-store',
        partialize: (state) => ({
          workflows: state.workflows,
          sidebarCollapsed: state.sidebarCollapsed,
          yamlPreviewVisible: state.yamlPreviewVisible,
        }),
      }
    ),
    { name: 'workflow-store' }
  )
)
