"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.YamlGeneratorService = void 0;
const yaml = __importStar(require("js-yaml"));
class YamlGeneratorService {
    generateYaml(workflow, includeComments = false) {
        const compoundAction = this.convertWorkflowToCompoundAction(workflow);
        if (includeComments) {
            return this.generateYamlWithComments(compoundAction, workflow);
        }
        return yaml.dump(compoundAction, {
            indent: 2,
            lineWidth: 120,
            noRefs: true,
            quotingType: '"',
            forceQuotes: false,
            flowLevel: -1,
            styles: {
                '!!str': 'literal'
            }
        });
    }
    convertWorkflowToCompoundAction(workflow) {
        const orderedSteps = this.orderStepsByDependencies(workflow.steps);
        const expressions = orderedSteps.map(step => this.convertStepToExpression(step));
        return {
            steps: expressions
        };
    }
    orderStepsByDependencies(steps) {
        return steps.sort((a, b) => {
            if (a.position.y !== b.position.y) {
                return a.position.y - b.position.y;
            }
            return a.position.x - b.position.x;
        });
    }
    convertStepToExpression(step) {
        switch (step.type) {
            case 'action':
                return this.createActionExpression(step);
            case 'script':
                return this.createScriptExpression(step);
            case 'switch':
                return this.createSwitchExpression(step);
            case 'for':
                return this.createForExpression(step);
            case 'parallel':
                return this.createParallelExpression(step);
            case 'try_catch':
                return this.createTryCatchExpression(step);
            case 'raise':
                return this.createRaiseExpression(step);
            case 'return':
                return this.createReturnExpression(step);
            default:
                throw new Error(`Unsupported step type: ${step.type}`);
        }
    }
    createActionExpression(step) {
        const config = step.config || {};
        const inputArgs = this.cleanInputArgs(config.input_args || {});
        const actionExpression = {
            action: {
                action_name: config.action_name || '',
                output_key: config.output_key || step.id,
                input_args: inputArgs
            }
        };
        if (config.progress_updates) {
            actionExpression.action.progress_updates = config.progress_updates;
        }
        if (config.delay_config) {
            actionExpression.action.delay_config = config.delay_config;
        }
        return actionExpression;
    }
    createScriptExpression(step) {
        const config = step.config || {};
        const inputArgs = this.cleanInputArgs(config.input_args || {});
        return {
            script: {
                output_key: config.output_key || step.id,
                input_args: inputArgs,
                code: config.code || '# APIthon script\n# Last line is the return value\nreturn {}'
            }
        };
    }
    createSwitchExpression(step) {
        const config = step.config || {};
        const switchExpression = {
            switch: {
                output_key: config.output_key || step.id,
                cases: (config.cases || []).map((caseConfig) => ({
                    condition: caseConfig.condition || 'true',
                    steps: (caseConfig.steps || []).map((stepConfig) => this.convertStepToExpression(stepConfig))
                }))
            }
        };
        if (config.default && config.default.steps) {
            switchExpression.switch.default = {
                steps: config.default.steps.map((stepConfig) => this.convertStepToExpression(stepConfig))
            };
        }
        return switchExpression;
    }
    createForExpression(step) {
        const config = step.config || {};
        return {
            for: {
                output_key: config.output_key || step.id,
                each: config.each || 'item',
                index: config.index || 'index',
                in: config.in || 'data.items',
                steps: (config.steps || []).map((stepConfig) => this.convertStepToExpression(stepConfig))
            }
        };
    }
    createParallelExpression(step) {
        const config = step.config || {};
        if (config.for) {
            return {
                parallel: {
                    output_key: config.output_key || step.id,
                    for: {
                        each: config.for.each || 'item',
                        index: config.for.index || 'index',
                        in: config.for.in || 'data.items',
                        steps: (config.for.steps || []).map((stepConfig) => this.convertStepToExpression(stepConfig))
                    }
                }
            };
        }
        else {
            return {
                parallel: {
                    output_key: config.output_key || step.id,
                    branches: (config.branches || []).map((branchConfig) => this.convertStepToExpression(branchConfig))
                }
            };
        }
    }
    createTryCatchExpression(step) {
        const config = step.config || {};
        const tryCatchExpression = {
            try_catch: {
                output_key: config.output_key || step.id,
                try: {
                    steps: (config.try?.steps || []).map((stepConfig) => this.convertStepToExpression(stepConfig))
                },
                catch: {
                    steps: (config.catch?.steps || []).map((stepConfig) => this.convertStepToExpression(stepConfig))
                }
            }
        };
        if (config.catch?.on_status_code) {
            tryCatchExpression.try_catch.catch.on_status_code = config.catch.on_status_code;
        }
        return tryCatchExpression;
    }
    createRaiseExpression(step) {
        const config = step.config || {};
        return {
            raise: {
                output_key: config.output_key || step.id,
                message: config.message || 'An error occurred'
            }
        };
    }
    createReturnExpression(step) {
        const config = step.config || {};
        const returnExpression = {
            return: {
                output_key: config.output_key || step.id
            }
        };
        if (config.value !== undefined) {
            returnExpression.return.value = config.value;
        }
        return returnExpression;
    }
    cleanInputArgs(inputArgs) {
        const cleaned = {};
        Object.entries(inputArgs).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                cleaned[key] = value;
            }
        });
        return cleaned;
    }
    generateYamlWithComments(compoundAction, workflow) {
        let yamlContent = `# Moveworks Compound Action: ${workflow.name}\n`;
        if (workflow.description) {
            yamlContent += `# Description: ${workflow.description}\n`;
        }
        yamlContent += `# Generated by Moveworks Compound Action Assistant\n`;
        yamlContent += `# Created: ${new Date().toISOString()}\n\n`;
        const mainYaml = yaml.dump(compoundAction, {
            indent: 2,
            lineWidth: 120,
            noRefs: true,
            quotingType: '"',
            forceQuotes: false
        });
        yamlContent += mainYaml;
        return yamlContent;
    }
    calculateComplexityScore(workflow) {
        let score = 0;
        workflow.steps.forEach(step => {
            switch (step.type) {
                case 'action':
                case 'script':
                    score += 1;
                    break;
                case 'switch':
                    score += 3;
                    break;
                case 'for':
                    score += 4;
                    break;
                case 'parallel':
                    score += 5;
                    break;
                case 'try_catch':
                    score += 2;
                    break;
                default:
                    score += 1;
            }
        });
        return score;
    }
    estimateExecutionTime(workflow) {
        let estimatedMs = 0;
        workflow.steps.forEach(step => {
            switch (step.type) {
                case 'action':
                    estimatedMs += 300;
                    break;
                case 'script':
                    estimatedMs += 150;
                    break;
                case 'for':
                    estimatedMs += 300 * 5;
                    break;
                case 'parallel':
                    estimatedMs += 50;
                    break;
                default:
                    estimatedMs += 100;
            }
        });
        return estimatedMs;
    }
}
exports.YamlGeneratorService = YamlGeneratorService;
//# sourceMappingURL=yamlGenerator.js.map