/**
 * Workflow Testing and Simulation Component
 * Allows users to test workflows with sample data
 */

import { useState } from 'react'
import { Play, Save, Upload, Download, AlertCircle, CheckCircle, Clock } from 'lucide-react'
import { WorkflowDefinition } from '@/types'
import SmartInput from './SmartInput'
import { cn } from '@/lib/utils'

interface TestScenario {
  id: string
  name: string
  description: string
  inputData: Record<string, any>
  expectedOutput?: Record<string, any>
  lastRun?: {
    timestamp: Date
    success: boolean
    output: any
    executionTime: number
    errors?: string[]
  }
}

interface WorkflowTesterProps {
  workflow: WorkflowDefinition
  onClose: () => void
}

export default function WorkflowTester({ workflow, onClose }: WorkflowTesterProps) {
  const [scenarios, setScenarios] = useState<TestScenario[]>([
    {
      id: 'default',
      name: 'Default Test',
      description: 'Basic test scenario',
      inputData: {}
    }
  ])
  const [selectedScenarioId, setSelectedScenarioId] = useState('default')
  const [isRunning, setIsRunning] = useState(false)
  const [showResults, setShowResults] = useState(false)

  const selectedScenario = scenarios.find(s => s.id === selectedScenarioId)

  const handleInputDataChange = (newData: string) => {
    if (!selectedScenario) return

    try {
      const parsedData = JSON.parse(newData)
      setScenarios(prev => prev.map(scenario => 
        scenario.id === selectedScenarioId 
          ? { ...scenario, inputData: parsedData }
          : scenario
      ))
    } catch (error) {
      // Handle JSON parsing errors gracefully
      console.warn('Invalid JSON input:', error)
    }
  }

  const simulateWorkflowExecution = async (inputData: Record<string, any>) => {
    // Simulate workflow execution
    const startTime = Date.now()
    
    // Mock execution logic
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    const executionTime = Date.now() - startTime
    const success = Math.random() > 0.2 // 80% success rate for demo

    if (success) {
      // Generate mock output based on workflow steps
      const output: Record<string, any> = { ...inputData }
      
      workflow.steps.forEach(step => {
        if (step.config?.output_key) {
          switch (step.type) {
            case 'action':
              if (step.config.action_name?.includes('get_user')) {
                output[step.config.output_key] = {
                  user_id: 'user_123',
                  email: inputData.email || '<EMAIL>',
                  display_name: 'Test User',
                  department: 'Engineering'
                }
              } else if (step.config.action_name?.includes('notification')) {
                output[step.config.output_key] = {
                  success: true,
                  message_id: 'msg_' + Math.random().toString(36).substr(2, 9)
                }
              } else if (step.config.action_name?.includes('approval')) {
                output[step.config.output_key] = {
                  request_id: 'req_' + Math.random().toString(36).substr(2, 9),
                  status: 'pending',
                  created_at: new Date().toISOString()
                }
              }
              break
            case 'script':
              output[step.config.output_key] = {
                result: 'Script executed successfully',
                data: inputData
              }
              break
            case 'switch':
              output[step.config.output_key] = {
                condition_result: Math.random() > 0.5,
                branch_taken: 'true_branch'
              }
              break
          }
        }
      })

      return { success: true, output, executionTime, errors: [] }
    } else {
      return {
        success: false,
        output: null,
        executionTime,
        errors: ['Simulated execution error', 'Network timeout']
      }
    }
  }

  const handleRunTest = async () => {
    if (!selectedScenario) return

    setIsRunning(true)
    setShowResults(true)

    try {
      const result = await simulateWorkflowExecution(selectedScenario.inputData)
      
      setScenarios(prev => prev.map(scenario =>
        scenario.id === selectedScenarioId
          ? {
              ...scenario,
              lastRun: {
                timestamp: new Date(),
                ...result
              }
            }
          : scenario
      ))
    } catch (error) {
      console.error('Test execution failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const handleSaveScenario = () => {
    const name = prompt('Enter scenario name:')
    if (!name) return

    const newScenario: TestScenario = {
      id: Date.now().toString(),
      name,
      description: 'Custom test scenario',
      inputData: {}
    }

    setScenarios(prev => [...prev, newScenario])
    setSelectedScenarioId(newScenario.id)
  }

  const handleExportScenarios = () => {
    const dataStr = JSON.stringify(scenarios, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${workflow.name}_test_scenarios.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleImportScenarios = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedScenarios = JSON.parse(e.target?.result as string)
        setScenarios(prev => [...prev, ...importedScenarios])
      } catch (error) {
        alert('Failed to import scenarios: Invalid JSON format')
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Test Workflow: {workflow.name}
              </h2>
              <p className="text-gray-600 mt-1">
                Simulate workflow execution with test data
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="flex h-[70vh]">
          {/* Test Configuration */}
          <div className="w-1/2 p-6 border-r border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-900">Test Scenarios</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleSaveScenario}
                  className="btn btn-ghost btn-sm"
                  title="Save new scenario"
                >
                  <Save className="h-4 w-4" />
                </button>
                <label className="btn btn-ghost btn-sm cursor-pointer" title="Import scenarios">
                  <Upload className="h-4 w-4" />
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportScenarios}
                    className="hidden"
                  />
                </label>
                <button
                  onClick={handleExportScenarios}
                  className="btn btn-ghost btn-sm"
                  title="Export scenarios"
                >
                  <Download className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Scenario Selection */}
            <div className="mb-4">
              <select
                value={selectedScenarioId}
                onChange={(e) => setSelectedScenarioId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {scenarios.map(scenario => (
                  <option key={scenario.id} value={scenario.id}>
                    {scenario.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Input Data Configuration */}
            {selectedScenario && (
              <div className="space-y-4">
                <SmartInput
                  label="Input Data (JSON)"
                  description="Test data to pass to the workflow"
                  value={JSON.stringify(selectedScenario.inputData, null, 2)}
                  onChange={handleInputDataChange}
                  expectedType="object"
                  className="h-64"
                />

                <button
                  onClick={handleRunTest}
                  disabled={isRunning}
                  className="w-full btn btn-primary"
                >
                  {isRunning ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Run Test
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Test Results */}
          <div className="w-1/2 p-6">
            <h3 className="font-medium text-gray-900 mb-4">Test Results</h3>
            
            {selectedScenario?.lastRun ? (
              <div className="space-y-4">
                {/* Execution Summary */}
                <div className={cn(
                  'p-4 rounded-lg border',
                  selectedScenario.lastRun.success
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                )}>
                  <div className="flex items-center space-x-2 mb-2">
                    {selectedScenario.lastRun.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    )}
                    <span className={cn(
                      'font-medium',
                      selectedScenario.lastRun.success ? 'text-green-900' : 'text-red-900'
                    )}>
                      {selectedScenario.lastRun.success ? 'Test Passed' : 'Test Failed'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>Executed: {selectedScenario.lastRun.timestamp.toLocaleString()}</p>
                    <p>Duration: {selectedScenario.lastRun.executionTime}ms</p>
                  </div>
                </div>

                {/* Errors */}
                {selectedScenario.lastRun.errors && selectedScenario.lastRun.errors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="font-medium text-red-900 mb-2">Errors</h4>
                    <ul className="text-sm text-red-700 space-y-1">
                      {selectedScenario.lastRun.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Output */}
                {selectedScenario.lastRun.output && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Output</h4>
                    <pre className="text-sm text-gray-700 overflow-auto max-h-64">
                      {JSON.stringify(selectedScenario.lastRun.output, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ) : showResults && isRunning ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <Clock className="h-8 w-8 mx-auto mb-2 animate-spin text-primary-600" />
                  <p className="text-gray-600">Running test...</p>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-32 text-gray-500">
                <div className="text-center">
                  <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Run a test to see results</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Test your workflow with different input scenarios to ensure it works as expected.
            </div>
            <button
              onClick={onClose}
              className="btn btn-secondary"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
