/**
 * Context-Aware Step Configuration Component
 * Provides intelligent suggestions and data context awareness
 */

import { useState, useEffect } from 'react'
import { Lightbulb, Database, Plus } from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { WorkflowStep } from '@/types'
import SmartInput from './SmartInput'
import { cn } from '@/lib/utils'

interface ActionSuggestion {
  action: string
  reason: string
  priority: 'high' | 'medium' | 'low'
  category: 'prerequisite' | 'follow-up' | 'enhancement'
}

interface DataContextItem {
  key: string
  type: string
  source: string
  description: string
  available: boolean
}

interface ContextAwareStepConfigProps {
  step: WorkflowStep
  onUpdate: (updates: Partial<WorkflowStep>) => void
}

export default function ContextAwareStepConfig({ step, onUpdate }: ContextAwareStepConfigProps) {
  const { currentWorkflow } = useWorkflowStore()
  const [suggestions, setSuggestions] = useState<ActionSuggestion[]>([])
  const [dataContext, setDataContext] = useState<DataContextItem[]>([])
  const [showSuggestions, setShowSuggestions] = useState(true)
  const [showDataContext, setShowDataContext] = useState(true)

  // Analyze workflow context and generate suggestions
  useEffect(() => {
    if (!currentWorkflow || !step) return

    const newSuggestions = generateActionSuggestions(step, currentWorkflow.steps)
    const newDataContext = analyzeDataContext(currentWorkflow.steps, step)

    setSuggestions(newSuggestions)
    setDataContext(newDataContext)
  }, [step, currentWorkflow])

  const generateActionSuggestions = (currentStep: WorkflowStep, allSteps: WorkflowStep[]): ActionSuggestion[] => {
    const suggestions: ActionSuggestion[] = []
    const currentStepIndex = allSteps.findIndex(s => s.id === currentStep.id)
    const previousSteps = allSteps.slice(0, currentStepIndex)

    // Analyze current step configuration
    if (currentStep.type === 'action' && currentStep.config?.action_name) {
      const actionName = currentStep.config.action_name

      // Suggestions for notification actions
      if (actionName.includes('notification') || actionName.includes('message')) {
        const hasUserLookup = previousSteps.some(step =>
          step.config?.action_name?.includes('get_user')
        )

        if (!hasUserLookup) {
          suggestions.push({
            action: 'mw.get_user_by_email',
            reason: 'User lookup is typically needed before sending notifications',
            priority: 'high',
            category: 'prerequisite'
          })
        }

        // Check if user_id is properly referenced
        const inputArgs = currentStep.config.input_args || {}
        if (!inputArgs.user_id && !inputArgs.user_record_id) {
          suggestions.push({
            action: 'Add user_id reference',
            reason: 'Notification actions require a user_id parameter',
            priority: 'high',
            category: 'enhancement'
          })
        }
      }

      // Suggestions for approval actions
      if (actionName.includes('approval')) {
        suggestions.push({
          action: 'mw.get_approval_request_status',
          reason: 'Consider checking approval status after creating request',
          priority: 'medium',
          category: 'follow-up'
        })
      }

      // Suggestions for email actions
      if (actionName.includes('email')) {
        const hasUserLookup = previousSteps.some(step =>
          step.config?.action_name?.includes('get_user')
        )

        if (!hasUserLookup) {
          suggestions.push({
            action: 'mw.get_user_by_email',
            reason: 'User lookup can provide email address and user details',
            priority: 'medium',
            category: 'prerequisite'
          })
        }
      }

      // Error handling suggestions
      if (!allSteps.some(step => step.type === 'try_catch')) {
        suggestions.push({
          action: 'Add error handling',
          reason: 'Consider wrapping API calls in try/catch blocks',
          priority: 'low',
          category: 'enhancement'
        })
      }
    }

    return suggestions
  }

  const analyzeDataContext = (allSteps: WorkflowStep[], currentStep: WorkflowStep): DataContextItem[] => {
    const context: DataContextItem[] = []
    const currentStepIndex = allSteps.findIndex(s => s.id === currentStep.id)
    const previousSteps = allSteps.slice(0, currentStepIndex)

    // Add workflow input data
    context.push({
      key: 'data',
      type: 'object',
      source: 'Workflow Input',
      description: 'Input data passed to the workflow',
      available: true
    })

    // Add outputs from previous steps
    previousSteps.forEach(step => {
      if (step.config?.output_key) {
        const outputKey = step.config.output_key
        let description = `Output from ${step.type} step`
        let type = 'object'

        // Provide more specific descriptions based on action type
        if (step.type === 'action' && step.config.action_name) {
          const actionName = step.config.action_name
          if (actionName.includes('get_user')) {
            description = 'User object with id, email, display_name, etc.'
            type = 'User'
          } else if (actionName.includes('approval')) {
            description = 'Approval request object with status, id, etc.'
            type = 'ApprovalRequest'
          } else if (actionName.includes('ticket')) {
            description = 'Ticket object with id, status, description, etc.'
            type = 'Ticket'
          }
        }

        context.push({
          key: outputKey,
          type,
          source: `Step: ${step.config.action_name || step.type}`,
          description,
          available: true
        })
      }
    })

    return context
  }

  const handleConfigChange = (field: string, value: any) => {
    onUpdate({
      config: {
        ...step.config,
        [field]: value
      }
    })
  }

  const handleInputArgChange = (argName: string, value: any) => {
    const currentInputArgs = step.config?.input_args || {}
    handleConfigChange('input_args', {
      ...currentInputArgs,
      [argName]: value
    })
  }

  const applySuggestion = (suggestion: ActionSuggestion) => {
    if (suggestion.action.startsWith('mw.')) {
      // Add a new step with the suggested action
      // This would integrate with the workflow store to add a new step
      console.log('Would add new step:', suggestion.action)
    } else if (suggestion.action === 'Add user_id reference') {
      handleInputArgChange('user_id', 'user_result.user_id')
    }
  }

  // Get data context as object for SmartInput
  const getDataContextObject = () => {
    const contextObj: Record<string, any> = {}
    dataContext.forEach(item => {
      if (item.available) {
        contextObj[item.key] = { type: item.type, description: item.description }
      }
    })
    return contextObj
  }

  return (
    <div className="space-y-6">
      {/* Action Suggestions */}
      {suggestions.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-blue-900">Smart Suggestions</h4>
            </div>
            <button
              onClick={() => setShowSuggestions(!showSuggestions)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              {showSuggestions ? 'Hide' : 'Show'}
            </button>
          </div>

          {showSuggestions && (
            <div className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="flex items-start justify-between p-3 bg-white rounded border border-blue-100"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={cn(
                        'text-xs px-2 py-1 rounded font-medium',
                        suggestion.priority === 'high' ? 'bg-red-100 text-red-700' :
                        suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-gray-100 text-gray-700'
                      )}>
                        {suggestion.priority.toUpperCase()}
                      </span>
                      <span className={cn(
                        'text-xs px-2 py-1 rounded',
                        suggestion.category === 'prerequisite' ? 'bg-orange-100 text-orange-700' :
                        suggestion.category === 'follow-up' ? 'bg-green-100 text-green-700' :
                        'bg-purple-100 text-purple-700'
                      )}>
                        {suggestion.category}
                      </span>
                    </div>
                    <div className="font-medium text-sm text-gray-900 mb-1">
                      {suggestion.action}
                    </div>
                    <div className="text-xs text-gray-600">
                      {suggestion.reason}
                    </div>
                  </div>
                  <button
                    onClick={() => applySuggestion(suggestion)}
                    className="ml-3 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                  >
                    Apply
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Data Context */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Database className="h-4 w-4 text-green-600" />
            <h4 className="font-medium text-green-900">Available Data Context</h4>
          </div>
          <button
            onClick={() => setShowDataContext(!showDataContext)}
            className="text-green-600 hover:text-green-800 text-sm"
          >
            {showDataContext ? 'Hide' : 'Show'}
          </button>
        </div>

        {showDataContext && (
          <div className="space-y-2">
            {dataContext.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 bg-white rounded border border-green-100"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                      {item.key}
                    </code>
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                      {item.type}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {item.description} • From: {item.source}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Step Configuration */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Step Configuration</h4>

        {/* Output Key */}
        <SmartInput
          label="Output Key"
          description="Variable name to store the result"
          value={step.config?.output_key || ''}
          onChange={(value) => handleConfigChange('output_key', value)}
          placeholder={`${step.type}_result`}
          expectedType="string"
          dataContext={getDataContextObject()}
        />

        {/* Action-specific configuration */}
        {step.type === 'action' && (
          <>
            <SmartInput
              label="Action Name"
              description="Moveworks built-in action to execute"
              value={step.config?.action_name || ''}
              onChange={(value) => handleConfigChange('action_name', value)}
              placeholder="mw.get_user_by_email"
              expectedType="string"
              dataContext={getDataContextObject()}
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Input Arguments
              </label>
              <div className="space-y-3">
                {Object.entries(step.config?.input_args || {}).map(([key, value]) => (
                  <div key={key} className="flex items-start space-x-2">
                    <div className="flex-1">
                      <SmartInput
                        label={key}
                        value={typeof value === 'string' ? value : JSON.stringify(value)}
                        onChange={(newValue) => handleInputArgChange(key, newValue)}
                        dataContext={getDataContextObject()}
                        expectedType="string"
                      />
                    </div>
                    <button
                      onClick={() => {
                        const newArgs = { ...step.config?.input_args }
                        delete newArgs[key]
                        handleConfigChange('input_args', newArgs)
                      }}
                      className="mt-6 p-1 text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  </div>
                ))}

                <button
                  onClick={() => {
                    const newKey = prompt('Enter argument name:')
                    if (newKey) {
                      handleInputArgChange(newKey, '')
                    }
                  }}
                  className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Argument</span>
                </button>
              </div>
            </div>
          </>
        )}

        {/* Script-specific configuration */}
        {step.type === 'script' && (
          <SmartInput
            label="APIthon Code"
            description="Python-like code to execute"
            value={step.config?.code || ''}
            onChange={(value) => handleConfigChange('code', value)}
            placeholder="# APIthon script&#10;return {'result': 'success'}"
            expectedType="string"
            dataContext={getDataContextObject()}
          />
        )}

        {/* Switch-specific configuration */}
        {step.type === 'switch' && (
          <SmartInput
            label="Condition"
            description="Boolean expression to evaluate"
            value={step.config?.condition || ''}
            onChange={(value) => handleConfigChange('condition', value)}
            placeholder="data.amount > 1000"
            expectedType="string"
            dataContext={getDataContextObject()}
          />
        )}
      </div>
    </div>
  )
}
