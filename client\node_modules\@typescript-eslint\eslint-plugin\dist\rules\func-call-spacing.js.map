{"version": 3, "file": "func-call-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/func-call-spacing.ts"], "names": [], "mappings": ";;AACA,wEAAsE;AAEtE,kCAMiB;AAajB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,iCAAiC,CAAC;QAC/C,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,gFAAgF;YAClF,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE;YACN,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;4BACd,IAAI,EAAE,CAAC,OAAO,CAAC;yBAChB;qBACF;oBACD,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,CAAC;iBACZ;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;4BACd,IAAI,EAAE,CAAC,QAAQ,CAAC;yBACjB;wBACD;4BACE,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,aAAa,EAAE;oCACb,IAAI,EAAE,SAAS;iCAChB;6BACF;4BACD,oBAAoB,EAAE,KAAK;yBAC5B;qBACF;oBACD,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,CAAC;iBACZ;aACF;SACF;QAED,QAAQ,EAAE;YACR,oBAAoB,EAClB,wDAAwD;YAC1D,iBAAiB,EAAE,qDAAqD;YACxE,OAAO,EAAE,gDAAgD;SAC1D;KACF;IACD,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;IAC7B,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QAC9B,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;QAElC;;;;WAIG;QACH,SAAS,YAAY,CACnB,IAAsD;YAEtD,MAAM,cAAc,GAAG,IAAA,+BAAwB,EAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAE,CAAC;YACzD,MAAM,oCAAoC,GAAG,UAAU,CAAC,YAAY,CAClE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CACjC,CAAC;YACH,MAAM,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CACvD,oCAAoC,EACpC,iBAAiB,EACjB,0BAAmB,CACpB,CAAC;YACF,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtE,mCAAmC;gBACnC,OAAO;YACT,CAAC;YACD,MAAM,eAAe,GAAG,UAAU,CAAC,cAAc,CAC/C,iBAAiB,EACjB,mCAA4B,CAC5B,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI;iBAC3B,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC3D,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YAChC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpD,MAAM,UAAU,GACd,aAAa,IAAI,wBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE7D,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;gBACvB,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC9B,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP;;;+BAGG;4BACH,IACE,CAAC,UAAU;gCACX,2BAA2B;gCAC3B,CAAC,cAAc,EACf,CAAC;gCACD,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;oCACxB,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;iCAC3B,CAAC,CAAC;4BACL,CAAC;4BAED,OAAO,IAAI,CAAC;wBACd,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,EAAE,CAAC;gBAC1B,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,IAAI,aAAa,IAAI,UAAU,EAAE,CAAC;oBAChC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC9B,SAAS,EAAE,sBAAsB;qBAClC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC9B,SAAS,EAAE,SAAS;wBACpB,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;wBACxD,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,CAAC,MAAO,CAAC,aAAa,IAAI,UAAU,EAAE,CAAC;oBAChD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC9B,SAAS,EAAE,mBAAmB;wBAC9B,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,GAAG,CACJ,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,cAAc,EAAE,YAAY;YAC5B,aAAa,EAAE,YAAY;SAC5B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}