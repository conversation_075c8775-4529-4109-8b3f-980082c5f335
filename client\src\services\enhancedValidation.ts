/**
 * Enhanced Validation Service
 * Provides real-time validation with actionable feedback
 */

import { WorkflowDefinition, WorkflowStep } from '@/types'

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  suggestions: ValidationSuggestion[]
}

export interface ValidationError {
  id: string
  stepId?: string
  field?: string
  message: string
  severity: 'error' | 'warning'
  fixable: boolean
  autoFix?: () => void
}

export interface ValidationWarning {
  id: string
  stepId?: string
  message: string
  impact: 'performance' | 'maintainability' | 'best-practice'
}

export interface ValidationSuggestion {
  id: string
  stepId?: string
  message: string
  action: string
  apply?: () => void
}

// Built-in actions with their parameter requirements
const BUILTIN_ACTIONS = {
  'mw.get_user_by_email': {
    required: ['email'],
    optional: [],
    returns: 'User object with user_id, display_name, email, etc.'
  },
  'mw.get_user_by_id': {
    required: ['user_id'],
    optional: [],
    returns: 'User object with user_id, display_name, email, etc.'
  },
  'mw.send_plaintext_chat_notification': {
    required: ['user_id', 'message'],
    optional: ['thread_id'],
    returns: 'Notification result with success status'
  },
  'mw.send_email': {
    required: ['to', 'subject', 'body'],
    optional: ['cc', 'bcc', 'attachments'],
    returns: 'Email result with message_id'
  },
  'mw.create_generic_approval_request': {
    required: ['approver_email', 'request_title', 'request_body'],
    optional: ['due_date', 'priority'],
    returns: 'Approval request with request_id'
  },
  'mw.get_approval_request_status': {
    required: ['request_id'],
    optional: [],
    returns: 'Approval status object'
  },
  'mw.create_ticket': {
    required: ['title', 'description'],
    optional: ['priority', 'assignee', 'labels'],
    returns: 'Ticket object with ticket_id'
  },
  'mw.get_ticket_by_id': {
    required: ['ticket_id'],
    optional: [],
    returns: 'Ticket object with details'
  }
}

// DSL functions with their syntax validation
const DSL_FUNCTIONS = {
  '$CONCAT': {
    minArgs: 2,
    maxArgs: Infinity,
    description: 'Concatenate strings or arrays'
  },
  '$MAP': {
    minArgs: 2,
    maxArgs: 2,
    description: 'Transform each element in an array'
  },
  '$FILTER': {
    minArgs: 2,
    maxArgs: 2,
    description: 'Filter array elements based on condition'
  },
  '$GET': {
    minArgs: 2,
    maxArgs: 3,
    description: 'Get value from object with optional default'
  },
  '$IF': {
    minArgs: 3,
    maxArgs: 3,
    description: 'Conditional expression'
  },
  '$LENGTH': {
    minArgs: 1,
    maxArgs: 1,
    description: 'Get length of string or array'
  },
  '$UPPER': {
    minArgs: 1,
    maxArgs: 1,
    description: 'Convert string to uppercase'
  },
  '$LOWER': {
    minArgs: 1,
    maxArgs: 1,
    description: 'Convert string to lowercase'
  }
}

export class EnhancedValidator {
  validateWorkflow(workflow: WorkflowDefinition): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []
    const suggestions: ValidationSuggestion[] = []

    // Validate workflow structure
    this.validateWorkflowStructure(workflow, errors, warnings)

    // Validate each step
    workflow.steps.forEach(step => {
      this.validateStep(step, workflow, errors, warnings, suggestions)
    })

    // Validate data flow
    this.validateDataFlow(workflow, errors, warnings, suggestions)

    // Check for best practices
    this.checkBestPractices(workflow, warnings, suggestions)

    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errors,
      warnings,
      suggestions
    }
  }

  private validateWorkflowStructure(
    workflow: WorkflowDefinition,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ) {
    if (!workflow.name?.trim()) {
      errors.push({
        id: 'missing-name',
        message: 'Workflow must have a name',
        severity: 'error',
        fixable: true
      })
    }

    if (!workflow.steps || workflow.steps.length === 0) {
      errors.push({
        id: 'no-steps',
        message: 'Workflow must contain at least one step',
        severity: 'error',
        fixable: false
      })
    }

    if (workflow.steps && workflow.steps.length > 50) {
      warnings.push({
        id: 'too-many-steps',
        message: 'Workflow has many steps, consider breaking into smaller workflows',
        impact: 'maintainability'
      })
    }
  }

  private validateStep(
    step: WorkflowStep,
    workflow: WorkflowDefinition,
    errors: ValidationError[],
    warnings: ValidationWarning[],
    suggestions: ValidationSuggestion[]
  ) {
    // Validate step structure
    if (!step.id) {
      errors.push({
        id: `step-no-id-${Math.random()}`,
        stepId: step.id,
        message: 'Step must have an ID',
        severity: 'error',
        fixable: true
      })
    }

    if (!step.type) {
      errors.push({
        id: `step-no-type-${step.id}`,
        stepId: step.id,
        message: 'Step must have a type',
        severity: 'error',
        fixable: false
      })
      return
    }

    // Validate based on step type
    switch (step.type) {
      case 'action':
        this.validateActionStep(step, errors, warnings, suggestions)
        break
      case 'script':
        this.validateScriptStep(step, errors, warnings)
        break
      case 'switch':
        this.validateSwitchStep(step, workflow, errors, warnings)
        break
      case 'for':
        this.validateForStep(step, errors, warnings)
        break
    }

    // Validate output key
    if (step.config?.output_key) {
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(step.config.output_key)) {
        errors.push({
          id: `invalid-output-key-${step.id}`,
          stepId: step.id,
          field: 'output_key',
          message: 'Output key must be a valid variable name',
          severity: 'error',
          fixable: true
        })
      }

      // Check for duplicate output keys
      const duplicates = workflow.steps.filter(s =>
        s.id !== step.id && s.config?.output_key === step.config.output_key
      )
      if (duplicates.length > 0) {
        errors.push({
          id: `duplicate-output-key-${step.id}`,
          stepId: step.id,
          field: 'output_key',
          message: `Output key '${step.config.output_key}' is used by another step`,
          severity: 'error',
          fixable: true
        })
      }
    }
  }

  private validateActionStep(
    step: WorkflowStep,
    errors: ValidationError[],
    warnings: ValidationWarning[],
    suggestions: ValidationSuggestion[]
  ) {
    const actionName = step.config?.action_name
    if (!actionName) {
      errors.push({
        id: `action-no-name-${step.id}`,
        stepId: step.id,
        field: 'action_name',
        message: 'Action step must specify an action name',
        severity: 'error',
        fixable: false
      })
      return
    }

    // Validate against known actions
    const actionSpec = BUILTIN_ACTIONS[actionName as keyof typeof BUILTIN_ACTIONS]
    if (!actionSpec) {
      warnings.push({
        id: `unknown-action-${step.id}`,
        stepId: step.id,
        message: `Unknown action '${actionName}'. This may be a custom action.`,
        impact: 'best-practice'
      })
      return
    }

    // Validate required parameters
    const inputArgs = step.config?.input_args || {}
    const missingRequired = actionSpec.required.filter(param => !inputArgs[param])

    missingRequired.forEach(param => {
      errors.push({
        id: `missing-param-${step.id}-${param}`,
        stepId: step.id,
        field: 'input_args',
        message: `Required parameter '${param}' is missing for action '${actionName}'`,
        severity: 'error',
        fixable: true
      })
    })

    // Validate parameter values
    Object.entries(inputArgs).forEach(([param, value]) => {
      this.validateParameterValue(step.id, param, value, errors, warnings)
    })

    // Suggest improvements
    if (actionName.includes('notification') && !inputArgs.user_id && !inputArgs.user_record_id) {
      suggestions.push({
        id: `suggest-user-lookup-${step.id}`,
        stepId: step.id,
        message: 'Consider adding a user lookup step before sending notifications',
        action: 'Add user lookup step'
      })
    }
  }

  private validateParameterValue(
    stepId: string,
    param: string,
    value: any,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ) {
    if (typeof value === 'string') {
      // Validate DSL expressions
      if (value.includes('$')) {
        this.validateDSLExpressions(stepId, param, value, errors, warnings)
      }

      // Validate data references
      if (value.includes('data.')) {
        this.validateDataReferences(stepId, param, value, warnings)
      }
    }
  }

  private validateDSLExpressions(
    stepId: string,
    param: string,
    value: string,
    errors: ValidationError[],
    _warnings: ValidationWarning[]
  ) {
    const dslPattern = /\$([A-Z_]+)\s*\(/g
    let match

    while ((match = dslPattern.exec(value)) !== null) {
      const funcName = `$${match[1]}`
      const funcSpec = DSL_FUNCTIONS[funcName as keyof typeof DSL_FUNCTIONS]

      if (!funcSpec) {
        errors.push({
          id: `unknown-dsl-${stepId}-${param}`,
          stepId,
          field: param,
          message: `Unknown DSL function '${funcName}'`,
          severity: 'error',
          fixable: false
        })
      } else {
        // Basic argument count validation (simplified)
        const argsMatch = value.match(new RegExp(`\\${funcName}\\s*\\(([^)]*)\\)`))
        if (argsMatch) {
          const argCount = argsMatch[1].split(',').filter(arg => arg.trim()).length
          if (argCount < funcSpec.minArgs) {
            errors.push({
              id: `dsl-too-few-args-${stepId}-${param}`,
              stepId,
              field: param,
              message: `${funcName} requires at least ${funcSpec.minArgs} arguments`,
              severity: 'error',
              fixable: false
            })
          }
          if (funcSpec.maxArgs !== Infinity && argCount > funcSpec.maxArgs) {
            errors.push({
              id: `dsl-too-many-args-${stepId}-${param}`,
              stepId,
              field: param,
              message: `${funcName} accepts at most ${funcSpec.maxArgs} arguments`,
              severity: 'error',
              fixable: false
            })
          }
        }
      }
    }
  }

  private validateDataReferences(
    stepId: string,
    param: string,
    value: string,
    warnings: ValidationWarning[]
  ) {
    const dataRefs = value.match(/data\.[a-zA-Z_][a-zA-Z0-9_.]*\b/g) || []

    dataRefs.forEach(ref => {
      // This is a simplified check - in a real implementation,
      // you'd validate against the actual workflow input schema
      if (ref.split('.').length > 4) {
        warnings.push({
          id: `deep-data-ref-${stepId}-${param}`,
          stepId,
          message: `Deep data reference '${ref}' may be fragile`,
          impact: 'maintainability'
        })
      }
    })
  }

  private validateScriptStep(
    step: WorkflowStep,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ) {
    const code = step.config?.code
    if (!code?.trim()) {
      errors.push({
        id: `script-no-code-${step.id}`,
        stepId: step.id,
        field: 'code',
        message: 'Script step must contain code',
        severity: 'error',
        fixable: false
      })
      return
    }

    // Basic syntax checks
    if (!code.includes('return')) {
      warnings.push({
        id: `script-no-return-${step.id}`,
        stepId: step.id,
        message: 'Script should typically return a value',
        impact: 'best-practice'
      })
    }
  }

  private validateSwitchStep(
    step: WorkflowStep,
    _workflow: WorkflowDefinition,
    errors: ValidationError[],
    _warnings: ValidationWarning[]
  ) {
    const condition = step.config?.condition
    if (!condition?.trim()) {
      errors.push({
        id: `switch-no-condition-${step.id}`,
        stepId: step.id,
        field: 'condition',
        message: 'Switch step must have a condition',
        severity: 'error',
        fixable: false
      })
    }
  }

  private validateForStep(
    step: WorkflowStep,
    errors: ValidationError[],
    _warnings: ValidationWarning[]
  ) {
    const config = step.config
    if (!config?.in) {
      errors.push({
        id: `for-no-array-${step.id}`,
        stepId: step.id,
        field: 'in',
        message: 'For loop must specify an array to iterate over',
        severity: 'error',
        fixable: false
      })
    }

    if (!config?.each) {
      // Note: This is a best practice warning but currently unused
      // warnings.push({
      //   id: `for-no-item-var-${step.id}`,
      //   stepId: step.id,
      //   message: 'For loop should specify an item variable name',
      //   impact: 'best-practice'
      // })
    }
  }

  private validateDataFlow(
    workflow: WorkflowDefinition,
    errors: ValidationError[],
    _warnings: ValidationWarning[],
    _suggestions: ValidationSuggestion[]
  ) {
    const availableData = new Set(['data'])

    workflow.steps.forEach(step => {
      // Check if step references unavailable data
      const stepStr = JSON.stringify(step.config)
      const dataRefs = stepStr.match(/[a-zA-Z_][a-zA-Z0-9_]*(?=\.)/g) || []

      dataRefs.forEach(ref => {
        if (ref !== 'data' && !availableData.has(ref)) {
          errors.push({
            id: `undefined-reference-${step.id}-${ref}`,
            stepId: step.id,
            message: `Reference to undefined variable '${ref}'`,
            severity: 'error',
            fixable: false
          })
        }
      })

      // Add this step's output to available data
      if (step.config?.output_key) {
        availableData.add(step.config.output_key)
      }
    })
  }

  private checkBestPractices(
    workflow: WorkflowDefinition,
    warnings: ValidationWarning[],
    suggestions: ValidationSuggestion[]
  ) {
    // Check for error handling
    const hasErrorHandling = workflow.steps.some(step => step.type === 'try_catch')
    if (!hasErrorHandling && workflow.steps.some(step => step.type === 'action')) {
      suggestions.push({
        id: 'add-error-handling',
        message: 'Consider adding error handling for API calls',
        action: 'Add try/catch blocks'
      })
    }

    // Check for long workflows
    if (workflow.steps.length > 20) {
      suggestions.push({
        id: 'break-up-workflow',
        message: 'Consider breaking this workflow into smaller, reusable components',
        action: 'Refactor workflow'
      })
    }

    // Check for unused outputs
    const outputKeys = new Set(workflow.steps.map(s => s.config?.output_key).filter(Boolean))
    const usedKeys = new Set()

    workflow.steps.forEach(step => {
      const stepStr = JSON.stringify(step.config)
      outputKeys.forEach(key => {
        if (stepStr.includes(key!)) {
          usedKeys.add(key)
        }
      })
    })

    outputKeys.forEach(key => {
      if (key && !usedKeys.has(key)) {
        warnings.push({
          id: `unused-output-${key}`,
          message: `Output '${key}' is never used`,
          impact: 'maintainability'
        })
      }
    })
  }
}

export const enhancedValidator = new EnhancedValidator()
