{"version": 3, "file": "no-unnecessary-type-arguments.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-arguments.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAKiB;AAejB,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,uDAAuD;YACpE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,wBAAwB,EACtB,0EAA0E;SAC7E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,oBAAoB,CAAC,IAAa;YAIzC,IAAI,IAAA,0BAAmB,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,MAAM;oBACjB,aAAa,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC;iBAC9C,CAAC;YACJ,CAAC;YACD,OAAO;gBACL,IAAI;gBACJ,aAAa,EAAE,EAAE;aAClB,CAAC;QACJ,CAAC;QAED,SAAS,wBAAwB,CAC/B,YAAmD,EACnD,cAAsD;YAEtD,+FAA+F;YAC/F,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,KAAK,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;gBACpB,OAAO;YACT,CAAC;YAED,oGAAoG;YACpG,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAChD,uFAAuF;YACvF,IAAI,WAAW,KAAK,OAAO,EAAE,CAAC;gBAC5B,2FAA2F;gBAC3F,0FAA0F;gBAC1F,gDAAgD;gBAChD,iFAAiF;gBACjF,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBAC9D,MAAM,eAAe,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACtD;gBACE,+DAA+D;gBAC/D,mBAAmB,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;oBACjD,mBAAmB,CAAC,aAAa,CAAC,MAAM;wBACtC,eAAe,CAAC,aAAa,CAAC,MAAM;oBACtC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CACpC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CACjD,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,0BAA0B;gBACrC,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,WAAW,CACf,CAAC,KAAK,CAAC;oBACL,CAAC,CAAC,YAAY,CAAC,KAAK;oBACpB,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACxD;aACJ,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,4BAA4B,CAAC,IAAI;gBAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAE5D,MAAM,cAAc,GAAG,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACtE,IAAI,cAAc,EAAE,CAAC;oBACnB,wBAAwB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,yBAAyB,CAChC,IAA4B,EAC5B,OAAuB;IAEvB,IAAI,EAAE,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,OAAO,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1D,OAAO,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB,CAChC,IAAyD,EACzD,OAAuB;IAEvB,MAAM,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACxD,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,YAAY,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IAE3C,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,IAAA,sBAAe,EAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAC1C,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;QACpB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC;QAC/B,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,IAAI,CAAC,cAAc;QACrB,CAAC,CAAC,SAAS,CACd,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAChC,IAA0C,EAC1C,OAAuB;IAEvB,MAAM,GAAG,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,GAAG,EAAE,cAAc,EAAE,CAAC;IACtC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7B,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;IAED,OAAO,OAAO,CAAC,cAAc,CAAC;AAChC,CAAC;AAED,SAAS,gBAAgB,CACvB,MAAiB,EACjB,OAAuB;IAEvB,OAAO,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;QAC1D,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAClC,CAAC,CAAC,MAAM,CAAC;AACb,CAAC"}