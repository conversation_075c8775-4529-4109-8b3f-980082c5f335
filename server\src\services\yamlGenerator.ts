/**
 * YAML Generation Service
 * Converts workflow definitions to valid Moveworks Compound Action YAML
 */

import * as yaml from 'js-yaml';
import {
  WorkflowDefinition,
  WorkflowStep,
  CompoundAction,
  Expression,
  ActionExpression,
  ScriptExpression
} from '../types/moveworks';

export class YamlGeneratorService {
  /**
   * Generate YAML from workflow definition
   */
  public generateYaml(workflow: WorkflowDefinition, includeComments = false): string {
    const compoundAction = this.convertWorkflowToCompoundAction(workflow);

    if (includeComments) {
      return this.generateYamlWithComments(compoundAction, workflow);
    }

    return yaml.dump(compoundAction, {
      indent: 2,
      lineWidth: 120,
      noRefs: true,
      quotingType: '"',
      forceQuotes: false
    });
  }

  /**
   * Convert workflow definition to compound action structure
   */
  private convertWorkflowToCompoundAction(workflow: WorkflowDefinition): CompoundAction {
    // Sort steps by their connections to maintain proper execution order
    const orderedSteps = this.orderStepsByDependencies(workflow.steps);

    const expressions: Expression[] = orderedSteps.map(step =>
      this.convertStepToExpression(step)
    );

    return {
      steps: expressions
    };
  }

  /**
   * Order steps based on their dependencies (connections)
   */
  private orderStepsByDependencies(steps: WorkflowStep[]): WorkflowStep[] {
    // For Phase 1, we'll use simple sequential ordering
    // In later phases, we'll implement proper dependency resolution
    return steps.sort((a, b) => {
      // Sort by position for now (left to right, top to bottom)
      if (a.position.y !== b.position.y) {
        return a.position.y - b.position.y;
      }
      return a.position.x - b.position.x;
    });
  }

  /**
   * Convert a workflow step to a Moveworks expression
   */
  private convertStepToExpression(step: WorkflowStep): Expression {
    switch (step.type) {
      case 'action':
        return this.createActionExpression(step);
      case 'script':
        return this.createScriptExpression(step);
      default:
        throw new Error(`Unsupported step type: ${step.type}`);
    }
  }

  /**
   * Create action expression from step configuration
   */
  private createActionExpression(step: WorkflowStep): ActionExpression {
    const config = step.config;

    return {
      action: {
        action_name: config.action_name || '',
        output_key: config.output_key || step.id,
        input_args: config.input_args || {},
        ...(config.progress_updates && { progress_updates: config.progress_updates }),
        ...(config.delay_config && { delay_config: config.delay_config })
      }
    };
  }

  /**
   * Create script expression from step configuration
   */
  private createScriptExpression(step: WorkflowStep): ScriptExpression {
    const config = step.config;

    return {
      script: {
        output_key: config.output_key || step.id,
        input_args: config.input_args || {},
        code: config.code || '# APIthon script\nreturn {}'
      }
    };
  }

  /**
   * Generate YAML with helpful comments
   */
  private generateYamlWithComments(compoundAction: CompoundAction, workflow: WorkflowDefinition): string {
    let yamlContent = `# Moveworks Compound Action: ${workflow.name}\n`;

    if (workflow.description) {
      yamlContent += `# Description: ${workflow.description}\n`;
    }

    yamlContent += `# Generated by Moveworks Compound Action Assistant\n`;
    yamlContent += `# Created: ${new Date().toISOString()}\n\n`;

    // Add the main YAML content
    const mainYaml = yaml.dump(compoundAction, {
      indent: 2,
      lineWidth: 120,
      noRefs: true,
      quotingType: '"',
      forceQuotes: false
    });

    yamlContent += mainYaml;

    return yamlContent;
  }

  /**
   * Calculate workflow complexity score
   */
  public calculateComplexityScore(workflow: WorkflowDefinition): number {
    let score = 0;

    workflow.steps.forEach(step => {
      switch (step.type) {
        case 'action':
        case 'script':
          score += 1;
          break;
        case 'switch':
          score += 3; // Conditional logic adds complexity
          break;
        case 'for':
          score += 4; // Loops add significant complexity
          break;
        case 'parallel':
          score += 5; // Parallel execution is most complex
          break;
        case 'try_catch':
          score += 2; // Error handling adds moderate complexity
          break;
        default:
          score += 1;
      }
    });

    return score;
  }

  /**
   * Estimate execution time based on workflow structure
   */
  public estimateExecutionTime(workflow: WorkflowDefinition): number {
    let estimatedMs = 0;

    workflow.steps.forEach(step => {
      switch (step.type) {
        case 'action':
          // HTTP actions typically take 100-500ms
          estimatedMs += 300;
          break;
        case 'script':
          // APIthon scripts add latency
          estimatedMs += 150;
          break;
        case 'for':
          // Estimate based on potential iterations (assume 5 avg)
          estimatedMs += 300 * 5;
          break;
        case 'parallel':
          // Parallel steps don't add to total time
          estimatedMs += 50; // Just coordination overhead
          break;
        default:
          estimatedMs += 100;
      }
    });

    return estimatedMs;
  }
}
