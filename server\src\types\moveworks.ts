/**
 * Core types for Moveworks Compound Action YAML generation
 * Based on "Generating Verified Moveworks Compound Action YAML for AI Application Development"
 */

// Base interfaces for all expression types
export interface BaseExpression {
  output_key?: string; // Can be "_" if output not needed
}

// Action expression - for HTTP actions and built-in actions
export interface ActionExpression extends BaseExpression {
  action: {
    action_name: string; // e.g., "mw.get_user_by_email" or custom connector action
    output_key?: string;
    input_args: Record<string, any>;
    progress_updates?: boolean;
    delay_config?: {
      delay_seconds: number;
      max_retries?: number;
    };
  };
}

// Script expression - for APIthon scripts
export interface ScriptExpression extends BaseExpression {
  script: {
    output_key?: string;
    input_args: Record<string, any>;
    code: string; // APIthon code
  };
}

// Switch expression - for conditional logic
export interface SwitchExpression extends BaseExpression {
  switch: {
    output_key?: string;
    cases: Array<{
      condition: string; // Boolean expression
      steps: Expression[];
    }>;
    default?: {
      steps: Expression[];
    };
  };
}

// For loop expression - for iteration
export interface ForExpression extends BaseExpression {
  for: {
    output_key?: string;
    each: string; // Variable name for current item
    index?: string; // Variable name for current index
    in: string; // Array reference (e.g., "data.items")
    steps: Expression[];
  };
}

// Parallel expression - for concurrent execution
export interface ParallelExpression extends BaseExpression {
  parallel: {
    output_key?: string;
    branches: Expression[]; // List of expressions to run in parallel
  } | {
    output_key?: string;
    for: {
      each: string;
      index?: string;
      in: string;
      steps: Expression[];
    };
  };
}

// Try-catch expression - for error handling
export interface TryCatchExpression extends BaseExpression {
  try_catch: {
    output_key?: string;
    try: {
      steps: Expression[];
    };
    catch: {
      on_status_code?: number[]; // Specific status codes to catch
      steps: Expression[];
    };
  };
}

// Raise expression - for throwing errors
export interface RaiseExpression extends BaseExpression {
  raise: {
    output_key?: string;
    message: string;
  };
}

// Return expression - for early workflow termination
export interface ReturnExpression extends BaseExpression {
  return: {
    output_key?: string;
    value?: any;
  };
}

// Union type for all possible expressions
export type Expression =
  | ActionExpression
  | ScriptExpression
  | SwitchExpression
  | ForExpression
  | ParallelExpression
  | TryCatchExpression
  | RaiseExpression
  | ReturnExpression;

// Root compound action structure
export interface CompoundAction {
  steps: Expression[];
}

// Data context objects available in Moveworks
export interface DataContext {
  data: Record<string, any>; // Input variables and step outputs
  requestor: {
    email: string;
    user_id: string;
    display_name: string;
    department?: string;
    location?: string;
  };
  mw: Record<string, any>; // Moveworks namespace
  error_data?: {
    failed_step_output_key: {
      error: {
        message: string;
        status_code?: number;
      };
    };
  };
}

// Built-in Moveworks actions (mw. namespace)
export interface MoveworksBuiltinAction {
  name: string;
  description: string;
  input_args: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  output_structure: Record<string, any>;
}

// Validation result
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  type: 'syntax' | 'structure' | 'reference' | 'type';
  message: string;
  path?: string | undefined;
  line?: number | undefined;
  column?: number | undefined;
}

export interface ValidationWarning {
  type: 'best_practice' | 'performance' | 'maintainability';
  message: string;
  path?: string;
  suggestion?: string;
}

// Workflow definition for the visual builder
export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  input_schema?: Record<string, any>; // JSON schema for expected inputs
  steps: WorkflowStep[];
  created_at: Date;
  updated_at: Date;
}

export interface WorkflowStep {
  id: string;
  type: 'action' | 'script' | 'switch' | 'for' | 'parallel' | 'try_catch' | 'raise' | 'return';
  position: { x: number; y: number };
  config?: any; // Type-specific configuration
  connections: string[]; // IDs of connected steps
}

// API request/response types
export interface GenerateYamlRequest {
  workflow: WorkflowDefinition;
  options?: {
    format?: 'yaml' | 'json';
    validate?: boolean;
    include_comments?: boolean;
  };
}

export interface GenerateYamlResponse {
  yaml: string;
  validation: ValidationResult;
  metadata: {
    step_count: number;
    complexity_score: number;
    estimated_execution_time?: number;
  };
}
