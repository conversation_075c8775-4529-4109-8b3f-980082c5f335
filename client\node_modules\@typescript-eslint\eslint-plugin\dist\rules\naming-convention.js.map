{"version": 3, "file": "naming-convention.js", "sourceRoot": "", "sources": ["../../src/rules/naming-convention.ts"], "names": [], "mappings": ";;AAAA,oEAAkE;AAElE,oDAAoE;AACpE,wEAAiE;AAGjE,kCAKiB;AAMjB,uEAA4E;AAe5E,qDAAqD;AACrD,sHAAsH;AACtH,MAAM,kCAAkC,GAAY;IAClD;QACE,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,CAAC,WAAW,CAAC;QACrB,iBAAiB,EAAE,OAAO;QAC1B,kBAAkB,EAAE,OAAO;KAC5B;IAED;QACE,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;KACpC;IAED;QACE,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;QACnC,iBAAiB,EAAE,OAAO;QAC1B,kBAAkB,EAAE,OAAO;KAC5B;IAED;QACE,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,CAAC,YAAY,CAAC;KACvB;CACF,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,4EAA4E;YAC5E,oBAAoB,EAAE,IAAI;SAC3B;QACD,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE;YACR,oBAAoB,EAClB,mEAAmE;YACrE,iBAAiB,EACf,0EAA0E;YAC5E,YAAY,EACV,qFAAqF;YACvF,aAAa,EACX,oEAAoE;YACtE,kBAAkB,EAChB,+EAA+E;YACjF,yBAAyB,EACvB,8GAA8G;SACjH;QACD,MAAM,EAAE,gCAAM;KACf;IACD,cAAc,EAAE,kCAAkC;IAClD,MAAM,CAAC,sBAAsB;QAC3B,MAAM,OAAO,GACX,sBAAsB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YACvC,CAAC,CAAC,sBAAsB;YACxB,CAAC,CAAC,2DAA2D;gBAC1D,MAAM,CAAC,cAAc,CACpB;oBACE,OAAO,EAAE,kCAAkC;iBAC5C,EACD,sBAAsB,CACX,CAAC;QAEpB,MAAM,UAAU,GAAG,IAAA,sCAAY,EAAC,OAAO,CAAC,CAAC;QAEzC,MAAM,eAAe,GACnB,IAAA,wBAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACvE,SAAS,YAAY,CACnB,SAA4B,EAC5B,IAO+C,EAC/C,SAAyB;YAEzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YACrB,IAAI,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;YAED,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC5B,CAAC;QAED,SAAS,kBAAkB,CACzB,IAKgC;YAEhC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;YACvC,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBACxE,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC9B,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;YAClC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;YAClC,CAAC;YACD,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,4BAA4B;gBACzD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B,EACvD,CAAC;gBACD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,eAAe,GAAG,IAAA,6BAAsB,EAAC,OAAO,CAAC,CAAC;QACxD,SAAS,QAAQ,CACf,IAAY,EACZ,eAA4C,IAAA,uBAAQ,EAAC,OAAO,CAAC;YAE7D,IAAI,QAAQ,GAAmC,IAAI,CAAC;YACpD,IAAI,KAAK,GAAgC,YAAY,CAAC;YACtD,OAAO,KAAK,EAAE,CAAC;gBACb,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;gBACvC,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM;gBACR,CAAC;gBACD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACtB,CAAC;YACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,cAAc,CAAC,EAAuB;YAC7C,OAAO;YACL,gBAAgB;YAChB,kCAAkC;YAClC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnE,oBAAoB;gBACpB,sCAAsC;gBACtC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBAClD,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;oBACjD,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAC9B,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAC9B,oBAM6C;YAE7C,OAAO,OAAO,CACZ,OAAO,IAAI,oBAAoB;gBAC7B,oBAAoB,CAAC,KAAK;gBAC1B,OAAO,IAAI,oBAAoB,CAAC,KAAK;gBACrC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CACnC,CAAC;QACJ,CAAC;QAED,SAAS,yBAAyB,CAAC,EAAuB;YACxD,OAAO,OAAO,CACZ,CAAC,OAAO,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;gBACvC,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;oBAClB,EAAE,CAAC,MAAM,CAAC,IAAI;oBACd,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI;oBACzB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAC1B,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAQX;YACF,iBAAiB;YAEjB,mEAAmE,EAAE;gBACnE,SAAS,EAAE,UAAU,CAAC,MAAM;gBAC5B,OAAO,EAAE,CACP,IAG4B,EAC5B,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBAEvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;wBAClB,KAAK,sBAAc,CAAC,sBAAsB;4BACxC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,OAAO,CAAC,CAAC;4BACjC,MAAM;wBACR,KAAK,sBAAc,CAAC,wBAAwB;4BAC1C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,SAAS,CAAC,CAAC;4BACnC,MAAM;wBACR,KAAK,sBAAc,CAAC,eAAe;4BACjC,qCAAqC;4BACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gCACrC,OAAO;4BACT,CAAC;4BACD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,OAAO,CAAC,CAAC;4BACjC,MAAM;oBACV,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACnC,CAAC;aACF;YAED,aAAa;YAEb,mBAAmB;YAEnB,kBAAkB,EAAE;gBAClB,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,WAAW,GAAG,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAEvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAa,CAAC;oBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC3B,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;wBACvD,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BAC5B,aAAa,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;wBACrC,CAAC;wBAED,IAAI,QAAQ,CAAC,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC,EAAE,CAAC;4BAChC,aAAa,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAED,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBACvB,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;wBAEzC,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;4BACvB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,YAAY,CAAC,CAAC;wBACxC,CAAC;wBAED,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC,EAAE,CAAC;4BACnD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;wBACpC,CAAC;wBAED,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;4BACtB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;wBAClC,CAAC;wBAED,IAAI,yBAAyB,CAAC,EAAE,CAAC,EAAE,CAAC;4BAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;wBACjC,CAAC;wBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAED,aAAa;YAEb,mBAAmB;YAEnB,4DAA4D,EAAE;gBAC5D,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CACP,IAG8B,EAC9B,SAAS,EACH,EAAE;oBACR,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;wBACpB,OAAO;oBACT,CAAC;oBAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,0CAA0C;oBAC1C,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC,KAAK,CAAC;oBAEtC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;wBACpB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAC1C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;wBACf,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,sBAAsB;YAEtB,oBAAoB;YACpB,oHAAoH,EAClH;gBACE,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CACP,IAK0C,EAC1C,SAAS,EACH,EAAE;oBACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;4BACtD,OAAO;wBACT,CAAC;wBAED,MAAM,WAAW,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;wBAErD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;4BACtB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;4BAEvC,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gCACtB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,YAAY,CAAC,CAAC;4BACxC,CAAC;4BAED,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gCACrB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;4BAClC,CAAC;4BAED,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAEH,uBAAuB;YAEvB,4BAA4B;YAE5B,mBAAmB,EAAE;gBACnB,SAAS,EAAE,UAAU,CAAC,iBAAiB;gBACvC,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAE3C,MAAM,WAAW,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAE9D,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;wBACtB,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;gBACL,CAAC;aACF;YAED,+BAA+B;YAE/B,mBAAmB;YAEnB,6LAA6L,EAC3L;gBACE,SAAS,EAAE,UAAU,CAAC,qBAAqB;gBAC3C,OAAO,EAAE,CACP,IAAsC,EACtC,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,0MAA0M,EACxM;gBACE,SAAS,EAAE,UAAU,CAAC,aAAa;gBACnC,OAAO,EAAE,CACP,IAEwD,EACxD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAC3C,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,+FAA+F,EAC7F;gBACE,SAAS,EAAE,UAAU,CAAC,YAAY;gBAClC,OAAO,EAAE,CACP,IAAiD,EACjD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,sBAAsB;YAEtB,iBAAiB;YAEjB,CAAC;gBACC,mFAAmF;gBACnF,8EAA8E;gBAC9E,yFAAyF;aAC1F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,mBAAmB;gBACzC,OAAO,EAAE,CACP,IAE6C,EAC7C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBAEzD,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,CAAC;gBACC,sHAAsH;gBACtH,iHAAiH;gBACjH,4HAA4H;gBAC5H,2FAA2F;aAC5F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,WAAW;gBACjC,OAAO,EAAE,CACP,IAIwD,EACxD,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAE3C,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;oBAED,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,CAAC;gBACC,qCAAqC;gBACrC,8FAA8F;aAC/F,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACb,SAAS,EAAE,UAAU,CAAC,UAAU;gBAChC,OAAO,EAAE,CACP,IAE+C,EAC/C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,oBAAoB;YAEpB,mBAAmB;YAEnB,oEAAoE,EAAE;gBACpE,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CAAC,IAAsC,EAAE,SAAS,EAAQ,EAAE;oBACnE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAY,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAED,4EAA4E,EAC1E;gBACE,SAAS,EAAE,UAAU,CAAC,QAAQ;gBAC9B,OAAO,EAAE,CACP,IAA8C,EAC9C,SAAS,EACH,EAAE;oBACR,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAC3C,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;aACF;YAEH,sBAAsB;YAEtB,qBAAqB;YAErB,uDAAuD;YACvD,gCAAgC,EAAE;gBAChC,SAAS,EAAE,UAAU,CAAC,UAAU;gBAChC,OAAO,EAAE,CACP,IAA0C,EAC1C,SAAS,EACH,EAAE;oBACR,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACnB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBAEvC,IAAI,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;wBAChD,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,cAAc,CAAC,CAAC;oBAC1C,CAAC;oBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,wBAAwB;YAExB,gBAAgB;YAEhB,mCAAmC,EAAE;gBACnC,SAAS,EAAE,UAAU,CAAC,KAAK;gBAC3B,OAAO,EAAE,CACP,IAA0D,EAC1D,SAAS,EACH,EAAE;oBACR,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACnB,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;wBACf,OAAO;oBACT,CAAC;oBAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,wCAAwC;oBACxC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC,KAAK,CAAC;oBAEtC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACrC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAC7B,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACF;YAED,mBAAmB;YAEnB,oBAAoB;YAEpB,sBAAsB,EAAE;gBACtB,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;oBAEhC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAC1C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,uBAAuB;YAEvB,oBAAoB;YAEpB,sBAAsB,EAAE;gBACtB,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;oBAEhC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAC1C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,uBAAuB;YAEvB,eAAe;YAEf,iBAAiB,EAAE;gBACjB,SAAS,EAAE,UAAU,CAAC,IAAI;gBAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,SAAS,EAAQ,EAAE;oBACjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,sCAAsC;oBACtC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC,KAAK,CAAC;oBAEtC,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAC1C,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,QAAQ,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBAClC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;gBAChC,CAAC;aACF;YAED,kBAAkB;YAElB,wBAAwB;YAExB,8CAA8C,EAAE;gBAC9C,SAAS,EAAE,UAAU,CAAC,aAAa;gBACnC,OAAO,EAAE,CAAC,IAA8B,EAAE,SAAS,EAAQ,EAAE;oBAC3D,MAAM,SAAS,GAAG,IAAI,GAAG,EAAa,CAAC;oBACvC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;oBAEhC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACpC,SAAS,CAAC,GAAG,CAAC,mCAAS,CAAC,MAAM,CAAC,CAAC;oBAClC,CAAC;oBAED,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAClC,CAAC;aACF;YAED,2BAA2B;SAC5B,CAAC;QAEF,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE;YACnE,OAAO;gBACL,QAAQ;gBACR,CAAC,IAAmC,EAAQ,EAAE;oBAC5C,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3B,CAAC;aACO,CAAC;QACb,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,yBAAyB,CAChC,OAAsC;IAEtC,MAAM,WAAW,GAA0B,EAAE,CAAC;IAC9C,MAAM,OAAO,GAAG,IAAI,8BAAc,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACvB,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,UAAU,CACjB,IAA+B,EAC/B,IAAY,EACZ,KAAkC;IAElC,IACE,IAAI,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QAC9D,IAAI,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAC5D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,QAAQ,EAAE,CAAC;QACb,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC;YACxC,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;gBAC1D,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EACjD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,QAAQ,CAAC,KAAkC;IAClD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;QAC9C,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAC/C,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,IAAyE,EACzE,MAAgC;IAEhC,MAAM,IAAI,GACR,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QACvC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC5C,CAAC,CAAC,IAAI,CAAC,IAAI;QACX,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,OAAO,IAAA,sBAAgB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC"}