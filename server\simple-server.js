const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Basic middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Simple server is running'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'Simple Test Server',
    message: 'Server is working correctly'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📖 Health check: http://localhost:${PORT}/health`);
});
