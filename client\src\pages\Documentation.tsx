import { useState, useEffect } from 'react'
import { ExternalLink, Search } from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { cn } from '@/lib/utils'

export default function Documentation() {
  const { builtinActions, dslFunctions, loadBuiltinActions, loadDSLFunctions } = useWorkflowStore()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [activeTab, setActiveTab] = useState('actions')

  useEffect(() => {
    loadBuiltinActions()
    loadDSLFunctions()
  }, [loadBuiltinActions, loadDSLFunctions])

  const filteredActions = builtinActions.filter(action =>
    action.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    action.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredFunctions = dslFunctions.filter(func =>
    func.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    func.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (selectedCategory === 'all' || func.category === selectedCategory)
  )

  const categories = ['all', ...new Set(dslFunctions.map(f => f.category))]

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">Documentation</h1>
        <p className="text-secondary-600">
          Comprehensive guide to Moveworks Compound Actions, built-in actions, and DSL functions.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
          <input
            type="text"
            placeholder="Search documentation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>
        {activeTab === 'dsl' && (
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="input w-auto"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-secondary-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('actions')}
            className={cn(
              "py-2 px-1 border-b-2 font-medium text-sm",
              activeTab === 'actions'
                ? "border-primary-500 text-primary-600"
                : "border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"
            )}
          >
            Built-in Actions ({builtinActions.length})
          </button>
          <button
            onClick={() => setActiveTab('dsl')}
            className={cn(
              "py-2 px-1 border-b-2 font-medium text-sm",
              activeTab === 'dsl'
                ? "border-primary-500 text-primary-600"
                : "border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"
            )}
          >
            DSL Functions ({dslFunctions.length})
          </button>
          <button
            onClick={() => setActiveTab('guide')}
            className={cn(
              "py-2 px-1 border-b-2 font-medium text-sm",
              activeTab === 'guide'
                ? "border-primary-500 text-primary-600"
                : "border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"
            )}
          >
            User Guide
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'actions' && (
        <div className="space-y-4">
          {filteredActions.map((action) => (
            <div key={action.name} className="card">
              <div className="card-content">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-secondary-900">
                    {action.name}
                  </h3>
                  <span className="badge badge-default">mw</span>
                </div>
                <p className="text-secondary-600 mb-4">{action.description}</p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Input Arguments</h4>
                    <div className="space-y-2">
                      {action.input_args.map((arg) => (
                        <div key={arg.name} className="flex items-start space-x-3 text-sm">
                          <code className="bg-secondary-100 px-2 py-1 rounded text-secondary-800 font-mono">
                            {arg.name}
                          </code>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-secondary-600">{arg.type}</span>
                              {arg.required && (
                                <span className="badge badge-error text-xs">required</span>
                              )}
                            </div>
                            <p className="text-secondary-600 mt-1">{arg.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Output Structure</h4>
                    <pre className="bg-secondary-50 p-3 rounded text-sm overflow-x-auto">
                      <code>{JSON.stringify(action.output_structure, null, 2)}</code>
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'dsl' && (
        <div className="space-y-4">
          {filteredFunctions.map((func) => (
            <div key={func.name} className="card">
              <div className="card-content">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-secondary-900">
                    {func.name}
                  </h3>
                  <span className="badge badge-default">{func.category}</span>
                </div>
                <p className="text-secondary-600 mb-4">{func.description}</p>

                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Syntax</h4>
                    <code className="block bg-secondary-100 p-3 rounded text-sm font-mono">
                      {func.syntax}
                    </code>
                  </div>

                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Example</h4>
                    <code className="block bg-secondary-50 p-3 rounded text-sm font-mono">
                      {func.example}
                    </code>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'guide' && (
        <div className="prose max-w-none">
          <div className="card">
            <div className="card-content">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">
                Getting Started with Moveworks Compound Actions
              </h2>

              <div className="space-y-6">
                <section>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                    What are Compound Actions?
                  </h3>
                  <p className="text-secondary-600 mb-4">
                    Moveworks Compound Actions are powerful workflow automation tools that allow you to
                    chain multiple actions together, handle complex data transformations, and implement
                    sophisticated business logic.
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                    Key Concepts
                  </h3>
                  <ul className="list-disc list-inside space-y-2 text-secondary-600">
                    <li><strong>Steps:</strong> Individual actions or operations in your workflow</li>
                    <li><strong>Data Object:</strong> Shared memory that stores input variables and step outputs</li>
                    <li><strong>Output Keys:</strong> Unique identifiers for storing step results</li>
                    <li><strong>Input Args:</strong> Parameters passed to each action</li>
                    <li><strong>Data References:</strong> Ways to access data from previous steps</li>
                  </ul>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                    Basic Workflow Structure
                  </h3>
                  <pre className="bg-secondary-50 p-4 rounded text-sm overflow-x-auto">
                    <code>{`steps:
  - action:
      action_name: mw.get_user_by_email
      output_key: user_info
      input_args:
        email: data.user_email

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_result
      input_args:
        user_record_id: data.user_info.user_id
        message: "Hello, welcome to the system!"`}</code>
                  </pre>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                    Best Practices
                  </h3>
                  <ul className="list-disc list-inside space-y-2 text-secondary-600">
                    <li>Use descriptive output_key names for better readability</li>
                    <li>Validate your workflows before deployment</li>
                    <li>Handle errors gracefully with try-catch blocks</li>
                    <li>Keep workflows modular and focused on specific tasks</li>
                    <li>Document your workflows with clear descriptions</li>
                  </ul>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                    External Resources
                  </h3>
                  <div className="space-y-2">
                    <a
                      href="https://docs.moveworks.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-primary-600 hover:text-primary-700"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Official Moveworks Documentation
                    </a>
                    <a
                      href="https://community.moveworks.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-primary-600 hover:text-primary-700"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Moveworks Community
                    </a>
                  </div>
                </section>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
