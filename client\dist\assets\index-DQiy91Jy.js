var $v=Object.defineProperty;var Ov=(e,t,n)=>t in e?$v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Nf=(e,t,n)=>Ov(e,typeof t!="symbol"?t+"":t,n);function Iv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function om(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var sm={exports:{}},Gl={},im={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xs=Symbol.for("react.element"),zv=Symbol.for("react.portal"),Lv=Symbol.for("react.fragment"),Dv=Symbol.for("react.strict_mode"),Fv=Symbol.for("react.profiler"),Uv=Symbol.for("react.provider"),Bv=Symbol.for("react.context"),Hv=Symbol.for("react.forward_ref"),Vv=Symbol.for("react.suspense"),Wv=Symbol.for("react.memo"),qv=Symbol.for("react.lazy"),Ef=Symbol.iterator;function Yv(e){return e===null||typeof e!="object"?null:(e=Ef&&e[Ef]||e["@@iterator"],typeof e=="function"?e:null)}var lm={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},am=Object.assign,cm={};function Eo(e,t,n){this.props=e,this.context=t,this.refs=cm,this.updater=n||lm}Eo.prototype.isReactComponent={};Eo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Eo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function um(){}um.prototype=Eo.prototype;function Zu(e,t,n){this.props=e,this.context=t,this.refs=cm,this.updater=n||lm}var Ju=Zu.prototype=new um;Ju.constructor=Zu;am(Ju,Eo.prototype);Ju.isPureReactComponent=!0;var kf=Array.isArray,dm=Object.prototype.hasOwnProperty,ed={current:null},fm={key:!0,ref:!0,__self:!0,__source:!0};function pm(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)dm.call(t,r)&&!fm.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),c=0;c<l;c++)a[c]=arguments[c+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Xs,type:e,key:s,ref:i,props:o,_owner:ed.current}}function Gv(e,t){return{$$typeof:Xs,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function td(e){return typeof e=="object"&&e!==null&&e.$$typeof===Xs}function Xv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var _f=/\/+/g;function Ma(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xv(""+e.key):t.toString(36)}function Fi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Xs:case zv:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+Ma(i,0):r,kf(o)?(n="",e!=null&&(n=e.replace(_f,"$&/")+"/"),Fi(o,t,n,"",function(c){return c})):o!=null&&(td(o)&&(o=Gv(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(_f,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",kf(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Ma(s,l);i+=Fi(s,t,n,a,o)}else if(a=Yv(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Ma(s,l++),i+=Fi(s,t,n,a,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function ci(e,t,n){if(e==null)return e;var r=[],o=0;return Fi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function Kv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Qe={current:null},Ui={transition:null},Qv={ReactCurrentDispatcher:Qe,ReactCurrentBatchConfig:Ui,ReactCurrentOwner:ed};function hm(){throw Error("act(...) is not supported in production builds of React.")}te.Children={map:ci,forEach:function(e,t,n){ci(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ci(e,function(){t++}),t},toArray:function(e){return ci(e,function(t){return t})||[]},only:function(e){if(!td(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};te.Component=Eo;te.Fragment=Lv;te.Profiler=Fv;te.PureComponent=Zu;te.StrictMode=Dv;te.Suspense=Vv;te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qv;te.act=hm;te.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=am({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=ed.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)dm.call(t,a)&&!fm.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var c=0;c<a;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:Xs,type:e.type,key:o,ref:s,props:r,_owner:i}};te.createContext=function(e){return e={$$typeof:Bv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Uv,_context:e},e.Consumer=e};te.createElement=pm;te.createFactory=function(e){var t=pm.bind(null,e);return t.type=e,t};te.createRef=function(){return{current:null}};te.forwardRef=function(e){return{$$typeof:Hv,render:e}};te.isValidElement=td;te.lazy=function(e){return{$$typeof:qv,_payload:{_status:-1,_result:e},_init:Kv}};te.memo=function(e,t){return{$$typeof:Wv,type:e,compare:t===void 0?null:t}};te.startTransition=function(e){var t=Ui.transition;Ui.transition={};try{e()}finally{Ui.transition=t}};te.unstable_act=hm;te.useCallback=function(e,t){return Qe.current.useCallback(e,t)};te.useContext=function(e){return Qe.current.useContext(e)};te.useDebugValue=function(){};te.useDeferredValue=function(e){return Qe.current.useDeferredValue(e)};te.useEffect=function(e,t){return Qe.current.useEffect(e,t)};te.useId=function(){return Qe.current.useId()};te.useImperativeHandle=function(e,t,n){return Qe.current.useImperativeHandle(e,t,n)};te.useInsertionEffect=function(e,t){return Qe.current.useInsertionEffect(e,t)};te.useLayoutEffect=function(e,t){return Qe.current.useLayoutEffect(e,t)};te.useMemo=function(e,t){return Qe.current.useMemo(e,t)};te.useReducer=function(e,t,n){return Qe.current.useReducer(e,t,n)};te.useRef=function(e){return Qe.current.useRef(e)};te.useState=function(e){return Qe.current.useState(e)};te.useSyncExternalStore=function(e,t,n){return Qe.current.useSyncExternalStore(e,t,n)};te.useTransition=function(){return Qe.current.useTransition()};te.version="18.3.1";im.exports=te;var b=im.exports;const I=om(b),Zv=Iv({__proto__:null,default:I},[b]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jv=b,ew=Symbol.for("react.element"),tw=Symbol.for("react.fragment"),nw=Object.prototype.hasOwnProperty,rw=Jv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ow={key:!0,ref:!0,__self:!0,__source:!0};function mm(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)nw.call(t,r)&&!ow.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:ew,type:e,key:s,ref:i,props:o,_owner:rw.current}}Gl.Fragment=tw;Gl.jsx=mm;Gl.jsxs=mm;sm.exports=Gl;var d=sm.exports,Cc={},gm={exports:{}},ht={},ym={exports:{}},xm={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(M,j){var O=M.length;M.push(j);e:for(;0<O;){var $=O-1>>>1,L=M[$];if(0<o(L,j))M[$]=j,M[O]=L,O=$;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var j=M[0],O=M.pop();if(O!==j){M[0]=O;e:for(var $=0,L=M.length,V=L>>>1;$<V;){var W=2*($+1)-1,Y=M[W],K=W+1,J=M[K];if(0>o(Y,O))K<L&&0>o(J,Y)?(M[$]=J,M[K]=O,$=K):(M[$]=Y,M[W]=O,$=W);else if(K<L&&0>o(J,O))M[$]=J,M[K]=O,$=K;else break e}}return j}function o(M,j){var O=M.sortIndex-j.sortIndex;return O!==0?O:M.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var a=[],c=[],u=1,f=null,m=3,g=!1,x=!1,y=!1,v=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function w(M){for(var j=n(c);j!==null;){if(j.callback===null)r(c);else if(j.startTime<=M)r(c),j.sortIndex=j.expirationTime,t(a,j);else break;j=n(c)}}function S(M){if(y=!1,w(M),!x)if(n(a)!==null)x=!0,R(N);else{var j=n(c);j!==null&&F(S,j.startTime-M)}}function N(M,j){x=!1,y&&(y=!1,h(C),C=-1),g=!0;var O=m;try{for(w(j),f=n(a);f!==null&&(!(f.expirationTime>j)||M&&!D());){var $=f.callback;if(typeof $=="function"){f.callback=null,m=f.priorityLevel;var L=$(f.expirationTime<=j);j=e.unstable_now(),typeof L=="function"?f.callback=L:f===n(a)&&r(a),w(j)}else r(a);f=n(a)}if(f!==null)var V=!0;else{var W=n(c);W!==null&&F(S,W.startTime-j),V=!1}return V}finally{f=null,m=O,g=!1}}var E=!1,_=null,C=-1,T=5,P=-1;function D(){return!(e.unstable_now()-P<T)}function U(){if(_!==null){var M=e.unstable_now();P=M;var j=!0;try{j=_(!0,M)}finally{j?B():(E=!1,_=null)}}else E=!1}var B;if(typeof p=="function")B=function(){p(U)};else if(typeof MessageChannel<"u"){var k=new MessageChannel,A=k.port2;k.port1.onmessage=U,B=function(){A.postMessage(null)}}else B=function(){v(U,0)};function R(M){_=M,E||(E=!0,B())}function F(M,j){C=v(function(){M(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(M){M.callback=null},e.unstable_continueExecution=function(){x||g||(x=!0,R(N))},e.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<M?Math.floor(1e3/M):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(M){switch(m){case 1:case 2:case 3:var j=3;break;default:j=m}var O=m;m=j;try{return M()}finally{m=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(M,j){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var O=m;m=M;try{return j()}finally{m=O}},e.unstable_scheduleCallback=function(M,j,O){var $=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?$+O:$):O=$,M){case 1:var L=-1;break;case 2:L=250;break;case 5:L=**********;break;case 4:L=1e4;break;default:L=5e3}return L=O+L,M={id:u++,callback:j,priorityLevel:M,startTime:O,expirationTime:L,sortIndex:-1},O>$?(M.sortIndex=O,t(c,M),n(a)===null&&M===n(c)&&(y?(h(C),C=-1):y=!0,F(S,O-$))):(M.sortIndex=L,t(a,M),x||g||(x=!0,R(N))),M},e.unstable_shouldYield=D,e.unstable_wrapCallback=function(M){var j=m;return function(){var O=m;m=j;try{return M.apply(this,arguments)}finally{m=O}}}})(xm);ym.exports=xm;var sw=ym.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var iw=b,ft=sw;function H(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var vm=new Set,ys={};function Nr(e,t){fo(e,t),fo(e+"Capture",t)}function fo(e,t){for(ys[e]=t,e=0;e<t.length;e++)vm.add(t[e])}var ln=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),jc=Object.prototype.hasOwnProperty,lw=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Cf={},jf={};function aw(e){return jc.call(jf,e)?!0:jc.call(Cf,e)?!1:lw.test(e)?jf[e]=!0:(Cf[e]=!0,!1)}function cw(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function uw(e,t,n,r){if(t===null||typeof t>"u"||cw(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ze(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var ze={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ze[e]=new Ze(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ze[t]=new Ze(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ze[e]=new Ze(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ze[e]=new Ze(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ze[e]=new Ze(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ze[e]=new Ze(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ze[e]=new Ze(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ze[e]=new Ze(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ze[e]=new Ze(e,5,!1,e.toLowerCase(),null,!1,!1)});var nd=/[\-:]([a-z])/g;function rd(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(nd,rd);ze[t]=new Ze(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(nd,rd);ze[t]=new Ze(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(nd,rd);ze[t]=new Ze(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ze[e]=new Ze(e,1,!1,e.toLowerCase(),null,!1,!1)});ze.xlinkHref=new Ze("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ze[e]=new Ze(e,1,!1,e.toLowerCase(),null,!0,!0)});function od(e,t,n,r){var o=ze.hasOwnProperty(t)?ze[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(uw(t,n,o,r)&&(n=null),r||o===null?aw(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var hn=iw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ui=Symbol.for("react.element"),Fr=Symbol.for("react.portal"),Ur=Symbol.for("react.fragment"),sd=Symbol.for("react.strict_mode"),Rc=Symbol.for("react.profiler"),wm=Symbol.for("react.provider"),Sm=Symbol.for("react.context"),id=Symbol.for("react.forward_ref"),Ac=Symbol.for("react.suspense"),Tc=Symbol.for("react.suspense_list"),ld=Symbol.for("react.memo"),vn=Symbol.for("react.lazy"),bm=Symbol.for("react.offscreen"),Rf=Symbol.iterator;function Oo(e){return e===null||typeof e!="object"?null:(e=Rf&&e[Rf]||e["@@iterator"],typeof e=="function"?e:null)}var ye=Object.assign,$a;function Qo(e){if($a===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$a=t&&t[1]||""}return`
`+$a+e}var Oa=!1;function Ia(e,t){if(!e||Oa)return"";Oa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,l=s.length-1;1<=i&&0<=l&&o[i]!==s[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==s[l]){if(i!==1||l!==1)do if(i--,l--,0>l||o[i]!==s[l]){var a=`
`+o[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=l);break}}}finally{Oa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Qo(e):""}function dw(e){switch(e.tag){case 5:return Qo(e.type);case 16:return Qo("Lazy");case 13:return Qo("Suspense");case 19:return Qo("SuspenseList");case 0:case 2:case 15:return e=Ia(e.type,!1),e;case 11:return e=Ia(e.type.render,!1),e;case 1:return e=Ia(e.type,!0),e;default:return""}}function Pc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ur:return"Fragment";case Fr:return"Portal";case Rc:return"Profiler";case sd:return"StrictMode";case Ac:return"Suspense";case Tc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Sm:return(e.displayName||"Context")+".Consumer";case wm:return(e._context.displayName||"Context")+".Provider";case id:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ld:return t=e.displayName||null,t!==null?t:Pc(e.type)||"Memo";case vn:t=e._payload,e=e._init;try{return Pc(e(t))}catch{}}return null}function fw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Pc(t);case 8:return t===sd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Un(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Nm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function pw(e){var t=Nm(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function di(e){e._valueTracker||(e._valueTracker=pw(e))}function Em(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Nm(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function al(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Mc(e,t){var n=t.checked;return ye({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Af(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Un(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function km(e,t){t=t.checked,t!=null&&od(e,"checked",t,!1)}function $c(e,t){km(e,t);var n=Un(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Oc(e,t.type,n):t.hasOwnProperty("defaultValue")&&Oc(e,t.type,Un(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Tf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Oc(e,t,n){(t!=="number"||al(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Zo=Array.isArray;function to(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Un(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ic(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(H(91));return ye({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(H(92));if(Zo(n)){if(1<n.length)throw Error(H(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Un(n)}}function _m(e,t){var n=Un(t.value),r=Un(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Mf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Cm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Cm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var fi,jm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(fi=fi||document.createElement("div"),fi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=fi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function xs(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var is={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hw=["Webkit","ms","Moz","O"];Object.keys(is).forEach(function(e){hw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),is[t]=is[e]})});function Rm(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||is.hasOwnProperty(e)&&is[e]?(""+t).trim():t+"px"}function Am(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Rm(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var mw=ye({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Lc(e,t){if(t){if(mw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(H(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(H(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(H(61))}if(t.style!=null&&typeof t.style!="object")throw Error(H(62))}}function Dc(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Fc=null;function ad(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Uc=null,no=null,ro=null;function $f(e){if(e=Zs(e)){if(typeof Uc!="function")throw Error(H(280));var t=e.stateNode;t&&(t=Jl(t),Uc(e.stateNode,e.type,t))}}function Tm(e){no?ro?ro.push(e):ro=[e]:no=e}function Pm(){if(no){var e=no,t=ro;if(ro=no=null,$f(e),t)for(e=0;e<t.length;e++)$f(t[e])}}function Mm(e,t){return e(t)}function $m(){}var za=!1;function Om(e,t,n){if(za)return e(t,n);za=!0;try{return Mm(e,t,n)}finally{za=!1,(no!==null||ro!==null)&&($m(),Pm())}}function vs(e,t){var n=e.stateNode;if(n===null)return null;var r=Jl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(H(231,t,typeof n));return n}var Bc=!1;if(ln)try{var Io={};Object.defineProperty(Io,"passive",{get:function(){Bc=!0}}),window.addEventListener("test",Io,Io),window.removeEventListener("test",Io,Io)}catch{Bc=!1}function gw(e,t,n,r,o,s,i,l,a){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var ls=!1,cl=null,ul=!1,Hc=null,yw={onError:function(e){ls=!0,cl=e}};function xw(e,t,n,r,o,s,i,l,a){ls=!1,cl=null,gw.apply(yw,arguments)}function vw(e,t,n,r,o,s,i,l,a){if(xw.apply(this,arguments),ls){if(ls){var c=cl;ls=!1,cl=null}else throw Error(H(198));ul||(ul=!0,Hc=c)}}function Er(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Im(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Of(e){if(Er(e)!==e)throw Error(H(188))}function ww(e){var t=e.alternate;if(!t){if(t=Er(e),t===null)throw Error(H(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return Of(o),e;if(s===r)return Of(o),t;s=s.sibling}throw Error(H(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=s;break}if(l===r){i=!0,r=o,n=s;break}l=l.sibling}if(!i){for(l=s.child;l;){if(l===n){i=!0,n=s,r=o;break}if(l===r){i=!0,r=s,n=o;break}l=l.sibling}if(!i)throw Error(H(189))}}if(n.alternate!==r)throw Error(H(190))}if(n.tag!==3)throw Error(H(188));return n.stateNode.current===n?e:t}function zm(e){return e=ww(e),e!==null?Lm(e):null}function Lm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Lm(e);if(t!==null)return t;e=e.sibling}return null}var Dm=ft.unstable_scheduleCallback,If=ft.unstable_cancelCallback,Sw=ft.unstable_shouldYield,bw=ft.unstable_requestPaint,Se=ft.unstable_now,Nw=ft.unstable_getCurrentPriorityLevel,cd=ft.unstable_ImmediatePriority,Fm=ft.unstable_UserBlockingPriority,dl=ft.unstable_NormalPriority,Ew=ft.unstable_LowPriority,Um=ft.unstable_IdlePriority,Xl=null,Bt=null;function kw(e){if(Bt&&typeof Bt.onCommitFiberRoot=="function")try{Bt.onCommitFiberRoot(Xl,e,void 0,(e.current.flags&128)===128)}catch{}}var Tt=Math.clz32?Math.clz32:jw,_w=Math.log,Cw=Math.LN2;function jw(e){return e>>>=0,e===0?32:31-(_w(e)/Cw|0)|0}var pi=64,hi=4194304;function Jo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function fl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var l=i&~o;l!==0?r=Jo(l):(s&=i,s!==0&&(r=Jo(s)))}else i=n&~o,i!==0?r=Jo(i):s!==0&&(r=Jo(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Tt(t),o=1<<n,r|=e[n],t&=~o;return r}function Rw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Aw(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Tt(s),l=1<<i,a=o[i];a===-1?(!(l&n)||l&r)&&(o[i]=Rw(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Vc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Bm(){var e=pi;return pi<<=1,!(pi&4194240)&&(pi=64),e}function La(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ks(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Tt(t),e[t]=n}function Tw(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Tt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function ud(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Tt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var le=0;function Hm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vm,dd,Wm,qm,Ym,Wc=!1,mi=[],An=null,Tn=null,Pn=null,ws=new Map,Ss=new Map,Nn=[],Pw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zf(e,t){switch(e){case"focusin":case"focusout":An=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Pn=null;break;case"pointerover":case"pointerout":ws.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ss.delete(t.pointerId)}}function zo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=Zs(t),t!==null&&dd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Mw(e,t,n,r,o){switch(t){case"focusin":return An=zo(An,e,t,n,r,o),!0;case"dragenter":return Tn=zo(Tn,e,t,n,r,o),!0;case"mouseover":return Pn=zo(Pn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return ws.set(s,zo(ws.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Ss.set(s,zo(Ss.get(s)||null,e,t,n,r,o)),!0}return!1}function Gm(e){var t=rr(e.target);if(t!==null){var n=Er(t);if(n!==null){if(t=n.tag,t===13){if(t=Im(n),t!==null){e.blockedOn=t,Ym(e.priority,function(){Wm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Bi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Fc=r,n.target.dispatchEvent(r),Fc=null}else return t=Zs(n),t!==null&&dd(t),e.blockedOn=n,!1;t.shift()}return!0}function Lf(e,t,n){Bi(e)&&n.delete(t)}function $w(){Wc=!1,An!==null&&Bi(An)&&(An=null),Tn!==null&&Bi(Tn)&&(Tn=null),Pn!==null&&Bi(Pn)&&(Pn=null),ws.forEach(Lf),Ss.forEach(Lf)}function Lo(e,t){e.blockedOn===t&&(e.blockedOn=null,Wc||(Wc=!0,ft.unstable_scheduleCallback(ft.unstable_NormalPriority,$w)))}function bs(e){function t(o){return Lo(o,e)}if(0<mi.length){Lo(mi[0],e);for(var n=1;n<mi.length;n++){var r=mi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(An!==null&&Lo(An,e),Tn!==null&&Lo(Tn,e),Pn!==null&&Lo(Pn,e),ws.forEach(t),Ss.forEach(t),n=0;n<Nn.length;n++)r=Nn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Nn.length&&(n=Nn[0],n.blockedOn===null);)Gm(n),n.blockedOn===null&&Nn.shift()}var oo=hn.ReactCurrentBatchConfig,pl=!0;function Ow(e,t,n,r){var o=le,s=oo.transition;oo.transition=null;try{le=1,fd(e,t,n,r)}finally{le=o,oo.transition=s}}function Iw(e,t,n,r){var o=le,s=oo.transition;oo.transition=null;try{le=4,fd(e,t,n,r)}finally{le=o,oo.transition=s}}function fd(e,t,n,r){if(pl){var o=qc(e,t,n,r);if(o===null)Ga(e,t,r,hl,n),zf(e,r);else if(Mw(o,e,t,n,r))r.stopPropagation();else if(zf(e,r),t&4&&-1<Pw.indexOf(e)){for(;o!==null;){var s=Zs(o);if(s!==null&&Vm(s),s=qc(e,t,n,r),s===null&&Ga(e,t,r,hl,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else Ga(e,t,r,null,n)}}var hl=null;function qc(e,t,n,r){if(hl=null,e=ad(r),e=rr(e),e!==null)if(t=Er(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Im(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return hl=e,null}function Xm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Nw()){case cd:return 1;case Fm:return 4;case dl:case Ew:return 16;case Um:return 536870912;default:return 16}default:return 16}}var Cn=null,pd=null,Hi=null;function Km(){if(Hi)return Hi;var e,t=pd,n=t.length,r,o="value"in Cn?Cn.value:Cn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Hi=o.slice(e,1<r?1-r:void 0)}function Vi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function gi(){return!0}function Df(){return!1}function mt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?gi:Df,this.isPropagationStopped=Df,this}return ye(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gi)},persist:function(){},isPersistent:gi}),t}var ko={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hd=mt(ko),Qs=ye({},ko,{view:0,detail:0}),zw=mt(Qs),Da,Fa,Do,Kl=ye({},Qs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:md,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Do&&(Do&&e.type==="mousemove"?(Da=e.screenX-Do.screenX,Fa=e.screenY-Do.screenY):Fa=Da=0,Do=e),Da)},movementY:function(e){return"movementY"in e?e.movementY:Fa}}),Ff=mt(Kl),Lw=ye({},Kl,{dataTransfer:0}),Dw=mt(Lw),Fw=ye({},Qs,{relatedTarget:0}),Ua=mt(Fw),Uw=ye({},ko,{animationName:0,elapsedTime:0,pseudoElement:0}),Bw=mt(Uw),Hw=ye({},ko,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vw=mt(Hw),Ww=ye({},ko,{data:0}),Uf=mt(Ww),qw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Yw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Gw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Gw[e])?!!t[e]:!1}function md(){return Xw}var Kw=ye({},Qs,{key:function(e){if(e.key){var t=qw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Vi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Yw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:md,charCode:function(e){return e.type==="keypress"?Vi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Vi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Qw=mt(Kw),Zw=ye({},Kl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bf=mt(Zw),Jw=ye({},Qs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:md}),e1=mt(Jw),t1=ye({},ko,{propertyName:0,elapsedTime:0,pseudoElement:0}),n1=mt(t1),r1=ye({},Kl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),o1=mt(r1),s1=[9,13,27,32],gd=ln&&"CompositionEvent"in window,as=null;ln&&"documentMode"in document&&(as=document.documentMode);var i1=ln&&"TextEvent"in window&&!as,Qm=ln&&(!gd||as&&8<as&&11>=as),Hf=" ",Vf=!1;function Zm(e,t){switch(e){case"keyup":return s1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Br=!1;function l1(e,t){switch(e){case"compositionend":return Jm(t);case"keypress":return t.which!==32?null:(Vf=!0,Hf);case"textInput":return e=t.data,e===Hf&&Vf?null:e;default:return null}}function a1(e,t){if(Br)return e==="compositionend"||!gd&&Zm(e,t)?(e=Km(),Hi=pd=Cn=null,Br=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qm&&t.locale!=="ko"?null:t.data;default:return null}}var c1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!c1[e.type]:t==="textarea"}function eg(e,t,n,r){Tm(r),t=ml(t,"onChange"),0<t.length&&(n=new hd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var cs=null,Ns=null;function u1(e){dg(e,0)}function Ql(e){var t=Wr(e);if(Em(t))return e}function d1(e,t){if(e==="change")return t}var tg=!1;if(ln){var Ba;if(ln){var Ha="oninput"in document;if(!Ha){var qf=document.createElement("div");qf.setAttribute("oninput","return;"),Ha=typeof qf.oninput=="function"}Ba=Ha}else Ba=!1;tg=Ba&&(!document.documentMode||9<document.documentMode)}function Yf(){cs&&(cs.detachEvent("onpropertychange",ng),Ns=cs=null)}function ng(e){if(e.propertyName==="value"&&Ql(Ns)){var t=[];eg(t,Ns,e,ad(e)),Om(u1,t)}}function f1(e,t,n){e==="focusin"?(Yf(),cs=t,Ns=n,cs.attachEvent("onpropertychange",ng)):e==="focusout"&&Yf()}function p1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ql(Ns)}function h1(e,t){if(e==="click")return Ql(t)}function m1(e,t){if(e==="input"||e==="change")return Ql(t)}function g1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var $t=typeof Object.is=="function"?Object.is:g1;function Es(e,t){if($t(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!jc.call(t,o)||!$t(e[o],t[o]))return!1}return!0}function Gf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xf(e,t){var n=Gf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Gf(n)}}function rg(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rg(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function og(){for(var e=window,t=al();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=al(e.document)}return t}function yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function y1(e){var t=og(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&rg(n.ownerDocument.documentElement,n)){if(r!==null&&yd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Xf(n,s);var i=Xf(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var x1=ln&&"documentMode"in document&&11>=document.documentMode,Hr=null,Yc=null,us=null,Gc=!1;function Kf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Gc||Hr==null||Hr!==al(r)||(r=Hr,"selectionStart"in r&&yd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),us&&Es(us,r)||(us=r,r=ml(Yc,"onSelect"),0<r.length&&(t=new hd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hr)))}function yi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Vr={animationend:yi("Animation","AnimationEnd"),animationiteration:yi("Animation","AnimationIteration"),animationstart:yi("Animation","AnimationStart"),transitionend:yi("Transition","TransitionEnd")},Va={},sg={};ln&&(sg=document.createElement("div").style,"AnimationEvent"in window||(delete Vr.animationend.animation,delete Vr.animationiteration.animation,delete Vr.animationstart.animation),"TransitionEvent"in window||delete Vr.transitionend.transition);function Zl(e){if(Va[e])return Va[e];if(!Vr[e])return e;var t=Vr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sg)return Va[e]=t[n];return e}var ig=Zl("animationend"),lg=Zl("animationiteration"),ag=Zl("animationstart"),cg=Zl("transitionend"),ug=new Map,Qf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Hn(e,t){ug.set(e,t),Nr(t,[e])}for(var Wa=0;Wa<Qf.length;Wa++){var qa=Qf[Wa],v1=qa.toLowerCase(),w1=qa[0].toUpperCase()+qa.slice(1);Hn(v1,"on"+w1)}Hn(ig,"onAnimationEnd");Hn(lg,"onAnimationIteration");Hn(ag,"onAnimationStart");Hn("dblclick","onDoubleClick");Hn("focusin","onFocus");Hn("focusout","onBlur");Hn(cg,"onTransitionEnd");fo("onMouseEnter",["mouseout","mouseover"]);fo("onMouseLeave",["mouseout","mouseover"]);fo("onPointerEnter",["pointerout","pointerover"]);fo("onPointerLeave",["pointerout","pointerover"]);Nr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Nr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Nr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Nr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Nr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Nr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var es="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),S1=new Set("cancel close invalid load scroll toggle".split(" ").concat(es));function Zf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,vw(r,t,void 0,e),e.currentTarget=null}function dg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],a=l.instance,c=l.currentTarget;if(l=l.listener,a!==s&&o.isPropagationStopped())break e;Zf(o,l,c),s=a}else for(i=0;i<r.length;i++){if(l=r[i],a=l.instance,c=l.currentTarget,l=l.listener,a!==s&&o.isPropagationStopped())break e;Zf(o,l,c),s=a}}}if(ul)throw e=Hc,ul=!1,Hc=null,e}function fe(e,t){var n=t[Jc];n===void 0&&(n=t[Jc]=new Set);var r=e+"__bubble";n.has(r)||(fg(t,e,2,!1),n.add(r))}function Ya(e,t,n){var r=0;t&&(r|=4),fg(n,e,r,t)}var xi="_reactListening"+Math.random().toString(36).slice(2);function ks(e){if(!e[xi]){e[xi]=!0,vm.forEach(function(n){n!=="selectionchange"&&(S1.has(n)||Ya(n,!1,e),Ya(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xi]||(t[xi]=!0,Ya("selectionchange",!1,t))}}function fg(e,t,n,r){switch(Xm(t)){case 1:var o=Ow;break;case 4:o=Iw;break;default:o=fd}n=o.bind(null,t,n,e),o=void 0,!Bc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ga(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;i=i.return}for(;l!==null;){if(i=rr(l),i===null)return;if(a=i.tag,a===5||a===6){r=s=i;continue e}l=l.parentNode}}r=r.return}Om(function(){var c=s,u=ad(n),f=[];e:{var m=ug.get(e);if(m!==void 0){var g=hd,x=e;switch(e){case"keypress":if(Vi(n)===0)break e;case"keydown":case"keyup":g=Qw;break;case"focusin":x="focus",g=Ua;break;case"focusout":x="blur",g=Ua;break;case"beforeblur":case"afterblur":g=Ua;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Dw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=e1;break;case ig:case lg:case ag:g=Bw;break;case cg:g=n1;break;case"scroll":g=zw;break;case"wheel":g=o1;break;case"copy":case"cut":case"paste":g=Vw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Bf}var y=(t&4)!==0,v=!y&&e==="scroll",h=y?m!==null?m+"Capture":null:m;y=[];for(var p=c,w;p!==null;){w=p;var S=w.stateNode;if(w.tag===5&&S!==null&&(w=S,h!==null&&(S=vs(p,h),S!=null&&y.push(_s(p,S,w)))),v)break;p=p.return}0<y.length&&(m=new g(m,x,null,n,u),f.push({event:m,listeners:y}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",m&&n!==Fc&&(x=n.relatedTarget||n.fromElement)&&(rr(x)||x[an]))break e;if((g||m)&&(m=u.window===u?u:(m=u.ownerDocument)?m.defaultView||m.parentWindow:window,g?(x=n.relatedTarget||n.toElement,g=c,x=x?rr(x):null,x!==null&&(v=Er(x),x!==v||x.tag!==5&&x.tag!==6)&&(x=null)):(g=null,x=c),g!==x)){if(y=Ff,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Bf,S="onPointerLeave",h="onPointerEnter",p="pointer"),v=g==null?m:Wr(g),w=x==null?m:Wr(x),m=new y(S,p+"leave",g,n,u),m.target=v,m.relatedTarget=w,S=null,rr(u)===c&&(y=new y(h,p+"enter",x,n,u),y.target=w,y.relatedTarget=v,S=y),v=S,g&&x)t:{for(y=g,h=x,p=0,w=y;w;w=Pr(w))p++;for(w=0,S=h;S;S=Pr(S))w++;for(;0<p-w;)y=Pr(y),p--;for(;0<w-p;)h=Pr(h),w--;for(;p--;){if(y===h||h!==null&&y===h.alternate)break t;y=Pr(y),h=Pr(h)}y=null}else y=null;g!==null&&Jf(f,m,g,y,!1),x!==null&&v!==null&&Jf(f,v,x,y,!0)}}e:{if(m=c?Wr(c):window,g=m.nodeName&&m.nodeName.toLowerCase(),g==="select"||g==="input"&&m.type==="file")var N=d1;else if(Wf(m))if(tg)N=m1;else{N=p1;var E=f1}else(g=m.nodeName)&&g.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(N=h1);if(N&&(N=N(e,c))){eg(f,N,n,u);break e}E&&E(e,m,c),e==="focusout"&&(E=m._wrapperState)&&E.controlled&&m.type==="number"&&Oc(m,"number",m.value)}switch(E=c?Wr(c):window,e){case"focusin":(Wf(E)||E.contentEditable==="true")&&(Hr=E,Yc=c,us=null);break;case"focusout":us=Yc=Hr=null;break;case"mousedown":Gc=!0;break;case"contextmenu":case"mouseup":case"dragend":Gc=!1,Kf(f,n,u);break;case"selectionchange":if(x1)break;case"keydown":case"keyup":Kf(f,n,u)}var _;if(gd)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Br?Zm(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(Qm&&n.locale!=="ko"&&(Br||C!=="onCompositionStart"?C==="onCompositionEnd"&&Br&&(_=Km()):(Cn=u,pd="value"in Cn?Cn.value:Cn.textContent,Br=!0)),E=ml(c,C),0<E.length&&(C=new Uf(C,e,null,n,u),f.push({event:C,listeners:E}),_?C.data=_:(_=Jm(n),_!==null&&(C.data=_)))),(_=i1?l1(e,n):a1(e,n))&&(c=ml(c,"onBeforeInput"),0<c.length&&(u=new Uf("onBeforeInput","beforeinput",null,n,u),f.push({event:u,listeners:c}),u.data=_))}dg(f,t)})}function _s(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ml(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=vs(e,n),s!=null&&r.unshift(_s(e,s,o)),s=vs(e,t),s!=null&&r.push(_s(e,s,o))),e=e.return}return r}function Pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Jf(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var l=n,a=l.alternate,c=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&c!==null&&(l=c,o?(a=vs(n,s),a!=null&&i.unshift(_s(n,a,l))):o||(a=vs(n,s),a!=null&&i.push(_s(n,a,l)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var b1=/\r\n?/g,N1=/\u0000|\uFFFD/g;function ep(e){return(typeof e=="string"?e:""+e).replace(b1,`
`).replace(N1,"")}function vi(e,t,n){if(t=ep(t),ep(e)!==t&&n)throw Error(H(425))}function gl(){}var Xc=null,Kc=null;function Qc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zc=typeof setTimeout=="function"?setTimeout:void 0,E1=typeof clearTimeout=="function"?clearTimeout:void 0,tp=typeof Promise=="function"?Promise:void 0,k1=typeof queueMicrotask=="function"?queueMicrotask:typeof tp<"u"?function(e){return tp.resolve(null).then(e).catch(_1)}:Zc;function _1(e){setTimeout(function(){throw e})}function Xa(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),bs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);bs(t)}function Mn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function np(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var _o=Math.random().toString(36).slice(2),Ut="__reactFiber$"+_o,Cs="__reactProps$"+_o,an="__reactContainer$"+_o,Jc="__reactEvents$"+_o,C1="__reactListeners$"+_o,j1="__reactHandles$"+_o;function rr(e){var t=e[Ut];if(t)return t;for(var n=e.parentNode;n;){if(t=n[an]||n[Ut]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=np(e);e!==null;){if(n=e[Ut])return n;e=np(e)}return t}e=n,n=e.parentNode}return null}function Zs(e){return e=e[Ut]||e[an],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Wr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(H(33))}function Jl(e){return e[Cs]||null}var eu=[],qr=-1;function Vn(e){return{current:e}}function pe(e){0>qr||(e.current=eu[qr],eu[qr]=null,qr--)}function ce(e,t){qr++,eu[qr]=e.current,e.current=t}var Bn={},We=Vn(Bn),nt=Vn(!1),mr=Bn;function po(e,t){var n=e.type.contextTypes;if(!n)return Bn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function rt(e){return e=e.childContextTypes,e!=null}function yl(){pe(nt),pe(We)}function rp(e,t,n){if(We.current!==Bn)throw Error(H(168));ce(We,t),ce(nt,n)}function pg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(H(108,fw(e)||"Unknown",o));return ye({},n,r)}function xl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Bn,mr=We.current,ce(We,e),ce(nt,nt.current),!0}function op(e,t,n){var r=e.stateNode;if(!r)throw Error(H(169));n?(e=pg(e,t,mr),r.__reactInternalMemoizedMergedChildContext=e,pe(nt),pe(We),ce(We,e)):pe(nt),ce(nt,n)}var en=null,ea=!1,Ka=!1;function hg(e){en===null?en=[e]:en.push(e)}function R1(e){ea=!0,hg(e)}function Wn(){if(!Ka&&en!==null){Ka=!0;var e=0,t=le;try{var n=en;for(le=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}en=null,ea=!1}catch(o){throw en!==null&&(en=en.slice(e+1)),Dm(cd,Wn),o}finally{le=t,Ka=!1}}return null}var Yr=[],Gr=0,vl=null,wl=0,gt=[],yt=0,gr=null,tn=1,nn="";function er(e,t){Yr[Gr++]=wl,Yr[Gr++]=vl,vl=e,wl=t}function mg(e,t,n){gt[yt++]=tn,gt[yt++]=nn,gt[yt++]=gr,gr=e;var r=tn;e=nn;var o=32-Tt(r)-1;r&=~(1<<o),n+=1;var s=32-Tt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,tn=1<<32-Tt(t)+o|n<<o|r,nn=s+e}else tn=1<<s|n<<o|r,nn=e}function xd(e){e.return!==null&&(er(e,1),mg(e,1,0))}function vd(e){for(;e===vl;)vl=Yr[--Gr],Yr[Gr]=null,wl=Yr[--Gr],Yr[Gr]=null;for(;e===gr;)gr=gt[--yt],gt[yt]=null,nn=gt[--yt],gt[yt]=null,tn=gt[--yt],gt[yt]=null}var dt=null,ut=null,he=!1,Rt=null;function gg(e,t){var n=vt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function sp(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,dt=e,ut=Mn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,dt=e,ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=gr!==null?{id:tn,overflow:nn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=vt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,dt=e,ut=null,!0):!1;default:return!1}}function tu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nu(e){if(he){var t=ut;if(t){var n=t;if(!sp(e,t)){if(tu(e))throw Error(H(418));t=Mn(n.nextSibling);var r=dt;t&&sp(e,t)?gg(r,n):(e.flags=e.flags&-4097|2,he=!1,dt=e)}}else{if(tu(e))throw Error(H(418));e.flags=e.flags&-4097|2,he=!1,dt=e}}}function ip(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;dt=e}function wi(e){if(e!==dt)return!1;if(!he)return ip(e),he=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Qc(e.type,e.memoizedProps)),t&&(t=ut)){if(tu(e))throw yg(),Error(H(418));for(;t;)gg(e,t),t=Mn(t.nextSibling)}if(ip(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(H(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ut=Mn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ut=null}}else ut=dt?Mn(e.stateNode.nextSibling):null;return!0}function yg(){for(var e=ut;e;)e=Mn(e.nextSibling)}function ho(){ut=dt=null,he=!1}function wd(e){Rt===null?Rt=[e]:Rt.push(e)}var A1=hn.ReactCurrentBatchConfig;function Fo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(H(309));var r=n.stateNode}if(!r)throw Error(H(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var l=o.refs;i===null?delete l[s]:l[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(H(284));if(!n._owner)throw Error(H(290,e))}return e}function Si(e,t){throw e=Object.prototype.toString.call(t),Error(H(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function lp(e){var t=e._init;return t(e._payload)}function xg(e){function t(h,p){if(e){var w=h.deletions;w===null?(h.deletions=[p],h.flags|=16):w.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=zn(h,p),h.index=0,h.sibling=null,h}function s(h,p,w){return h.index=w,e?(w=h.alternate,w!==null?(w=w.index,w<p?(h.flags|=2,p):w):(h.flags|=2,p)):(h.flags|=1048576,p)}function i(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,w,S){return p===null||p.tag!==6?(p=rc(w,h.mode,S),p.return=h,p):(p=o(p,w),p.return=h,p)}function a(h,p,w,S){var N=w.type;return N===Ur?u(h,p,w.props.children,S,w.key):p!==null&&(p.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===vn&&lp(N)===p.type)?(S=o(p,w.props),S.ref=Fo(h,p,w),S.return=h,S):(S=Qi(w.type,w.key,w.props,null,h.mode,S),S.ref=Fo(h,p,w),S.return=h,S)}function c(h,p,w,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==w.containerInfo||p.stateNode.implementation!==w.implementation?(p=oc(w,h.mode,S),p.return=h,p):(p=o(p,w.children||[]),p.return=h,p)}function u(h,p,w,S,N){return p===null||p.tag!==7?(p=dr(w,h.mode,S,N),p.return=h,p):(p=o(p,w),p.return=h,p)}function f(h,p,w){if(typeof p=="string"&&p!==""||typeof p=="number")return p=rc(""+p,h.mode,w),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ui:return w=Qi(p.type,p.key,p.props,null,h.mode,w),w.ref=Fo(h,null,p),w.return=h,w;case Fr:return p=oc(p,h.mode,w),p.return=h,p;case vn:var S=p._init;return f(h,S(p._payload),w)}if(Zo(p)||Oo(p))return p=dr(p,h.mode,w,null),p.return=h,p;Si(h,p)}return null}function m(h,p,w,S){var N=p!==null?p.key:null;if(typeof w=="string"&&w!==""||typeof w=="number")return N!==null?null:l(h,p,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ui:return w.key===N?a(h,p,w,S):null;case Fr:return w.key===N?c(h,p,w,S):null;case vn:return N=w._init,m(h,p,N(w._payload),S)}if(Zo(w)||Oo(w))return N!==null?null:u(h,p,w,S,null);Si(h,w)}return null}function g(h,p,w,S,N){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(w)||null,l(p,h,""+S,N);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case ui:return h=h.get(S.key===null?w:S.key)||null,a(p,h,S,N);case Fr:return h=h.get(S.key===null?w:S.key)||null,c(p,h,S,N);case vn:var E=S._init;return g(h,p,w,E(S._payload),N)}if(Zo(S)||Oo(S))return h=h.get(w)||null,u(p,h,S,N,null);Si(p,S)}return null}function x(h,p,w,S){for(var N=null,E=null,_=p,C=p=0,T=null;_!==null&&C<w.length;C++){_.index>C?(T=_,_=null):T=_.sibling;var P=m(h,_,w[C],S);if(P===null){_===null&&(_=T);break}e&&_&&P.alternate===null&&t(h,_),p=s(P,p,C),E===null?N=P:E.sibling=P,E=P,_=T}if(C===w.length)return n(h,_),he&&er(h,C),N;if(_===null){for(;C<w.length;C++)_=f(h,w[C],S),_!==null&&(p=s(_,p,C),E===null?N=_:E.sibling=_,E=_);return he&&er(h,C),N}for(_=r(h,_);C<w.length;C++)T=g(_,h,C,w[C],S),T!==null&&(e&&T.alternate!==null&&_.delete(T.key===null?C:T.key),p=s(T,p,C),E===null?N=T:E.sibling=T,E=T);return e&&_.forEach(function(D){return t(h,D)}),he&&er(h,C),N}function y(h,p,w,S){var N=Oo(w);if(typeof N!="function")throw Error(H(150));if(w=N.call(w),w==null)throw Error(H(151));for(var E=N=null,_=p,C=p=0,T=null,P=w.next();_!==null&&!P.done;C++,P=w.next()){_.index>C?(T=_,_=null):T=_.sibling;var D=m(h,_,P.value,S);if(D===null){_===null&&(_=T);break}e&&_&&D.alternate===null&&t(h,_),p=s(D,p,C),E===null?N=D:E.sibling=D,E=D,_=T}if(P.done)return n(h,_),he&&er(h,C),N;if(_===null){for(;!P.done;C++,P=w.next())P=f(h,P.value,S),P!==null&&(p=s(P,p,C),E===null?N=P:E.sibling=P,E=P);return he&&er(h,C),N}for(_=r(h,_);!P.done;C++,P=w.next())P=g(_,h,C,P.value,S),P!==null&&(e&&P.alternate!==null&&_.delete(P.key===null?C:P.key),p=s(P,p,C),E===null?N=P:E.sibling=P,E=P);return e&&_.forEach(function(U){return t(h,U)}),he&&er(h,C),N}function v(h,p,w,S){if(typeof w=="object"&&w!==null&&w.type===Ur&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case ui:e:{for(var N=w.key,E=p;E!==null;){if(E.key===N){if(N=w.type,N===Ur){if(E.tag===7){n(h,E.sibling),p=o(E,w.props.children),p.return=h,h=p;break e}}else if(E.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===vn&&lp(N)===E.type){n(h,E.sibling),p=o(E,w.props),p.ref=Fo(h,E,w),p.return=h,h=p;break e}n(h,E);break}else t(h,E);E=E.sibling}w.type===Ur?(p=dr(w.props.children,h.mode,S,w.key),p.return=h,h=p):(S=Qi(w.type,w.key,w.props,null,h.mode,S),S.ref=Fo(h,p,w),S.return=h,h=S)}return i(h);case Fr:e:{for(E=w.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===w.containerInfo&&p.stateNode.implementation===w.implementation){n(h,p.sibling),p=o(p,w.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=oc(w,h.mode,S),p.return=h,h=p}return i(h);case vn:return E=w._init,v(h,p,E(w._payload),S)}if(Zo(w))return x(h,p,w,S);if(Oo(w))return y(h,p,w,S);Si(h,w)}return typeof w=="string"&&w!==""||typeof w=="number"?(w=""+w,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,w),p.return=h,h=p):(n(h,p),p=rc(w,h.mode,S),p.return=h,h=p),i(h)):n(h,p)}return v}var mo=xg(!0),vg=xg(!1),Sl=Vn(null),bl=null,Xr=null,Sd=null;function bd(){Sd=Xr=bl=null}function Nd(e){var t=Sl.current;pe(Sl),e._currentValue=t}function ru(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function so(e,t){bl=e,Sd=Xr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(et=!0),e.firstContext=null)}function bt(e){var t=e._currentValue;if(Sd!==e)if(e={context:e,memoizedValue:t,next:null},Xr===null){if(bl===null)throw Error(H(308));Xr=e,bl.dependencies={lanes:0,firstContext:e}}else Xr=Xr.next=e;return t}var or=null;function Ed(e){or===null?or=[e]:or.push(e)}function wg(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Ed(t)):(n.next=o.next,o.next=n),t.interleaved=n,cn(e,r)}function cn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var wn=!1;function kd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function on(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $n(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,re&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,cn(e,n)}return o=r.interleaved,o===null?(t.next=t,Ed(r)):(t.next=o.next,o.next=t),r.interleaved=t,cn(e,n)}function Wi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ud(e,n)}}function ap(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Nl(e,t,n,r){var o=e.updateQueue;wn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,c=a.next;a.next=null,i===null?s=c:i.next=c,i=a;var u=e.alternate;u!==null&&(u=u.updateQueue,l=u.lastBaseUpdate,l!==i&&(l===null?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=a))}if(s!==null){var f=o.baseState;i=0,u=c=a=null,l=s;do{var m=l.lane,g=l.eventTime;if((r&m)===m){u!==null&&(u=u.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var x=e,y=l;switch(m=t,g=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){f=x.call(g,f,m);break e}f=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,m=typeof x=="function"?x.call(g,f,m):x,m==null)break e;f=ye({},f,m);break e;case 2:wn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,m=o.effects,m===null?o.effects=[l]:m.push(l))}else g={eventTime:g,lane:m,tag:l.tag,payload:l.payload,callback:l.callback,next:null},u===null?(c=u=g,a=f):u=u.next=g,i|=m;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;m=l,l=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(!0);if(u===null&&(a=f),o.baseState=a,o.firstBaseUpdate=c,o.lastBaseUpdate=u,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);xr|=i,e.lanes=i,e.memoizedState=f}}function cp(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(H(191,o));o.call(r)}}}var Js={},Ht=Vn(Js),js=Vn(Js),Rs=Vn(Js);function sr(e){if(e===Js)throw Error(H(174));return e}function _d(e,t){switch(ce(Rs,t),ce(js,e),ce(Ht,Js),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:zc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=zc(t,e)}pe(Ht),ce(Ht,t)}function go(){pe(Ht),pe(js),pe(Rs)}function bg(e){sr(Rs.current);var t=sr(Ht.current),n=zc(t,e.type);t!==n&&(ce(js,e),ce(Ht,n))}function Cd(e){js.current===e&&(pe(Ht),pe(js))}var me=Vn(0);function El(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Qa=[];function jd(){for(var e=0;e<Qa.length;e++)Qa[e]._workInProgressVersionPrimary=null;Qa.length=0}var qi=hn.ReactCurrentDispatcher,Za=hn.ReactCurrentBatchConfig,yr=0,ge=null,Ce=null,Ae=null,kl=!1,ds=!1,As=0,T1=0;function Ue(){throw Error(H(321))}function Rd(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!$t(e[n],t[n]))return!1;return!0}function Ad(e,t,n,r,o,s){if(yr=s,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,qi.current=e===null||e.memoizedState===null?O1:I1,e=n(r,o),ds){s=0;do{if(ds=!1,As=0,25<=s)throw Error(H(301));s+=1,Ae=Ce=null,t.updateQueue=null,qi.current=z1,e=n(r,o)}while(ds)}if(qi.current=_l,t=Ce!==null&&Ce.next!==null,yr=0,Ae=Ce=ge=null,kl=!1,t)throw Error(H(300));return e}function Td(){var e=As!==0;return As=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ae===null?ge.memoizedState=Ae=e:Ae=Ae.next=e,Ae}function Nt(){if(Ce===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Ae===null?ge.memoizedState:Ae.next;if(t!==null)Ae=t,Ce=e;else{if(e===null)throw Error(H(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Ae===null?ge.memoizedState=Ae=e:Ae=Ae.next=e}return Ae}function Ts(e,t){return typeof t=="function"?t(e):t}function Ja(e){var t=Nt(),n=t.queue;if(n===null)throw Error(H(311));n.lastRenderedReducer=e;var r=Ce,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var l=i=null,a=null,c=s;do{var u=c.lane;if((yr&u)===u)a!==null&&(a=a.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};a===null?(l=a=f,i=r):a=a.next=f,ge.lanes|=u,xr|=u}c=c.next}while(c!==null&&c!==s);a===null?i=r:a.next=l,$t(r,t.memoizedState)||(et=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ge.lanes|=s,xr|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ec(e){var t=Nt(),n=t.queue;if(n===null)throw Error(H(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);$t(s,t.memoizedState)||(et=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Ng(){}function Eg(e,t){var n=ge,r=Nt(),o=t(),s=!$t(r.memoizedState,o);if(s&&(r.memoizedState=o,et=!0),r=r.queue,Pd(Cg.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||Ae!==null&&Ae.memoizedState.tag&1){if(n.flags|=2048,Ps(9,_g.bind(null,n,r,o,t),void 0,null),Te===null)throw Error(H(349));yr&30||kg(n,t,o)}return o}function kg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function _g(e,t,n,r){t.value=n,t.getSnapshot=r,jg(t)&&Rg(e)}function Cg(e,t,n){return n(function(){jg(t)&&Rg(e)})}function jg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!$t(e,n)}catch{return!0}}function Rg(e){var t=cn(e,1);t!==null&&Pt(t,e,1,-1)}function up(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ts,lastRenderedState:e},t.queue=e,e=e.dispatch=$1.bind(null,ge,e),[t.memoizedState,e]}function Ps(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ag(){return Nt().memoizedState}function Yi(e,t,n,r){var o=Ft();ge.flags|=e,o.memoizedState=Ps(1|t,n,void 0,r===void 0?null:r)}function ta(e,t,n,r){var o=Nt();r=r===void 0?null:r;var s=void 0;if(Ce!==null){var i=Ce.memoizedState;if(s=i.destroy,r!==null&&Rd(r,i.deps)){o.memoizedState=Ps(t,n,s,r);return}}ge.flags|=e,o.memoizedState=Ps(1|t,n,s,r)}function dp(e,t){return Yi(8390656,8,e,t)}function Pd(e,t){return ta(2048,8,e,t)}function Tg(e,t){return ta(4,2,e,t)}function Pg(e,t){return ta(4,4,e,t)}function Mg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function $g(e,t,n){return n=n!=null?n.concat([e]):null,ta(4,4,Mg.bind(null,t,e),n)}function Md(){}function Og(e,t){var n=Nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Rd(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ig(e,t){var n=Nt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Rd(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zg(e,t,n){return yr&21?($t(n,t)||(n=Bm(),ge.lanes|=n,xr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,et=!0),e.memoizedState=n)}function P1(e,t){var n=le;le=n!==0&&4>n?n:4,e(!0);var r=Za.transition;Za.transition={};try{e(!1),t()}finally{le=n,Za.transition=r}}function Lg(){return Nt().memoizedState}function M1(e,t,n){var r=In(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Dg(e))Fg(t,n);else if(n=wg(e,t,n,r),n!==null){var o=Ke();Pt(n,e,r,o),Ug(n,t,r)}}function $1(e,t,n){var r=In(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Dg(e))Fg(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,l=s(i,n);if(o.hasEagerState=!0,o.eagerState=l,$t(l,i)){var a=t.interleaved;a===null?(o.next=o,Ed(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=wg(e,t,o,r),n!==null&&(o=Ke(),Pt(n,e,r,o),Ug(n,t,r))}}function Dg(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function Fg(e,t){ds=kl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ug(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ud(e,n)}}var _l={readContext:bt,useCallback:Ue,useContext:Ue,useEffect:Ue,useImperativeHandle:Ue,useInsertionEffect:Ue,useLayoutEffect:Ue,useMemo:Ue,useReducer:Ue,useRef:Ue,useState:Ue,useDebugValue:Ue,useDeferredValue:Ue,useTransition:Ue,useMutableSource:Ue,useSyncExternalStore:Ue,useId:Ue,unstable_isNewReconciler:!1},O1={readContext:bt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:bt,useEffect:dp,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Yi(4194308,4,Mg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Yi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Yi(4,2,e,t)},useMemo:function(e,t){var n=Ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=M1.bind(null,ge,e),[r.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:up,useDebugValue:Md,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=up(!1),t=e[0];return e=P1.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ge,o=Ft();if(he){if(n===void 0)throw Error(H(407));n=n()}else{if(n=t(),Te===null)throw Error(H(349));yr&30||kg(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,dp(Cg.bind(null,r,s,e),[e]),r.flags|=2048,Ps(9,_g.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Ft(),t=Te.identifierPrefix;if(he){var n=nn,r=tn;n=(r&~(1<<32-Tt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=As++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=T1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},I1={readContext:bt,useCallback:Og,useContext:bt,useEffect:Pd,useImperativeHandle:$g,useInsertionEffect:Tg,useLayoutEffect:Pg,useMemo:Ig,useReducer:Ja,useRef:Ag,useState:function(){return Ja(Ts)},useDebugValue:Md,useDeferredValue:function(e){var t=Nt();return zg(t,Ce.memoizedState,e)},useTransition:function(){var e=Ja(Ts)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Ng,useSyncExternalStore:Eg,useId:Lg,unstable_isNewReconciler:!1},z1={readContext:bt,useCallback:Og,useContext:bt,useEffect:Pd,useImperativeHandle:$g,useInsertionEffect:Tg,useLayoutEffect:Pg,useMemo:Ig,useReducer:ec,useRef:Ag,useState:function(){return ec(Ts)},useDebugValue:Md,useDeferredValue:function(e){var t=Nt();return Ce===null?t.memoizedState=e:zg(t,Ce.memoizedState,e)},useTransition:function(){var e=ec(Ts)[0],t=Nt().memoizedState;return[e,t]},useMutableSource:Ng,useSyncExternalStore:Eg,useId:Lg,unstable_isNewReconciler:!1};function _t(e,t){if(e&&e.defaultProps){t=ye({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ou(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ye({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var na={isMounted:function(e){return(e=e._reactInternals)?Er(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ke(),o=In(e),s=on(r,o);s.payload=t,n!=null&&(s.callback=n),t=$n(e,s,o),t!==null&&(Pt(t,e,o,r),Wi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ke(),o=In(e),s=on(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=$n(e,s,o),t!==null&&(Pt(t,e,o,r),Wi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ke(),r=In(e),o=on(n,r);o.tag=2,t!=null&&(o.callback=t),t=$n(e,o,r),t!==null&&(Pt(t,e,r,n),Wi(t,e,r))}};function fp(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!Es(n,r)||!Es(o,s):!0}function Bg(e,t,n){var r=!1,o=Bn,s=t.contextType;return typeof s=="object"&&s!==null?s=bt(s):(o=rt(t)?mr:We.current,r=t.contextTypes,s=(r=r!=null)?po(e,o):Bn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=na,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function pp(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&na.enqueueReplaceState(t,t.state,null)}function su(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},kd(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=bt(s):(s=rt(t)?mr:We.current,o.context=po(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(ou(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&na.enqueueReplaceState(o,o.state,null),Nl(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function yo(e,t){try{var n="",r=t;do n+=dw(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function tc(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function iu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var L1=typeof WeakMap=="function"?WeakMap:Map;function Hg(e,t,n){n=on(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){jl||(jl=!0,gu=r),iu(e,t)},n}function Vg(e,t,n){n=on(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){iu(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){iu(e,t),typeof r!="function"&&(On===null?On=new Set([this]):On.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function hp(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new L1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Z1.bind(null,e,t,n),t.then(e,e))}function mp(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function gp(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=on(-1,1),t.tag=2,$n(n,t,1))),n.lanes|=1),e)}var D1=hn.ReactCurrentOwner,et=!1;function Ge(e,t,n,r){t.child=e===null?vg(t,null,n,r):mo(t,e.child,n,r)}function yp(e,t,n,r,o){n=n.render;var s=t.ref;return so(t,o),r=Ad(e,t,n,r,s,o),n=Td(),e!==null&&!et?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,un(e,t,o)):(he&&n&&xd(t),t.flags|=1,Ge(e,t,r,o),t.child)}function xp(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Ud(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Wg(e,t,s,r,o)):(e=Qi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:Es,n(i,r)&&e.ref===t.ref)return un(e,t,o)}return t.flags|=1,e=zn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Wg(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(Es(s,r)&&e.ref===t.ref)if(et=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(et=!0);else return t.lanes=e.lanes,un(e,t,o)}return lu(e,t,n,r,o)}function qg(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ce(Qr,at),at|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ce(Qr,at),at|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ce(Qr,at),at|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ce(Qr,at),at|=r;return Ge(e,t,o,n),t.child}function Yg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function lu(e,t,n,r,o){var s=rt(n)?mr:We.current;return s=po(t,s),so(t,o),n=Ad(e,t,n,r,s,o),r=Td(),e!==null&&!et?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,un(e,t,o)):(he&&r&&xd(t),t.flags|=1,Ge(e,t,n,o),t.child)}function vp(e,t,n,r,o){if(rt(n)){var s=!0;xl(t)}else s=!1;if(so(t,o),t.stateNode===null)Gi(e,t),Bg(t,n,r),su(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,l=t.memoizedProps;i.props=l;var a=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=bt(c):(c=rt(n)?mr:We.current,c=po(t,c));var u=n.getDerivedStateFromProps,f=typeof u=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==r||a!==c)&&pp(t,i,r,c),wn=!1;var m=t.memoizedState;i.state=m,Nl(t,r,i,o),a=t.memoizedState,l!==r||m!==a||nt.current||wn?(typeof u=="function"&&(ou(t,n,u,r),a=t.memoizedState),(l=wn||fp(t,n,l,r,m,a,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),i.props=r,i.state=a,i.context=c,r=l):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Sg(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:_t(t.type,l),i.props=c,f=t.pendingProps,m=i.context,a=n.contextType,typeof a=="object"&&a!==null?a=bt(a):(a=rt(n)?mr:We.current,a=po(t,a));var g=n.getDerivedStateFromProps;(u=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==f||m!==a)&&pp(t,i,r,a),wn=!1,m=t.memoizedState,i.state=m,Nl(t,r,i,o);var x=t.memoizedState;l!==f||m!==x||nt.current||wn?(typeof g=="function"&&(ou(t,n,g,r),x=t.memoizedState),(c=wn||fp(t,n,c,r,m,x,a)||!1)?(u||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,x,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,x,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),i.props=r,i.state=x,i.context=a,r=c):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return au(e,t,n,r,s,o)}function au(e,t,n,r,o,s){Yg(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&op(t,n,!1),un(e,t,s);r=t.stateNode,D1.current=t;var l=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=mo(t,e.child,null,s),t.child=mo(t,null,l,s)):Ge(e,t,l,s),t.memoizedState=r.state,o&&op(t,n,!0),t.child}function Gg(e){var t=e.stateNode;t.pendingContext?rp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&rp(e,t.context,!1),_d(e,t.containerInfo)}function wp(e,t,n,r,o){return ho(),wd(o),t.flags|=256,Ge(e,t,n,r),t.child}var cu={dehydrated:null,treeContext:null,retryLane:0};function uu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Xg(e,t,n){var r=t.pendingProps,o=me.current,s=!1,i=(t.flags&128)!==0,l;if((l=i)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ce(me,o&1),e===null)return nu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=sa(i,r,0,null),e=dr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=uu(n),t.memoizedState=cu,e):$d(t,i));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return F1(e,t,i,r,l,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=zn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?s=zn(l,s):(s=dr(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?uu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=cu,r}return s=e.child,e=s.sibling,r=zn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function $d(e,t){return t=sa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function bi(e,t,n,r){return r!==null&&wd(r),mo(t,e.child,null,n),e=$d(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function F1(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=tc(Error(H(422))),bi(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=sa({mode:"visible",children:r.children},o,0,null),s=dr(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&mo(t,e.child,null,i),t.child.memoizedState=uu(i),t.memoizedState=cu,s);if(!(t.mode&1))return bi(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(H(419)),r=tc(s,r,void 0),bi(e,t,i,r)}if(l=(i&e.childLanes)!==0,et||l){if(r=Te,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,cn(e,o),Pt(r,e,o,-1))}return Fd(),r=tc(Error(H(421))),bi(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=J1.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,ut=Mn(o.nextSibling),dt=t,he=!0,Rt=null,e!==null&&(gt[yt++]=tn,gt[yt++]=nn,gt[yt++]=gr,tn=e.id,nn=e.overflow,gr=t),t=$d(t,r.children),t.flags|=4096,t)}function Sp(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ru(e.return,t,n)}function nc(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Kg(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Ge(e,t,r.children,n),r=me.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sp(e,n,t);else if(e.tag===19)Sp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ce(me,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&El(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),nc(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&El(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}nc(t,!0,n,null,s);break;case"together":nc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function un(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),xr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(H(153));if(t.child!==null){for(e=t.child,n=zn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=zn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function U1(e,t,n){switch(t.tag){case 3:Gg(t),ho();break;case 5:bg(t);break;case 1:rt(t.type)&&xl(t);break;case 4:_d(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ce(Sl,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ce(me,me.current&1),t.flags|=128,null):n&t.child.childLanes?Xg(e,t,n):(ce(me,me.current&1),e=un(e,t,n),e!==null?e.sibling:null);ce(me,me.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Kg(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ce(me,me.current),r)break;return null;case 22:case 23:return t.lanes=0,qg(e,t,n)}return un(e,t,n)}var Qg,du,Zg,Jg;Qg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};du=function(){};Zg=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,sr(Ht.current);var s=null;switch(n){case"input":o=Mc(e,o),r=Mc(e,r),s=[];break;case"select":o=ye({},o,{value:void 0}),r=ye({},r,{value:void 0}),s=[];break;case"textarea":o=Ic(e,o),r=Ic(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=gl)}Lc(n,r);var i;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var l=o[c];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(ys.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var a=r[c];if(l=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&a!==l&&(a!=null||l!=null))if(c==="style")if(l){for(i in l)!l.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in a)a.hasOwnProperty(i)&&l[i]!==a[i]&&(n||(n={}),n[i]=a[i])}else n||(s||(s=[]),s.push(c,n)),n=a;else c==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(c,a)):c==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(c,""+a):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(ys.hasOwnProperty(c)?(a!=null&&c==="onScroll"&&fe("scroll",e),s||l===a||(s=[])):(s=s||[]).push(c,a))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};Jg=function(e,t,n,r){n!==r&&(t.flags|=4)};function Uo(e,t){if(!he)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function B1(e,t,n){var r=t.pendingProps;switch(vd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Be(t),null;case 1:return rt(t.type)&&yl(),Be(t),null;case 3:return r=t.stateNode,go(),pe(nt),pe(We),jd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(wi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Rt!==null&&(vu(Rt),Rt=null))),du(e,t),Be(t),null;case 5:Cd(t);var o=sr(Rs.current);if(n=t.type,e!==null&&t.stateNode!=null)Zg(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(H(166));return Be(t),null}if(e=sr(Ht.current),wi(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Ut]=t,r[Cs]=s,e=(t.mode&1)!==0,n){case"dialog":fe("cancel",r),fe("close",r);break;case"iframe":case"object":case"embed":fe("load",r);break;case"video":case"audio":for(o=0;o<es.length;o++)fe(es[o],r);break;case"source":fe("error",r);break;case"img":case"image":case"link":fe("error",r),fe("load",r);break;case"details":fe("toggle",r);break;case"input":Af(r,s),fe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},fe("invalid",r);break;case"textarea":Pf(r,s),fe("invalid",r)}Lc(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&vi(r.textContent,l,e),o=["children",""+l]):ys.hasOwnProperty(i)&&l!=null&&i==="onScroll"&&fe("scroll",r)}switch(n){case"input":di(r),Tf(r,s,!0);break;case"textarea":di(r),Mf(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=gl)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Cm(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Ut]=t,e[Cs]=r,Qg(e,t,!1,!1),t.stateNode=e;e:{switch(i=Dc(n,r),n){case"dialog":fe("cancel",e),fe("close",e),o=r;break;case"iframe":case"object":case"embed":fe("load",e),o=r;break;case"video":case"audio":for(o=0;o<es.length;o++)fe(es[o],e);o=r;break;case"source":fe("error",e),o=r;break;case"img":case"image":case"link":fe("error",e),fe("load",e),o=r;break;case"details":fe("toggle",e),o=r;break;case"input":Af(e,r),o=Mc(e,r),fe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ye({},r,{value:void 0}),fe("invalid",e);break;case"textarea":Pf(e,r),o=Ic(e,r),fe("invalid",e);break;default:o=r}Lc(n,o),l=o;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?Am(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&jm(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&xs(e,a):typeof a=="number"&&xs(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(ys.hasOwnProperty(s)?a!=null&&s==="onScroll"&&fe("scroll",e):a!=null&&od(e,s,a,i))}switch(n){case"input":di(e),Tf(e,r,!1);break;case"textarea":di(e),Mf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Un(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?to(e,!!r.multiple,s,!1):r.defaultValue!=null&&to(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=gl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Be(t),null;case 6:if(e&&t.stateNode!=null)Jg(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(H(166));if(n=sr(Rs.current),sr(Ht.current),wi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ut]=t,(s=r.nodeValue!==n)&&(e=dt,e!==null))switch(e.tag){case 3:vi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&vi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ut]=t,t.stateNode=r}return Be(t),null;case 13:if(pe(me),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(he&&ut!==null&&t.mode&1&&!(t.flags&128))yg(),ho(),t.flags|=98560,s=!1;else if(s=wi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(H(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(H(317));s[Ut]=t}else ho(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Be(t),s=!1}else Rt!==null&&(vu(Rt),Rt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||me.current&1?je===0&&(je=3):Fd())),t.updateQueue!==null&&(t.flags|=4),Be(t),null);case 4:return go(),du(e,t),e===null&&ks(t.stateNode.containerInfo),Be(t),null;case 10:return Nd(t.type._context),Be(t),null;case 17:return rt(t.type)&&yl(),Be(t),null;case 19:if(pe(me),s=t.memoizedState,s===null)return Be(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Uo(s,!1);else{if(je!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=El(e),i!==null){for(t.flags|=128,Uo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ce(me,me.current&1|2),t.child}e=e.sibling}s.tail!==null&&Se()>xo&&(t.flags|=128,r=!0,Uo(s,!1),t.lanes=4194304)}else{if(!r)if(e=El(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Uo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!he)return Be(t),null}else 2*Se()-s.renderingStartTime>xo&&n!==1073741824&&(t.flags|=128,r=!0,Uo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Se(),t.sibling=null,n=me.current,ce(me,r?n&1|2:n&1),t):(Be(t),null);case 22:case 23:return Dd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?at&1073741824&&(Be(t),t.subtreeFlags&6&&(t.flags|=8192)):Be(t),null;case 24:return null;case 25:return null}throw Error(H(156,t.tag))}function H1(e,t){switch(vd(t),t.tag){case 1:return rt(t.type)&&yl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return go(),pe(nt),pe(We),jd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Cd(t),null;case 13:if(pe(me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(H(340));ho()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return pe(me),null;case 4:return go(),null;case 10:return Nd(t.type._context),null;case 22:case 23:return Dd(),null;case 24:return null;default:return null}}var Ni=!1,He=!1,V1=typeof WeakSet=="function"?WeakSet:Set,q=null;function Kr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){xe(e,t,r)}else n.current=null}function fu(e,t,n){try{n()}catch(r){xe(e,t,r)}}var bp=!1;function W1(e,t){if(Xc=pl,e=og(),yd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,l=-1,a=-1,c=0,u=0,f=e,m=null;t:for(;;){for(var g;f!==n||o!==0&&f.nodeType!==3||(l=i+o),f!==s||r!==0&&f.nodeType!==3||(a=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(g=f.firstChild)!==null;)m=f,f=g;for(;;){if(f===e)break t;if(m===n&&++c===o&&(l=i),m===s&&++u===r&&(a=i),(g=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Kc={focusedElem:e,selectionRange:n},pl=!1,q=t;q!==null;)if(t=q,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,q=e;else for(;q!==null;){t=q;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,v=x.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:_t(t.type,y),v);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var w=t.stateNode.containerInfo;w.nodeType===1?w.textContent="":w.nodeType===9&&w.documentElement&&w.removeChild(w.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(H(163))}}catch(S){xe(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,q=e;break}q=t.return}return x=bp,bp=!1,x}function fs(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&fu(t,n,s)}o=o.next}while(o!==r)}}function ra(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function e0(e){var t=e.alternate;t!==null&&(e.alternate=null,e0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ut],delete t[Cs],delete t[Jc],delete t[C1],delete t[j1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function t0(e){return e.tag===5||e.tag===3||e.tag===4}function Np(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||t0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=gl));else if(r!==4&&(e=e.child,e!==null))for(hu(e,t,n),e=e.sibling;e!==null;)hu(e,t,n),e=e.sibling}function mu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(mu(e,t,n),e=e.sibling;e!==null;)mu(e,t,n),e=e.sibling}var $e=null,Ct=!1;function mn(e,t,n){for(n=n.child;n!==null;)n0(e,t,n),n=n.sibling}function n0(e,t,n){if(Bt&&typeof Bt.onCommitFiberUnmount=="function")try{Bt.onCommitFiberUnmount(Xl,n)}catch{}switch(n.tag){case 5:He||Kr(n,t);case 6:var r=$e,o=Ct;$e=null,mn(e,t,n),$e=r,Ct=o,$e!==null&&(Ct?(e=$e,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):$e.removeChild(n.stateNode));break;case 18:$e!==null&&(Ct?(e=$e,n=n.stateNode,e.nodeType===8?Xa(e.parentNode,n):e.nodeType===1&&Xa(e,n),bs(e)):Xa($e,n.stateNode));break;case 4:r=$e,o=Ct,$e=n.stateNode.containerInfo,Ct=!0,mn(e,t,n),$e=r,Ct=o;break;case 0:case 11:case 14:case 15:if(!He&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&fu(n,t,i),o=o.next}while(o!==r)}mn(e,t,n);break;case 1:if(!He&&(Kr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){xe(n,t,l)}mn(e,t,n);break;case 21:mn(e,t,n);break;case 22:n.mode&1?(He=(r=He)||n.memoizedState!==null,mn(e,t,n),He=r):mn(e,t,n);break;default:mn(e,t,n)}}function Ep(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new V1),t.forEach(function(r){var o=eS.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function kt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,l=i;e:for(;l!==null;){switch(l.tag){case 5:$e=l.stateNode,Ct=!1;break e;case 3:$e=l.stateNode.containerInfo,Ct=!0;break e;case 4:$e=l.stateNode.containerInfo,Ct=!0;break e}l=l.return}if($e===null)throw Error(H(160));n0(s,i,o),$e=null,Ct=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(c){xe(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)r0(t,e),t=t.sibling}function r0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(kt(t,e),Lt(e),r&4){try{fs(3,e,e.return),ra(3,e)}catch(y){xe(e,e.return,y)}try{fs(5,e,e.return)}catch(y){xe(e,e.return,y)}}break;case 1:kt(t,e),Lt(e),r&512&&n!==null&&Kr(n,n.return);break;case 5:if(kt(t,e),Lt(e),r&512&&n!==null&&Kr(n,n.return),e.flags&32){var o=e.stateNode;try{xs(o,"")}catch(y){xe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&km(o,s),Dc(l,i);var c=Dc(l,s);for(i=0;i<a.length;i+=2){var u=a[i],f=a[i+1];u==="style"?Am(o,f):u==="dangerouslySetInnerHTML"?jm(o,f):u==="children"?xs(o,f):od(o,u,f,c)}switch(l){case"input":$c(o,s);break;case"textarea":_m(o,s);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?to(o,!!s.multiple,g,!1):m!==!!s.multiple&&(s.defaultValue!=null?to(o,!!s.multiple,s.defaultValue,!0):to(o,!!s.multiple,s.multiple?[]:"",!1))}o[Cs]=s}catch(y){xe(e,e.return,y)}}break;case 6:if(kt(t,e),Lt(e),r&4){if(e.stateNode===null)throw Error(H(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(y){xe(e,e.return,y)}}break;case 3:if(kt(t,e),Lt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{bs(t.containerInfo)}catch(y){xe(e,e.return,y)}break;case 4:kt(t,e),Lt(e);break;case 13:kt(t,e),Lt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(zd=Se())),r&4&&Ep(e);break;case 22:if(u=n!==null&&n.memoizedState!==null,e.mode&1?(He=(c=He)||u,kt(t,e),He=c):kt(t,e),Lt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!u&&e.mode&1)for(q=e,u=e.child;u!==null;){for(f=q=u;q!==null;){switch(m=q,g=m.child,m.tag){case 0:case 11:case 14:case 15:fs(4,m,m.return);break;case 1:Kr(m,m.return);var x=m.stateNode;if(typeof x.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){xe(r,n,y)}}break;case 5:Kr(m,m.return);break;case 22:if(m.memoizedState!==null){_p(f);continue}}g!==null?(g.return=m,q=g):_p(f)}u=u.sibling}e:for(u=null,f=e;;){if(f.tag===5){if(u===null){u=f;try{o=f.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,a=f.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Rm("display",i))}catch(y){xe(e,e.return,y)}}}else if(f.tag===6){if(u===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(y){xe(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;u===f&&(u=null),f=f.return}u===f&&(u=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:kt(t,e),Lt(e),r&4&&Ep(e);break;case 21:break;default:kt(t,e),Lt(e)}}function Lt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(t0(n)){var r=n;break e}n=n.return}throw Error(H(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(xs(o,""),r.flags&=-33);var s=Np(e);mu(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,l=Np(e);hu(e,l,i);break;default:throw Error(H(161))}}catch(a){xe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function q1(e,t,n){q=e,o0(e)}function o0(e,t,n){for(var r=(e.mode&1)!==0;q!==null;){var o=q,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Ni;if(!i){var l=o.alternate,a=l!==null&&l.memoizedState!==null||He;l=Ni;var c=He;if(Ni=i,(He=a)&&!c)for(q=o;q!==null;)i=q,a=i.child,i.tag===22&&i.memoizedState!==null?Cp(o):a!==null?(a.return=i,q=a):Cp(o);for(;s!==null;)q=s,o0(s),s=s.sibling;q=o,Ni=l,He=c}kp(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,q=s):kp(e)}}function kp(e){for(;q!==null;){var t=q;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:He||ra(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!He)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:_t(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&cp(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}cp(t,i,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var u=c.memoizedState;if(u!==null){var f=u.dehydrated;f!==null&&bs(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(H(163))}He||t.flags&512&&pu(t)}catch(m){xe(t,t.return,m)}}if(t===e){q=null;break}if(n=t.sibling,n!==null){n.return=t.return,q=n;break}q=t.return}}function _p(e){for(;q!==null;){var t=q;if(t===e){q=null;break}var n=t.sibling;if(n!==null){n.return=t.return,q=n;break}q=t.return}}function Cp(e){for(;q!==null;){var t=q;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ra(4,t)}catch(a){xe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){xe(t,o,a)}}var s=t.return;try{pu(t)}catch(a){xe(t,s,a)}break;case 5:var i=t.return;try{pu(t)}catch(a){xe(t,i,a)}}}catch(a){xe(t,t.return,a)}if(t===e){q=null;break}var l=t.sibling;if(l!==null){l.return=t.return,q=l;break}q=t.return}}var Y1=Math.ceil,Cl=hn.ReactCurrentDispatcher,Od=hn.ReactCurrentOwner,St=hn.ReactCurrentBatchConfig,re=0,Te=null,Ne=null,Ie=0,at=0,Qr=Vn(0),je=0,Ms=null,xr=0,oa=0,Id=0,ps=null,Je=null,zd=0,xo=1/0,Jt=null,jl=!1,gu=null,On=null,Ei=!1,jn=null,Rl=0,hs=0,yu=null,Xi=-1,Ki=0;function Ke(){return re&6?Se():Xi!==-1?Xi:Xi=Se()}function In(e){return e.mode&1?re&2&&Ie!==0?Ie&-Ie:A1.transition!==null?(Ki===0&&(Ki=Bm()),Ki):(e=le,e!==0||(e=window.event,e=e===void 0?16:Xm(e.type)),e):1}function Pt(e,t,n,r){if(50<hs)throw hs=0,yu=null,Error(H(185));Ks(e,n,r),(!(re&2)||e!==Te)&&(e===Te&&(!(re&2)&&(oa|=n),je===4&&En(e,Ie)),ot(e,r),n===1&&re===0&&!(t.mode&1)&&(xo=Se()+500,ea&&Wn()))}function ot(e,t){var n=e.callbackNode;Aw(e,t);var r=fl(e,e===Te?Ie:0);if(r===0)n!==null&&If(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&If(n),t===1)e.tag===0?R1(jp.bind(null,e)):hg(jp.bind(null,e)),k1(function(){!(re&6)&&Wn()}),n=null;else{switch(Hm(r)){case 1:n=cd;break;case 4:n=Fm;break;case 16:n=dl;break;case 536870912:n=Um;break;default:n=dl}n=f0(n,s0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function s0(e,t){if(Xi=-1,Ki=0,re&6)throw Error(H(327));var n=e.callbackNode;if(io()&&e.callbackNode!==n)return null;var r=fl(e,e===Te?Ie:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Al(e,r);else{t=r;var o=re;re|=2;var s=l0();(Te!==e||Ie!==t)&&(Jt=null,xo=Se()+500,ur(e,t));do try{K1();break}catch(l){i0(e,l)}while(!0);bd(),Cl.current=s,re=o,Ne!==null?t=0:(Te=null,Ie=0,t=je)}if(t!==0){if(t===2&&(o=Vc(e),o!==0&&(r=o,t=xu(e,o))),t===1)throw n=Ms,ur(e,0),En(e,r),ot(e,Se()),n;if(t===6)En(e,r);else{if(o=e.current.alternate,!(r&30)&&!G1(o)&&(t=Al(e,r),t===2&&(s=Vc(e),s!==0&&(r=s,t=xu(e,s))),t===1))throw n=Ms,ur(e,0),En(e,r),ot(e,Se()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(H(345));case 2:tr(e,Je,Jt);break;case 3:if(En(e,r),(r&130023424)===r&&(t=zd+500-Se(),10<t)){if(fl(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ke(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Zc(tr.bind(null,e,Je,Jt),t);break}tr(e,Je,Jt);break;case 4:if(En(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Tt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=Se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Y1(r/1960))-r,10<r){e.timeoutHandle=Zc(tr.bind(null,e,Je,Jt),r);break}tr(e,Je,Jt);break;case 5:tr(e,Je,Jt);break;default:throw Error(H(329))}}}return ot(e,Se()),e.callbackNode===n?s0.bind(null,e):null}function xu(e,t){var n=ps;return e.current.memoizedState.isDehydrated&&(ur(e,t).flags|=256),e=Al(e,t),e!==2&&(t=Je,Je=n,t!==null&&vu(t)),e}function vu(e){Je===null?Je=e:Je.push.apply(Je,e)}function G1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!$t(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function En(e,t){for(t&=~Id,t&=~oa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Tt(t),r=1<<n;e[n]=-1,t&=~r}}function jp(e){if(re&6)throw Error(H(327));io();var t=fl(e,0);if(!(t&1))return ot(e,Se()),null;var n=Al(e,t);if(e.tag!==0&&n===2){var r=Vc(e);r!==0&&(t=r,n=xu(e,r))}if(n===1)throw n=Ms,ur(e,0),En(e,t),ot(e,Se()),n;if(n===6)throw Error(H(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,tr(e,Je,Jt),ot(e,Se()),null}function Ld(e,t){var n=re;re|=1;try{return e(t)}finally{re=n,re===0&&(xo=Se()+500,ea&&Wn())}}function vr(e){jn!==null&&jn.tag===0&&!(re&6)&&io();var t=re;re|=1;var n=St.transition,r=le;try{if(St.transition=null,le=1,e)return e()}finally{le=r,St.transition=n,re=t,!(re&6)&&Wn()}}function Dd(){at=Qr.current,pe(Qr)}function ur(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,E1(n)),Ne!==null)for(n=Ne.return;n!==null;){var r=n;switch(vd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&yl();break;case 3:go(),pe(nt),pe(We),jd();break;case 5:Cd(r);break;case 4:go();break;case 13:pe(me);break;case 19:pe(me);break;case 10:Nd(r.type._context);break;case 22:case 23:Dd()}n=n.return}if(Te=e,Ne=e=zn(e.current,null),Ie=at=t,je=0,Ms=null,Id=oa=xr=0,Je=ps=null,or!==null){for(t=0;t<or.length;t++)if(n=or[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}or=null}return e}function i0(e,t){do{var n=Ne;try{if(bd(),qi.current=_l,kl){for(var r=ge.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}kl=!1}if(yr=0,Ae=Ce=ge=null,ds=!1,As=0,Od.current=null,n===null||n.return===null){je=1,Ms=t,Ne=null;break}e:{var s=e,i=n.return,l=n,a=t;if(t=Ie,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var c=a,u=l,f=u.tag;if(!(u.mode&1)&&(f===0||f===11||f===15)){var m=u.alternate;m?(u.updateQueue=m.updateQueue,u.memoizedState=m.memoizedState,u.lanes=m.lanes):(u.updateQueue=null,u.memoizedState=null)}var g=mp(i);if(g!==null){g.flags&=-257,gp(g,i,l,s,t),g.mode&1&&hp(s,c,t),t=g,a=c;var x=t.updateQueue;if(x===null){var y=new Set;y.add(a),t.updateQueue=y}else x.add(a);break e}else{if(!(t&1)){hp(s,c,t),Fd();break e}a=Error(H(426))}}else if(he&&l.mode&1){var v=mp(i);if(v!==null){!(v.flags&65536)&&(v.flags|=256),gp(v,i,l,s,t),wd(yo(a,l));break e}}s=a=yo(a,l),je!==4&&(je=2),ps===null?ps=[s]:ps.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var h=Hg(s,a,t);ap(s,h);break e;case 1:l=a;var p=s.type,w=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||w!==null&&typeof w.componentDidCatch=="function"&&(On===null||!On.has(w)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=Vg(s,l,t);ap(s,S);break e}}s=s.return}while(s!==null)}c0(n)}catch(N){t=N,Ne===n&&n!==null&&(Ne=n=n.return);continue}break}while(!0)}function l0(){var e=Cl.current;return Cl.current=_l,e===null?_l:e}function Fd(){(je===0||je===3||je===2)&&(je=4),Te===null||!(xr&268435455)&&!(oa&268435455)||En(Te,Ie)}function Al(e,t){var n=re;re|=2;var r=l0();(Te!==e||Ie!==t)&&(Jt=null,ur(e,t));do try{X1();break}catch(o){i0(e,o)}while(!0);if(bd(),re=n,Cl.current=r,Ne!==null)throw Error(H(261));return Te=null,Ie=0,je}function X1(){for(;Ne!==null;)a0(Ne)}function K1(){for(;Ne!==null&&!Sw();)a0(Ne)}function a0(e){var t=d0(e.alternate,e,at);e.memoizedProps=e.pendingProps,t===null?c0(e):Ne=t,Od.current=null}function c0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=H1(n,t),n!==null){n.flags&=32767,Ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{je=6,Ne=null;return}}else if(n=B1(n,t,at),n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);je===0&&(je=5)}function tr(e,t,n){var r=le,o=St.transition;try{St.transition=null,le=1,Q1(e,t,n,r)}finally{St.transition=o,le=r}return null}function Q1(e,t,n,r){do io();while(jn!==null);if(re&6)throw Error(H(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(H(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Tw(e,s),e===Te&&(Ne=Te=null,Ie=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ei||(Ei=!0,f0(dl,function(){return io(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=St.transition,St.transition=null;var i=le;le=1;var l=re;re|=4,Od.current=null,W1(e,n),r0(n,e),y1(Kc),pl=!!Xc,Kc=Xc=null,e.current=n,q1(n),bw(),re=l,le=i,St.transition=s}else e.current=n;if(Ei&&(Ei=!1,jn=e,Rl=o),s=e.pendingLanes,s===0&&(On=null),kw(n.stateNode),ot(e,Se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(jl)throw jl=!1,e=gu,gu=null,e;return Rl&1&&e.tag!==0&&io(),s=e.pendingLanes,s&1?e===yu?hs++:(hs=0,yu=e):hs=0,Wn(),null}function io(){if(jn!==null){var e=Hm(Rl),t=St.transition,n=le;try{if(St.transition=null,le=16>e?16:e,jn===null)var r=!1;else{if(e=jn,jn=null,Rl=0,re&6)throw Error(H(331));var o=re;for(re|=4,q=e.current;q!==null;){var s=q,i=s.child;if(q.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var c=l[a];for(q=c;q!==null;){var u=q;switch(u.tag){case 0:case 11:case 15:fs(8,u,s)}var f=u.child;if(f!==null)f.return=u,q=f;else for(;q!==null;){u=q;var m=u.sibling,g=u.return;if(e0(u),u===c){q=null;break}if(m!==null){m.return=g,q=m;break}q=g}}}var x=s.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var v=y.sibling;y.sibling=null,y=v}while(y!==null)}}q=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,q=i;else e:for(;q!==null;){if(s=q,s.flags&2048)switch(s.tag){case 0:case 11:case 15:fs(9,s,s.return)}var h=s.sibling;if(h!==null){h.return=s.return,q=h;break e}q=s.return}}var p=e.current;for(q=p;q!==null;){i=q;var w=i.child;if(i.subtreeFlags&2064&&w!==null)w.return=i,q=w;else e:for(i=p;q!==null;){if(l=q,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ra(9,l)}}catch(N){xe(l,l.return,N)}if(l===i){q=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,q=S;break e}q=l.return}}if(re=o,Wn(),Bt&&typeof Bt.onPostCommitFiberRoot=="function")try{Bt.onPostCommitFiberRoot(Xl,e)}catch{}r=!0}return r}finally{le=n,St.transition=t}}return!1}function Rp(e,t,n){t=yo(n,t),t=Hg(e,t,1),e=$n(e,t,1),t=Ke(),e!==null&&(Ks(e,1,t),ot(e,t))}function xe(e,t,n){if(e.tag===3)Rp(e,e,n);else for(;t!==null;){if(t.tag===3){Rp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(On===null||!On.has(r))){e=yo(n,e),e=Vg(t,e,1),t=$n(t,e,1),e=Ke(),t!==null&&(Ks(t,1,e),ot(t,e));break}}t=t.return}}function Z1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ke(),e.pingedLanes|=e.suspendedLanes&n,Te===e&&(Ie&n)===n&&(je===4||je===3&&(Ie&130023424)===Ie&&500>Se()-zd?ur(e,0):Id|=n),ot(e,t)}function u0(e,t){t===0&&(e.mode&1?(t=hi,hi<<=1,!(hi&130023424)&&(hi=4194304)):t=1);var n=Ke();e=cn(e,t),e!==null&&(Ks(e,t,n),ot(e,n))}function J1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),u0(e,n)}function eS(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(H(314))}r!==null&&r.delete(t),u0(e,n)}var d0;d0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||nt.current)et=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return et=!1,U1(e,t,n);et=!!(e.flags&131072)}else et=!1,he&&t.flags&1048576&&mg(t,wl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Gi(e,t),e=t.pendingProps;var o=po(t,We.current);so(t,n),o=Ad(null,t,r,e,o,n);var s=Td();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,rt(r)?(s=!0,xl(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,kd(t),o.updater=na,t.stateNode=o,o._reactInternals=t,su(t,r,e,n),t=au(null,t,r,!0,s,n)):(t.tag=0,he&&s&&xd(t),Ge(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Gi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=nS(r),e=_t(r,e),o){case 0:t=lu(null,t,r,e,n);break e;case 1:t=vp(null,t,r,e,n);break e;case 11:t=yp(null,t,r,e,n);break e;case 14:t=xp(null,t,r,_t(r.type,e),n);break e}throw Error(H(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),lu(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),vp(e,t,r,o,n);case 3:e:{if(Gg(t),e===null)throw Error(H(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Sg(e,t),Nl(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=yo(Error(H(423)),t),t=wp(e,t,r,n,o);break e}else if(r!==o){o=yo(Error(H(424)),t),t=wp(e,t,r,n,o);break e}else for(ut=Mn(t.stateNode.containerInfo.firstChild),dt=t,he=!0,Rt=null,n=vg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ho(),r===o){t=un(e,t,n);break e}Ge(e,t,r,n)}t=t.child}return t;case 5:return bg(t),e===null&&nu(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,Qc(r,o)?i=null:s!==null&&Qc(r,s)&&(t.flags|=32),Yg(e,t),Ge(e,t,i,n),t.child;case 6:return e===null&&nu(t),null;case 13:return Xg(e,t,n);case 4:return _d(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=mo(t,null,r,n):Ge(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),yp(e,t,r,o,n);case 7:return Ge(e,t,t.pendingProps,n),t.child;case 8:return Ge(e,t,t.pendingProps.children,n),t.child;case 12:return Ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ce(Sl,r._currentValue),r._currentValue=i,s!==null)if($t(s.value,i)){if(s.children===o.children&&!nt.current){t=un(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){i=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=on(-1,n&-n),a.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var u=c.pending;u===null?a.next=a:(a.next=u.next,u.next=a),c.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ru(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(H(341));i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ru(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Ge(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,so(t,n),o=bt(o),r=r(o),t.flags|=1,Ge(e,t,r,n),t.child;case 14:return r=t.type,o=_t(r,t.pendingProps),o=_t(r.type,o),xp(e,t,r,o,n);case 15:return Wg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),Gi(e,t),t.tag=1,rt(r)?(e=!0,xl(t)):e=!1,so(t,n),Bg(t,r,o),su(t,r,o,n),au(null,t,r,!0,e,n);case 19:return Kg(e,t,n);case 22:return qg(e,t,n)}throw Error(H(156,t.tag))};function f0(e,t){return Dm(e,t)}function tS(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(e,t,n,r){return new tS(e,t,n,r)}function Ud(e){return e=e.prototype,!(!e||!e.isReactComponent)}function nS(e){if(typeof e=="function")return Ud(e)?1:0;if(e!=null){if(e=e.$$typeof,e===id)return 11;if(e===ld)return 14}return 2}function zn(e,t){var n=e.alternate;return n===null?(n=vt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Qi(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Ud(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Ur:return dr(n.children,o,s,t);case sd:i=8,o|=8;break;case Rc:return e=vt(12,n,t,o|2),e.elementType=Rc,e.lanes=s,e;case Ac:return e=vt(13,n,t,o),e.elementType=Ac,e.lanes=s,e;case Tc:return e=vt(19,n,t,o),e.elementType=Tc,e.lanes=s,e;case bm:return sa(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wm:i=10;break e;case Sm:i=9;break e;case id:i=11;break e;case ld:i=14;break e;case vn:i=16,r=null;break e}throw Error(H(130,e==null?e:typeof e,""))}return t=vt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function dr(e,t,n,r){return e=vt(7,e,r,t),e.lanes=n,e}function sa(e,t,n,r){return e=vt(22,e,r,t),e.elementType=bm,e.lanes=n,e.stateNode={isHidden:!1},e}function rc(e,t,n){return e=vt(6,e,null,t),e.lanes=n,e}function oc(e,t,n){return t=vt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function rS(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=La(0),this.expirationTimes=La(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=La(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Bd(e,t,n,r,o,s,i,l,a){return e=new rS(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=vt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},kd(s),e}function oS(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Fr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function p0(e){if(!e)return Bn;e=e._reactInternals;e:{if(Er(e)!==e||e.tag!==1)throw Error(H(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(rt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(H(171))}if(e.tag===1){var n=e.type;if(rt(n))return pg(e,n,t)}return t}function h0(e,t,n,r,o,s,i,l,a){return e=Bd(n,r,!0,e,o,s,i,l,a),e.context=p0(null),n=e.current,r=Ke(),o=In(n),s=on(r,o),s.callback=t??null,$n(n,s,o),e.current.lanes=o,Ks(e,o,r),ot(e,r),e}function ia(e,t,n,r){var o=t.current,s=Ke(),i=In(o);return n=p0(n),t.context===null?t.context=n:t.pendingContext=n,t=on(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=$n(o,t,i),e!==null&&(Pt(e,o,i,s),Wi(e,o,i)),i}function Tl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ap(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Hd(e,t){Ap(e,t),(e=e.alternate)&&Ap(e,t)}function sS(){return null}var m0=typeof reportError=="function"?reportError:function(e){console.error(e)};function Vd(e){this._internalRoot=e}la.prototype.render=Vd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(H(409));ia(e,t,null,null)};la.prototype.unmount=Vd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;vr(function(){ia(null,e,null,null)}),t[an]=null}};function la(e){this._internalRoot=e}la.prototype.unstable_scheduleHydration=function(e){if(e){var t=qm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Nn.length&&t!==0&&t<Nn[n].priority;n++);Nn.splice(n,0,e),n===0&&Gm(e)}};function Wd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function aa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tp(){}function iS(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=Tl(i);s.call(c)}}var i=h0(t,r,e,0,null,!1,!1,"",Tp);return e._reactRootContainer=i,e[an]=i.current,ks(e.nodeType===8?e.parentNode:e),vr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var c=Tl(a);l.call(c)}}var a=Bd(e,0,!1,null,null,!1,!1,"",Tp);return e._reactRootContainer=a,e[an]=a.current,ks(e.nodeType===8?e.parentNode:e),vr(function(){ia(t,a,n,r)}),a}function ca(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var l=o;o=function(){var a=Tl(i);l.call(a)}}ia(t,i,e,o)}else i=iS(n,t,e,o,r);return Tl(i)}Vm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Jo(t.pendingLanes);n!==0&&(ud(t,n|1),ot(t,Se()),!(re&6)&&(xo=Se()+500,Wn()))}break;case 13:vr(function(){var r=cn(e,1);if(r!==null){var o=Ke();Pt(r,e,1,o)}}),Hd(e,1)}};dd=function(e){if(e.tag===13){var t=cn(e,134217728);if(t!==null){var n=Ke();Pt(t,e,134217728,n)}Hd(e,134217728)}};Wm=function(e){if(e.tag===13){var t=In(e),n=cn(e,t);if(n!==null){var r=Ke();Pt(n,e,t,r)}Hd(e,t)}};qm=function(){return le};Ym=function(e,t){var n=le;try{return le=e,t()}finally{le=n}};Uc=function(e,t,n){switch(t){case"input":if($c(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Jl(r);if(!o)throw Error(H(90));Em(r),$c(r,o)}}}break;case"textarea":_m(e,n);break;case"select":t=n.value,t!=null&&to(e,!!n.multiple,t,!1)}};Mm=Ld;$m=vr;var lS={usingClientEntryPoint:!1,Events:[Zs,Wr,Jl,Tm,Pm,Ld]},Bo={findFiberByHostInstance:rr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},aS={bundleType:Bo.bundleType,version:Bo.version,rendererPackageName:Bo.rendererPackageName,rendererConfig:Bo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:hn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zm(e),e===null?null:e.stateNode},findFiberByHostInstance:Bo.findFiberByHostInstance||sS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ki=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ki.isDisabled&&ki.supportsFiber)try{Xl=ki.inject(aS),Bt=ki}catch{}}ht.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lS;ht.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Wd(t))throw Error(H(200));return oS(e,t,null,n)};ht.createRoot=function(e,t){if(!Wd(e))throw Error(H(299));var n=!1,r="",o=m0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Bd(e,1,!1,null,null,n,!1,r,o),e[an]=t.current,ks(e.nodeType===8?e.parentNode:e),new Vd(t)};ht.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(H(188)):(e=Object.keys(e).join(","),Error(H(268,e)));return e=zm(t),e=e===null?null:e.stateNode,e};ht.flushSync=function(e){return vr(e)};ht.hydrate=function(e,t,n){if(!aa(t))throw Error(H(200));return ca(null,e,t,!0,n)};ht.hydrateRoot=function(e,t,n){if(!Wd(e))throw Error(H(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=m0;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=h0(t,null,e,1,n??null,o,!1,s,i),e[an]=t.current,ks(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new la(t)};ht.render=function(e,t,n){if(!aa(t))throw Error(H(200));return ca(null,e,t,!1,n)};ht.unmountComponentAtNode=function(e){if(!aa(e))throw Error(H(40));return e._reactRootContainer?(vr(function(){ca(null,null,e,!1,function(){e._reactRootContainer=null,e[an]=null})}),!0):!1};ht.unstable_batchedUpdates=Ld;ht.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!aa(n))throw Error(H(200));if(e==null||e._reactInternals===void 0)throw Error(H(38));return ca(e,t,n,!1,r)};ht.version="18.3.1-next-f1338f8080-20240426";function g0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(g0)}catch(e){console.error(e)}}g0(),gm.exports=ht;var cS=gm.exports,Pp=cS;Cc.createRoot=Pp.createRoot,Cc.hydrateRoot=Pp.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $s(){return $s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$s.apply(this,arguments)}var Rn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Rn||(Rn={}));const Mp="popstate";function uS(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:l}=r.location;return wu("",{pathname:s,search:i,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Pl(o)}return fS(t,n,null,e)}function ke(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function y0(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function dS(){return Math.random().toString(36).substr(2,8)}function $p(e,t){return{usr:e.state,key:e.key,idx:t}}function wu(e,t,n,r){return n===void 0&&(n=null),$s({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Co(t):t,{state:n,key:t&&t.key||r||dS()})}function Pl(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Co(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fS(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,l=Rn.Pop,a=null,c=u();c==null&&(c=0,i.replaceState($s({},i.state,{idx:c}),""));function u(){return(i.state||{idx:null}).idx}function f(){l=Rn.Pop;let v=u(),h=v==null?null:v-c;c=v,a&&a({action:l,location:y.location,delta:h})}function m(v,h){l=Rn.Push;let p=wu(y.location,v,h);c=u()+1;let w=$p(p,c),S=y.createHref(p);try{i.pushState(w,"",S)}catch(N){if(N instanceof DOMException&&N.name==="DataCloneError")throw N;o.location.assign(S)}s&&a&&a({action:l,location:y.location,delta:1})}function g(v,h){l=Rn.Replace;let p=wu(y.location,v,h);c=u();let w=$p(p,c),S=y.createHref(p);i.replaceState(w,"",S),s&&a&&a({action:l,location:y.location,delta:0})}function x(v){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof v=="string"?v:Pl(v);return p=p.replace(/ $/,"%20"),ke(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let y={get action(){return l},get location(){return e(o,i)},listen(v){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(Mp,f),a=v,()=>{o.removeEventListener(Mp,f),a=null}},createHref(v){return t(o,v)},createURL:x,encodeLocation(v){let h=x(v);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:m,replace:g,go(v){return i.go(v)}};return y}var Op;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Op||(Op={}));function pS(e,t,n){return n===void 0&&(n="/"),hS(e,t,n)}function hS(e,t,n,r){let o=typeof t=="string"?Co(t):t,s=qd(o.pathname||"/",n);if(s==null)return null;let i=x0(e);mS(i);let l=null;for(let a=0;l==null&&a<i.length;++a){let c=CS(s);l=ES(i[a],c)}return l}function x0(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,l)=>{let a={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};a.relativePath.startsWith("/")&&(ke(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let c=Ln([r,a.relativePath]),u=n.concat(a);s.children&&s.children.length>0&&(ke(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),x0(s.children,t,u,c)),!(s.path==null&&!s.index)&&t.push({path:c,score:bS(c,s.index),routesMeta:u})};return e.forEach((s,i)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))o(s,i);else for(let a of v0(s.path))o(s,i,a)}),t}function v0(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=v0(r.join("/")),l=[];return l.push(...i.map(a=>a===""?s:[s,a].join("/"))),o&&l.push(...i),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function mS(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:NS(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const gS=/^:[\w-]+$/,yS=3,xS=2,vS=1,wS=10,SS=-2,Ip=e=>e==="*";function bS(e,t){let n=e.split("/"),r=n.length;return n.some(Ip)&&(r+=SS),t&&(r+=xS),n.filter(o=>!Ip(o)).reduce((o,s)=>o+(gS.test(s)?yS:s===""?vS:wS),r)}function NS(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function ES(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let l=0;l<r.length;++l){let a=r[l],c=l===r.length-1,u=s==="/"?t:t.slice(s.length)||"/",f=kS({path:a.relativePath,caseSensitive:a.caseSensitive,end:c},u),m=a.route;if(!f)return null;Object.assign(o,f.params),i.push({params:o,pathname:Ln([s,f.pathname]),pathnameBase:TS(Ln([s,f.pathnameBase])),route:m}),f.pathnameBase!=="/"&&(s=Ln([s,f.pathnameBase]))}return i}function kS(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=_S(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((c,u,f)=>{let{paramName:m,isOptional:g}=u;if(m==="*"){let y=l[f]||"";i=s.slice(0,s.length-y.length).replace(/(.)\/+$/,"$1")}const x=l[f];return g&&!x?c[m]=void 0:c[m]=(x||"").replace(/%2F/g,"/"),c},{}),pathname:s,pathnameBase:i,pattern:e}}function _S(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),y0(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function CS(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return y0(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function qd(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function jS(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Co(e):e;return{pathname:n?n.startsWith("/")?n:RS(n,t):t,search:PS(r),hash:MS(o)}}function RS(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function sc(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function AS(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function w0(e,t){let n=AS(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function S0(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Co(e):(o=$s({},e),ke(!o.pathname||!o.pathname.includes("?"),sc("?","pathname","search",o)),ke(!o.pathname||!o.pathname.includes("#"),sc("#","pathname","hash",o)),ke(!o.search||!o.search.includes("#"),sc("#","search","hash",o)));let s=e===""||o.pathname==="",i=s?"/":o.pathname,l;if(i==null)l=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let m=i.split("/");for(;m[0]==="..";)m.shift(),f-=1;o.pathname=m.join("/")}l=f>=0?t[f]:"/"}let a=jS(o,l),c=i&&i!=="/"&&i.endsWith("/"),u=(s||i===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(c||u)&&(a.pathname+="/"),a}const Ln=e=>e.join("/").replace(/\/\/+/g,"/"),TS=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),PS=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,MS=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function $S(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const b0=["post","put","patch","delete"];new Set(b0);const OS=["get",...b0];new Set(OS);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Os(){return Os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Os.apply(this,arguments)}const Yd=b.createContext(null),IS=b.createContext(null),kr=b.createContext(null),ua=b.createContext(null),qn=b.createContext({outlet:null,matches:[],isDataRoute:!1}),N0=b.createContext(null);function zS(e,t){let{relative:n}=t===void 0?{}:t;ei()||ke(!1);let{basename:r,navigator:o}=b.useContext(kr),{hash:s,pathname:i,search:l}=k0(e,{relative:n}),a=i;return r!=="/"&&(a=i==="/"?r:Ln([r,i])),o.createHref({pathname:a,search:l,hash:s})}function ei(){return b.useContext(ua)!=null}function ti(){return ei()||ke(!1),b.useContext(ua).location}function E0(e){b.useContext(kr).static||b.useLayoutEffect(e)}function LS(){let{isDataRoute:e}=b.useContext(qn);return e?ZS():DS()}function DS(){ei()||ke(!1);let e=b.useContext(Yd),{basename:t,future:n,navigator:r}=b.useContext(kr),{matches:o}=b.useContext(qn),{pathname:s}=ti(),i=JSON.stringify(w0(o,n.v7_relativeSplatPath)),l=b.useRef(!1);return E0(()=>{l.current=!0}),b.useCallback(function(c,u){if(u===void 0&&(u={}),!l.current)return;if(typeof c=="number"){r.go(c);return}let f=S0(c,JSON.parse(i),s,u.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Ln([t,f.pathname])),(u.replace?r.replace:r.push)(f,u.state,u)},[t,r,i,s,e])}function FS(){let{matches:e}=b.useContext(qn),t=e[e.length-1];return t?t.params:{}}function k0(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=b.useContext(kr),{matches:o}=b.useContext(qn),{pathname:s}=ti(),i=JSON.stringify(w0(o,r.v7_relativeSplatPath));return b.useMemo(()=>S0(e,JSON.parse(i),s,n==="path"),[e,i,s,n])}function US(e,t){return BS(e,t)}function BS(e,t,n,r){ei()||ke(!1);let{navigator:o}=b.useContext(kr),{matches:s}=b.useContext(qn),i=s[s.length-1],l=i?i.params:{};i&&i.pathname;let a=i?i.pathnameBase:"/";i&&i.route;let c=ti(),u;if(t){var f;let v=typeof t=="string"?Co(t):t;a==="/"||(f=v.pathname)!=null&&f.startsWith(a)||ke(!1),u=v}else u=c;let m=u.pathname||"/",g=m;if(a!=="/"){let v=a.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(v.length).join("/")}let x=pS(e,{pathname:g}),y=YS(x&&x.map(v=>Object.assign({},v,{params:Object.assign({},l,v.params),pathname:Ln([a,o.encodeLocation?o.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?a:Ln([a,o.encodeLocation?o.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),s,n,r);return t&&y?b.createElement(ua.Provider,{value:{location:Os({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:Rn.Pop}},y):y}function HS(){let e=QS(),t=$S(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return b.createElement(b.Fragment,null,b.createElement("h2",null,"Unexpected Application Error!"),b.createElement("h3",{style:{fontStyle:"italic"}},t),n?b.createElement("pre",{style:o},n):null,null)}const VS=b.createElement(HS,null);class WS extends b.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?b.createElement(qn.Provider,{value:this.props.routeContext},b.createElement(N0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function qS(e){let{routeContext:t,match:n,children:r}=e,o=b.useContext(Yd);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),b.createElement(qn.Provider,{value:t},r)}function YS(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let u=i.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);u>=0||ke(!1),i=i.slice(0,Math.min(i.length,u+1))}let a=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let u=0;u<i.length;u++){let f=i[u];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(c=u),f.route.id){let{loaderData:m,errors:g}=n,x=f.route.loader&&m[f.route.id]===void 0&&(!g||g[f.route.id]===void 0);if(f.route.lazy||x){a=!0,c>=0?i=i.slice(0,c+1):i=[i[0]];break}}}return i.reduceRight((u,f,m)=>{let g,x=!1,y=null,v=null;n&&(g=l&&f.route.id?l[f.route.id]:void 0,y=f.route.errorElement||VS,a&&(c<0&&m===0?(JS("route-fallback"),x=!0,v=null):c===m&&(x=!0,v=f.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,m+1)),p=()=>{let w;return g?w=y:x?w=v:f.route.Component?w=b.createElement(f.route.Component,null):f.route.element?w=f.route.element:w=u,b.createElement(qS,{match:f,routeContext:{outlet:u,matches:h,isDataRoute:n!=null},children:w})};return n&&(f.route.ErrorBoundary||f.route.errorElement||m===0)?b.createElement(WS,{location:n.location,revalidation:n.revalidation,component:y,error:g,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var _0=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(_0||{}),C0=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(C0||{});function GS(e){let t=b.useContext(Yd);return t||ke(!1),t}function XS(e){let t=b.useContext(IS);return t||ke(!1),t}function KS(e){let t=b.useContext(qn);return t||ke(!1),t}function j0(e){let t=KS(),n=t.matches[t.matches.length-1];return n.route.id||ke(!1),n.route.id}function QS(){var e;let t=b.useContext(N0),n=XS(),r=j0();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function ZS(){let{router:e}=GS(_0.UseNavigateStable),t=j0(C0.UseNavigateStable),n=b.useRef(!1);return E0(()=>{n.current=!0}),b.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,Os({fromRouteId:t},s)))},[e,t])}const zp={};function JS(e,t,n){zp[e]||(zp[e]=!0)}function eb(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function zr(e){ke(!1)}function tb(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Rn.Pop,navigator:s,static:i=!1,future:l}=e;ei()&&ke(!1);let a=t.replace(/^\/*/,"/"),c=b.useMemo(()=>({basename:a,navigator:s,static:i,future:Os({v7_relativeSplatPath:!1},l)}),[a,l,s,i]);typeof r=="string"&&(r=Co(r));let{pathname:u="/",search:f="",hash:m="",state:g=null,key:x="default"}=r,y=b.useMemo(()=>{let v=qd(u,a);return v==null?null:{location:{pathname:v,search:f,hash:m,state:g,key:x},navigationType:o}},[a,u,f,m,g,x,o]);return y==null?null:b.createElement(kr.Provider,{value:c},b.createElement(ua.Provider,{children:n,value:y}))}function nb(e){let{children:t,location:n}=e;return US(Su(t),n)}new Promise(()=>{});function Su(e,t){t===void 0&&(t=[]);let n=[];return b.Children.forEach(e,(r,o)=>{if(!b.isValidElement(r))return;let s=[...t,o];if(r.type===b.Fragment){n.push.apply(n,Su(r.props.children,s));return}r.type!==zr&&ke(!1),!r.props.index||!r.props.children||ke(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=Su(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function bu(){return bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bu.apply(this,arguments)}function rb(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function ob(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function sb(e,t){return e.button===0&&(!t||t==="_self")&&!ob(e)}const ib=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],lb="6";try{window.__reactRouterVersion=lb}catch{}const ab="startTransition",Lp=Zv[ab];function cb(e){let{basename:t,children:n,future:r,window:o}=e,s=b.useRef();s.current==null&&(s.current=uS({window:o,v5Compat:!0}));let i=s.current,[l,a]=b.useState({action:i.action,location:i.location}),{v7_startTransition:c}=r||{},u=b.useCallback(f=>{c&&Lp?Lp(()=>a(f)):a(f)},[a,c]);return b.useLayoutEffect(()=>i.listen(u),[i,u]),b.useEffect(()=>eb(r),[r]),b.createElement(tb,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i,future:r})}const ub=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",db=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ml=b.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:i,state:l,target:a,to:c,preventScrollReset:u,viewTransition:f}=t,m=rb(t,ib),{basename:g}=b.useContext(kr),x,y=!1;if(typeof c=="string"&&db.test(c)&&(x=c,ub))try{let w=new URL(window.location.href),S=c.startsWith("//")?new URL(w.protocol+c):new URL(c),N=qd(S.pathname,g);S.origin===w.origin&&N!=null?c=N+S.search+S.hash:y=!0}catch{}let v=zS(c,{relative:o}),h=fb(c,{replace:i,state:l,target:a,preventScrollReset:u,relative:o,viewTransition:f});function p(w){r&&r(w),w.defaultPrevented||h(w)}return b.createElement("a",bu({},m,{href:x||v,onClick:y||s?r:p,ref:n,target:a}))});var Dp;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Dp||(Dp={}));var Fp;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Fp||(Fp={}));function fb(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:i,viewTransition:l}=t===void 0?{}:t,a=LS(),c=ti(),u=k0(e,{relative:i});return b.useCallback(f=>{if(sb(f,n)){f.preventDefault();let m=r!==void 0?r:Pl(c)===Pl(u);a(e,{replace:m,state:o,preventScrollReset:s,relative:i,viewTransition:l})}},[c,a,u,r,o,n,e,s,i,l])}let pb={data:""},hb=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||pb,mb=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,gb=/\/\*[^]*?\*\/|  +/g,Up=/\n+/g,kn=(e,t)=>{let n="",r="",o="";for(let s in e){let i=e[s];s[0]=="@"?s[1]=="i"?n=s+" "+i+";":r+=s[1]=="f"?kn(i,s):s+"{"+kn(i,s[1]=="k"?"":t)+"}":typeof i=="object"?r+=kn(i,t?t.replace(/([^,])+/g,l=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,a=>/&/.test(a)?a.replace(/&/g,l):l?l+" "+a:a)):s):i!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=kn.p?kn.p(s,i):s+":"+i+";")}return n+(t&&o?t+"{"+o+"}":o)+r},Kt={},R0=e=>{if(typeof e=="object"){let t="";for(let n in e)t+=n+R0(e[n]);return t}return e},yb=(e,t,n,r,o)=>{let s=R0(e),i=Kt[s]||(Kt[s]=(a=>{let c=0,u=11;for(;c<a.length;)u=101*u+a.charCodeAt(c++)>>>0;return"go"+u})(s));if(!Kt[i]){let a=s!==e?e:(c=>{let u,f,m=[{}];for(;u=mb.exec(c.replace(gb,""));)u[4]?m.shift():u[3]?(f=u[3].replace(Up," ").trim(),m.unshift(m[0][f]=m[0][f]||{})):m[0][u[1]]=u[2].replace(Up," ").trim();return m[0]})(e);Kt[i]=kn(o?{["@keyframes "+i]:a}:a,n?"":"."+i)}let l=n&&Kt.g?Kt.g:null;return n&&(Kt.g=Kt[i]),((a,c,u,f)=>{f?c.data=c.data.replace(f,a):c.data.indexOf(a)===-1&&(c.data=u?a+c.data:c.data+a)})(Kt[i],t,r,l),i},xb=(e,t,n)=>e.reduce((r,o,s)=>{let i=t[s];if(i&&i.call){let l=i(n),a=l&&l.props&&l.props.className||/^go/.test(l)&&l;i=a?"."+a:l&&typeof l=="object"?l.props?"":kn(l,""):l===!1?"":l}return r+o+(i??"")},"");function da(e){let t=this||{},n=e.call?e(t.p):e;return yb(n.unshift?n.raw?xb(n,[].slice.call(arguments,1),t.p):n.reduce((r,o)=>Object.assign(r,o&&o.call?o(t.p):o),{}):n,hb(t.target),t.g,t.o,t.k)}let A0,Nu,Eu;da.bind({g:1});let dn=da.bind({k:1});function vb(e,t,n,r){kn.p=t,A0=e,Nu=n,Eu=r}function Yn(e,t){let n=this||{};return function(){let r=arguments;function o(s,i){let l=Object.assign({},s),a=l.className||o.className;n.p=Object.assign({theme:Nu&&Nu()},l),n.o=/ *go\d+/.test(a),l.className=da.apply(n,r)+(a?" "+a:"");let c=e;return e[0]&&(c=l.as||e,delete l.as),Eu&&c[0]&&Eu(l),A0(c,l)}return o}}var wb=e=>typeof e=="function",$l=(e,t)=>wb(e)?e(t):e,Sb=(()=>{let e=0;return()=>(++e).toString()})(),T0=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),bb=20,P0=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,bb)};case 1:return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case 2:let{toast:n}=t;return P0(e,{type:e.toasts.find(s=>s.id===n.id)?1:0,toast:n});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(s=>s.id===r||r===void 0?{...s,dismissed:!0,visible:!1}:s)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let o=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+o}))}}},Zi=[],ir={toasts:[],pausedAt:void 0},_r=e=>{ir=P0(ir,e),Zi.forEach(t=>{t(ir)})},Nb={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Eb=(e={})=>{let[t,n]=b.useState(ir),r=b.useRef(ir);b.useEffect(()=>(r.current!==ir&&n(ir),Zi.push(n),()=>{let s=Zi.indexOf(n);s>-1&&Zi.splice(s,1)}),[]);let o=t.toasts.map(s=>{var i,l,a;return{...e,...e[s.type],...s,removeDelay:s.removeDelay||((i=e[s.type])==null?void 0:i.removeDelay)||(e==null?void 0:e.removeDelay),duration:s.duration||((l=e[s.type])==null?void 0:l.duration)||(e==null?void 0:e.duration)||Nb[s.type],style:{...e.style,...(a=e[s.type])==null?void 0:a.style,...s.style}}});return{...t,toasts:o}},kb=(e,t="blank",n)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(n==null?void 0:n.id)||Sb()}),ni=e=>(t,n)=>{let r=kb(t,e,n);return _r({type:2,toast:r}),r.id},Xe=(e,t)=>ni("blank")(e,t);Xe.error=ni("error");Xe.success=ni("success");Xe.loading=ni("loading");Xe.custom=ni("custom");Xe.dismiss=e=>{_r({type:3,toastId:e})};Xe.remove=e=>_r({type:4,toastId:e});Xe.promise=(e,t,n)=>{let r=Xe.loading(t.loading,{...n,...n==null?void 0:n.loading});return typeof e=="function"&&(e=e()),e.then(o=>{let s=t.success?$l(t.success,o):void 0;return s?Xe.success(s,{id:r,...n,...n==null?void 0:n.success}):Xe.dismiss(r),o}).catch(o=>{let s=t.error?$l(t.error,o):void 0;s?Xe.error(s,{id:r,...n,...n==null?void 0:n.error}):Xe.dismiss(r)}),e};var _b=(e,t)=>{_r({type:1,toast:{id:e,height:t}})},Cb=()=>{_r({type:5,time:Date.now()})},ms=new Map,jb=1e3,Rb=(e,t=jb)=>{if(ms.has(e))return;let n=setTimeout(()=>{ms.delete(e),_r({type:4,toastId:e})},t);ms.set(e,n)},Ab=e=>{let{toasts:t,pausedAt:n}=Eb(e);b.useEffect(()=>{if(n)return;let s=Date.now(),i=t.map(l=>{if(l.duration===1/0)return;let a=(l.duration||0)+l.pauseDuration-(s-l.createdAt);if(a<0){l.visible&&Xe.dismiss(l.id);return}return setTimeout(()=>Xe.dismiss(l.id),a)});return()=>{i.forEach(l=>l&&clearTimeout(l))}},[t,n]);let r=b.useCallback(()=>{n&&_r({type:6,time:Date.now()})},[n]),o=b.useCallback((s,i)=>{let{reverseOrder:l=!1,gutter:a=8,defaultPosition:c}=i||{},u=t.filter(g=>(g.position||c)===(s.position||c)&&g.height),f=u.findIndex(g=>g.id===s.id),m=u.filter((g,x)=>x<f&&g.visible).length;return u.filter(g=>g.visible).slice(...l?[m+1]:[0,m]).reduce((g,x)=>g+(x.height||0)+a,0)},[t]);return b.useEffect(()=>{t.forEach(s=>{if(s.dismissed)Rb(s.id,s.removeDelay);else{let i=ms.get(s.id);i&&(clearTimeout(i),ms.delete(s.id))}})},[t]),{toasts:t,handlers:{updateHeight:_b,startPause:Cb,endPause:r,calculateOffset:o}}},Tb=dn`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Pb=dn`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Mb=dn`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,$b=Yn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Tb} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Pb} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Mb} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Ob=dn`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Ib=Yn("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Ob} 1s linear infinite;
`,zb=dn`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Lb=dn`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Db=Yn("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${zb} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Lb} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Fb=Yn("div")`
  position: absolute;
`,Ub=Yn("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Bb=dn`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Hb=Yn("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Bb} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Vb=({toast:e})=>{let{icon:t,type:n,iconTheme:r}=e;return t!==void 0?typeof t=="string"?b.createElement(Hb,null,t):t:n==="blank"?null:b.createElement(Ub,null,b.createElement(Ib,{...r}),n!=="loading"&&b.createElement(Fb,null,n==="error"?b.createElement($b,{...r}):b.createElement(Db,{...r})))},Wb=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,qb=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Yb="0%{opacity:0;} 100%{opacity:1;}",Gb="0%{opacity:1;} 100%{opacity:0;}",Xb=Yn("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Kb=Yn("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Qb=(e,t)=>{let n=e.includes("top")?1:-1,[r,o]=T0()?[Yb,Gb]:[Wb(n),qb(n)];return{animation:t?`${dn(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${dn(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Zb=b.memo(({toast:e,position:t,style:n,children:r})=>{let o=e.height?Qb(e.position||t||"top-center",e.visible):{opacity:0},s=b.createElement(Vb,{toast:e}),i=b.createElement(Kb,{...e.ariaProps},$l(e.message,e));return b.createElement(Xb,{className:e.className,style:{...o,...n,...e.style}},typeof r=="function"?r({icon:s,message:i}):b.createElement(b.Fragment,null,s,i))});vb(b.createElement);var Jb=({id:e,className:t,style:n,onHeightUpdate:r,children:o})=>{let s=b.useCallback(i=>{if(i){let l=()=>{let a=i.getBoundingClientRect().height;r(e,a)};l(),new MutationObserver(l).observe(i,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return b.createElement("div",{ref:s,className:t,style:n},o)},e2=(e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},o=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:T0()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...o}},t2=da`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,_i=16,n2=({reverseOrder:e,position:t="top-center",toastOptions:n,gutter:r,children:o,containerStyle:s,containerClassName:i})=>{let{toasts:l,handlers:a}=Ab(n);return b.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:_i,left:_i,right:_i,bottom:_i,pointerEvents:"none",...s},className:i,onMouseEnter:a.startPause,onMouseLeave:a.endPause},l.map(c=>{let u=c.position||t,f=a.calculateOffset(c,{reverseOrder:e,gutter:r,defaultPosition:t}),m=e2(u,f);return b.createElement(Jb,{id:c.id,key:c.id,onHeightUpdate:a.updateHeight,className:c.visible?t2:"",style:m},c.type==="custom"?$l(c.message,c):o?o(c):b.createElement(Zb,{toast:c,position:u}))}))},Oe=Xe;function Le(e){if(typeof e=="string"||typeof e=="number")return""+e;let t="";if(Array.isArray(e))for(let n=0,r;n<e.length;n++)(r=Le(e[n]))!==""&&(t+=(t&&" ")+r);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var M0={exports:{}},$0={},O0={exports:{}},I0={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vo=b;function r2(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var o2=typeof Object.is=="function"?Object.is:r2,s2=vo.useState,i2=vo.useEffect,l2=vo.useLayoutEffect,a2=vo.useDebugValue;function c2(e,t){var n=t(),r=s2({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return l2(function(){o.value=n,o.getSnapshot=t,ic(o)&&s({inst:o})},[e,n,t]),i2(function(){return ic(o)&&s({inst:o}),e(function(){ic(o)&&s({inst:o})})},[e]),a2(n),n}function ic(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o2(e,n)}catch{return!0}}function u2(e,t){return t()}var d2=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u2:c2;I0.useSyncExternalStore=vo.useSyncExternalStore!==void 0?vo.useSyncExternalStore:d2;O0.exports=I0;var f2=O0.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fa=b,p2=f2;function h2(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var m2=typeof Object.is=="function"?Object.is:h2,g2=p2.useSyncExternalStore,y2=fa.useRef,x2=fa.useEffect,v2=fa.useMemo,w2=fa.useDebugValue;$0.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var s=y2(null);if(s.current===null){var i={hasValue:!1,value:null};s.current=i}else i=s.current;s=v2(function(){function a(g){if(!c){if(c=!0,u=g,g=r(g),o!==void 0&&i.hasValue){var x=i.value;if(o(x,g))return f=x}return f=g}if(x=f,m2(u,g))return x;var y=r(g);return o!==void 0&&o(x,y)?(u=g,x):(u=g,f=y)}var c=!1,u,f,m=n===void 0?null:n;return[function(){return a(t())},m===null?void 0:function(){return a(m())}]},[t,n,r,o]);var l=g2(e,s[0],s[1]);return x2(function(){i.hasValue=!0,i.value=l},[l]),w2(l),l};M0.exports=$0;var S2=M0.exports;const z0=om(S2),b2={},Bp=e=>{let t;const n=new Set,r=(u,f)=>{const m=typeof u=="function"?u(t):u;if(!Object.is(m,t)){const g=t;t=f??(typeof m!="object"||m===null)?m:Object.assign({},t,m),n.forEach(x=>x(t,g))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>c,subscribe:u=>(n.add(u),()=>n.delete(u)),destroy:()=>{(b2?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,o,a);return a},L0=e=>e?Bp(e):Bp,{useDebugValue:N2}=I,{useSyncExternalStoreWithSelector:E2}=z0,k2=e=>e;function D0(e,t=k2,n){const r=E2(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return N2(r),r}const Hp=(e,t)=>{const n=L0(e),r=(o,s=t)=>D0(n,o,s);return Object.assign(r,n),r},_2=(e,t)=>e?Hp(e,t):Hp;function Pe(e,t){if(Object.is(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[r,o]of e)if(!Object.is(o,t.get(r)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const r of e)if(!t.has(r))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var C2={value:()=>{}};function pa(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Ji(n)}function Ji(e){this._=e}function j2(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Ji.prototype=pa.prototype={constructor:Ji,on:function(e,t){var n=this._,r=j2(e+"",n),o,s=-1,i=r.length;if(arguments.length<2){for(;++s<i;)if((o=(e=r[s]).type)&&(o=R2(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++s<i;)if(o=(e=r[s]).type)n[o]=Vp(n[o],e.name,t);else if(t==null)for(o in n)n[o]=Vp(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Ji(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,s;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(s=this._[e],r=0,o=s.length;r<o;++r)s[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,s=r.length;o<s;++o)r[o].value.apply(t,n)}};function R2(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function Vp(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=C2,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var ku="http://www.w3.org/1999/xhtml";const Wp={svg:"http://www.w3.org/2000/svg",xhtml:ku,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ha(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Wp.hasOwnProperty(t)?{space:Wp[t],local:e}:e}function A2(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===ku&&t.documentElement.namespaceURI===ku?t.createElement(e):t.createElementNS(n,e)}}function T2(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function F0(e){var t=ha(e);return(t.local?T2:A2)(t)}function P2(){}function Gd(e){return e==null?P2:function(){return this.querySelector(e)}}function M2(e){typeof e!="function"&&(e=Gd(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var s=t[o],i=s.length,l=r[o]=new Array(i),a,c,u=0;u<i;++u)(a=s[u])&&(c=e.call(a,a.__data__,u,s))&&("__data__"in a&&(c.__data__=a.__data__),l[u]=c);return new pt(r,this._parents)}function $2(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function O2(){return[]}function U0(e){return e==null?O2:function(){return this.querySelectorAll(e)}}function I2(e){return function(){return $2(e.apply(this,arguments))}}function z2(e){typeof e=="function"?e=I2(e):e=U0(e);for(var t=this._groups,n=t.length,r=[],o=[],s=0;s<n;++s)for(var i=t[s],l=i.length,a,c=0;c<l;++c)(a=i[c])&&(r.push(e.call(a,a.__data__,c,i)),o.push(a));return new pt(r,o)}function B0(e){return function(){return this.matches(e)}}function H0(e){return function(t){return t.matches(e)}}var L2=Array.prototype.find;function D2(e){return function(){return L2.call(this.children,e)}}function F2(){return this.firstElementChild}function U2(e){return this.select(e==null?F2:D2(typeof e=="function"?e:H0(e)))}var B2=Array.prototype.filter;function H2(){return Array.from(this.children)}function V2(e){return function(){return B2.call(this.children,e)}}function W2(e){return this.selectAll(e==null?H2:V2(typeof e=="function"?e:H0(e)))}function q2(e){typeof e!="function"&&(e=B0(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var s=t[o],i=s.length,l=r[o]=[],a,c=0;c<i;++c)(a=s[c])&&e.call(a,a.__data__,c,s)&&l.push(a);return new pt(r,this._parents)}function V0(e){return new Array(e.length)}function Y2(){return new pt(this._enter||this._groups.map(V0),this._parents)}function Ol(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Ol.prototype={constructor:Ol,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function G2(e){return function(){return e}}function X2(e,t,n,r,o,s){for(var i=0,l,a=t.length,c=s.length;i<c;++i)(l=t[i])?(l.__data__=s[i],r[i]=l):n[i]=new Ol(e,s[i]);for(;i<a;++i)(l=t[i])&&(o[i]=l)}function K2(e,t,n,r,o,s,i){var l,a,c=new Map,u=t.length,f=s.length,m=new Array(u),g;for(l=0;l<u;++l)(a=t[l])&&(m[l]=g=i.call(a,a.__data__,l,t)+"",c.has(g)?o[l]=a:c.set(g,a));for(l=0;l<f;++l)g=i.call(e,s[l],l,s)+"",(a=c.get(g))?(r[l]=a,a.__data__=s[l],c.delete(g)):n[l]=new Ol(e,s[l]);for(l=0;l<u;++l)(a=t[l])&&c.get(m[l])===a&&(o[l]=a)}function Q2(e){return e.__data__}function Z2(e,t){if(!arguments.length)return Array.from(this,Q2);var n=t?K2:X2,r=this._parents,o=this._groups;typeof e!="function"&&(e=G2(e));for(var s=o.length,i=new Array(s),l=new Array(s),a=new Array(s),c=0;c<s;++c){var u=r[c],f=o[c],m=f.length,g=J2(e.call(u,u&&u.__data__,c,r)),x=g.length,y=l[c]=new Array(x),v=i[c]=new Array(x),h=a[c]=new Array(m);n(u,f,y,v,h,g,t);for(var p=0,w=0,S,N;p<x;++p)if(S=y[p]){for(p>=w&&(w=p+1);!(N=v[w])&&++w<x;);S._next=N||null}}return i=new pt(i,r),i._enter=l,i._exit=a,i}function J2(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function eN(){return new pt(this._exit||this._groups.map(V0),this._parents)}function tN(e,t,n){var r=this.enter(),o=this,s=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?s.remove():n(s),r&&o?r.merge(o).order():o}function nN(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,s=r.length,i=Math.min(o,s),l=new Array(o),a=0;a<i;++a)for(var c=n[a],u=r[a],f=c.length,m=l[a]=new Array(f),g,x=0;x<f;++x)(g=c[x]||u[x])&&(m[x]=g);for(;a<o;++a)l[a]=n[a];return new pt(l,this._parents)}function rN(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,s=r[o],i;--o>=0;)(i=r[o])&&(s&&i.compareDocumentPosition(s)^4&&s.parentNode.insertBefore(i,s),s=i);return this}function oN(e){e||(e=sN);function t(f,m){return f&&m?e(f.__data__,m.__data__):!f-!m}for(var n=this._groups,r=n.length,o=new Array(r),s=0;s<r;++s){for(var i=n[s],l=i.length,a=o[s]=new Array(l),c,u=0;u<l;++u)(c=i[u])&&(a[u]=c);a.sort(t)}return new pt(o,this._parents).order()}function sN(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function iN(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function lN(){return Array.from(this)}function aN(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,s=r.length;o<s;++o){var i=r[o];if(i)return i}return null}function cN(){let e=0;for(const t of this)++e;return e}function uN(){return!this.node()}function dN(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],s=0,i=o.length,l;s<i;++s)(l=o[s])&&e.call(l,l.__data__,s,o);return this}function fN(e){return function(){this.removeAttribute(e)}}function pN(e){return function(){this.removeAttributeNS(e.space,e.local)}}function hN(e,t){return function(){this.setAttribute(e,t)}}function mN(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function gN(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function yN(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function xN(e,t){var n=ha(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?pN:fN:typeof t=="function"?n.local?yN:gN:n.local?mN:hN)(n,t))}function W0(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function vN(e){return function(){this.style.removeProperty(e)}}function wN(e,t,n){return function(){this.style.setProperty(e,t,n)}}function SN(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function bN(e,t,n){return arguments.length>1?this.each((t==null?vN:typeof t=="function"?SN:wN)(e,t,n??"")):wo(this.node(),e)}function wo(e,t){return e.style.getPropertyValue(t)||W0(e).getComputedStyle(e,null).getPropertyValue(t)}function NN(e){return function(){delete this[e]}}function EN(e,t){return function(){this[e]=t}}function kN(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function _N(e,t){return arguments.length>1?this.each((t==null?NN:typeof t=="function"?kN:EN)(e,t)):this.node()[e]}function q0(e){return e.trim().split(/^|\s+/)}function Xd(e){return e.classList||new Y0(e)}function Y0(e){this._node=e,this._names=q0(e.getAttribute("class")||"")}Y0.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function G0(e,t){for(var n=Xd(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function X0(e,t){for(var n=Xd(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function CN(e){return function(){G0(this,e)}}function jN(e){return function(){X0(this,e)}}function RN(e,t){return function(){(t.apply(this,arguments)?G0:X0)(this,e)}}function AN(e,t){var n=q0(e+"");if(arguments.length<2){for(var r=Xd(this.node()),o=-1,s=n.length;++o<s;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?RN:t?CN:jN)(n,t))}function TN(){this.textContent=""}function PN(e){return function(){this.textContent=e}}function MN(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function $N(e){return arguments.length?this.each(e==null?TN:(typeof e=="function"?MN:PN)(e)):this.node().textContent}function ON(){this.innerHTML=""}function IN(e){return function(){this.innerHTML=e}}function zN(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function LN(e){return arguments.length?this.each(e==null?ON:(typeof e=="function"?zN:IN)(e)):this.node().innerHTML}function DN(){this.nextSibling&&this.parentNode.appendChild(this)}function FN(){return this.each(DN)}function UN(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function BN(){return this.each(UN)}function HN(e){var t=typeof e=="function"?e:F0(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function VN(){return null}function WN(e,t){var n=typeof e=="function"?e:F0(e),r=t==null?VN:typeof t=="function"?t:Gd(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function qN(){var e=this.parentNode;e&&e.removeChild(this)}function YN(){return this.each(qN)}function GN(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function XN(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function KN(e){return this.select(e?XN:GN)}function QN(e){return arguments.length?this.property("__data__",e):this.node().__data__}function ZN(e){return function(t){e.call(this,t,this.__data__)}}function JN(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function eE(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,s;n<o;++n)s=t[n],(!e.type||s.type===e.type)&&s.name===e.name?this.removeEventListener(s.type,s.listener,s.options):t[++r]=s;++r?t.length=r:delete this.__on}}}function tE(e,t,n){return function(){var r=this.__on,o,s=ZN(t);if(r){for(var i=0,l=r.length;i<l;++i)if((o=r[i]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=s,o.options=n),o.value=t;return}}this.addEventListener(e.type,s,n),o={type:e.type,name:e.name,value:t,listener:s,options:n},r?r.push(o):this.__on=[o]}}function nE(e,t,n){var r=JN(e+""),o,s=r.length,i;if(arguments.length<2){var l=this.node().__on;if(l){for(var a=0,c=l.length,u;a<c;++a)for(o=0,u=l[a];o<s;++o)if((i=r[o]).type===u.type&&i.name===u.name)return u.value}return}for(l=t?tE:eE,o=0;o<s;++o)this.each(l(r[o],t,n));return this}function K0(e,t,n){var r=W0(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function rE(e,t){return function(){return K0(this,e,t)}}function oE(e,t){return function(){return K0(this,e,t.apply(this,arguments))}}function sE(e,t){return this.each((typeof t=="function"?oE:rE)(e,t))}function*iE(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,s=r.length,i;o<s;++o)(i=r[o])&&(yield i)}var Q0=[null];function pt(e,t){this._groups=e,this._parents=t}function ri(){return new pt([[document.documentElement]],Q0)}function lE(){return this}pt.prototype=ri.prototype={constructor:pt,select:M2,selectAll:z2,selectChild:U2,selectChildren:W2,filter:q2,data:Z2,enter:Y2,exit:eN,join:tN,merge:nN,selection:lE,order:rN,sort:oN,call:iN,nodes:lN,node:aN,size:cN,empty:uN,each:dN,attr:xN,style:bN,property:_N,classed:AN,text:$N,html:LN,raise:FN,lower:BN,append:HN,insert:WN,remove:YN,clone:KN,datum:QN,on:nE,dispatch:sE,[Symbol.iterator]:iE};function xt(e){return typeof e=="string"?new pt([[document.querySelector(e)]],[document.documentElement]):new pt([[e]],Q0)}function aE(e){let t;for(;t=e.sourceEvent;)e=t;return e}function jt(e,t){if(e=aE(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,r=r.matrixTransform(t.getScreenCTM().inverse()),[r.x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const cE={passive:!1},Is={capture:!0,passive:!1};function lc(e){e.stopImmediatePropagation()}function lo(e){e.preventDefault(),e.stopImmediatePropagation()}function Z0(e){var t=e.document.documentElement,n=xt(e).on("dragstart.drag",lo,Is);"onselectstart"in t?n.on("selectstart.drag",lo,Is):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function J0(e,t){var n=e.document.documentElement,r=xt(e).on("dragstart.drag",null);t&&(r.on("click.drag",lo,Is),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const Ci=e=>()=>e;function _u(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:s,x:i,y:l,dx:a,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:s,enumerable:!0,configurable:!0},x:{value:i,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:a,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}_u.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function uE(e){return!e.ctrlKey&&!e.button}function dE(){return this.parentNode}function fE(e,t){return t??{x:e.x,y:e.y}}function pE(){return navigator.maxTouchPoints||"ontouchstart"in this}function hE(){var e=uE,t=dE,n=fE,r=pE,o={},s=pa("start","drag","end"),i=0,l,a,c,u,f=0;function m(S){S.on("mousedown.drag",g).filter(r).on("touchstart.drag",v).on("touchmove.drag",h,cE).on("touchend.drag touchcancel.drag",p).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(S,N){if(!(u||!e.call(this,S,N))){var E=w(this,t.call(this,S,N),S,N,"mouse");E&&(xt(S.view).on("mousemove.drag",x,Is).on("mouseup.drag",y,Is),Z0(S.view),lc(S),c=!1,l=S.clientX,a=S.clientY,E("start",S))}}function x(S){if(lo(S),!c){var N=S.clientX-l,E=S.clientY-a;c=N*N+E*E>f}o.mouse("drag",S)}function y(S){xt(S.view).on("mousemove.drag mouseup.drag",null),J0(S.view,c),lo(S),o.mouse("end",S)}function v(S,N){if(e.call(this,S,N)){var E=S.changedTouches,_=t.call(this,S,N),C=E.length,T,P;for(T=0;T<C;++T)(P=w(this,_,S,N,E[T].identifier,E[T]))&&(lc(S),P("start",S,E[T]))}}function h(S){var N=S.changedTouches,E=N.length,_,C;for(_=0;_<E;++_)(C=o[N[_].identifier])&&(lo(S),C("drag",S,N[_]))}function p(S){var N=S.changedTouches,E=N.length,_,C;for(u&&clearTimeout(u),u=setTimeout(function(){u=null},500),_=0;_<E;++_)(C=o[N[_].identifier])&&(lc(S),C("end",S,N[_]))}function w(S,N,E,_,C,T){var P=s.copy(),D=jt(T||E,N),U,B,k;if((k=n.call(S,new _u("beforestart",{sourceEvent:E,target:m,identifier:C,active:i,x:D[0],y:D[1],dx:0,dy:0,dispatch:P}),_))!=null)return U=k.x-D[0]||0,B=k.y-D[1]||0,function A(R,F,M){var j=D,O;switch(R){case"start":o[C]=A,O=i++;break;case"end":delete o[C],--i;case"drag":D=jt(M||F,N),O=i;break}P.call(R,S,new _u(R,{sourceEvent:F,subject:k,target:m,identifier:C,active:O,x:D[0]+U,y:D[1]+B,dx:D[0]-j[0],dy:D[1]-j[1],dispatch:P}),_)}}return m.filter=function(S){return arguments.length?(e=typeof S=="function"?S:Ci(!!S),m):e},m.container=function(S){return arguments.length?(t=typeof S=="function"?S:Ci(S),m):t},m.subject=function(S){return arguments.length?(n=typeof S=="function"?S:Ci(S),m):n},m.touchable=function(S){return arguments.length?(r=typeof S=="function"?S:Ci(!!S),m):r},m.on=function(){var S=s.on.apply(s,arguments);return S===s?m:S},m.clickDistance=function(S){return arguments.length?(f=(S=+S)*S,m):Math.sqrt(f)},m}function Kd(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function ey(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function oi(){}var zs=.7,Il=1/zs,ao="\\s*([+-]?\\d+)\\s*",Ls="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Vt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",mE=/^#([0-9a-f]{3,8})$/,gE=new RegExp(`^rgb\\(${ao},${ao},${ao}\\)$`),yE=new RegExp(`^rgb\\(${Vt},${Vt},${Vt}\\)$`),xE=new RegExp(`^rgba\\(${ao},${ao},${ao},${Ls}\\)$`),vE=new RegExp(`^rgba\\(${Vt},${Vt},${Vt},${Ls}\\)$`),wE=new RegExp(`^hsl\\(${Ls},${Vt},${Vt}\\)$`),SE=new RegExp(`^hsla\\(${Ls},${Vt},${Vt},${Ls}\\)$`),qp={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Kd(oi,Ds,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Yp,formatHex:Yp,formatHex8:bE,formatHsl:NE,formatRgb:Gp,toString:Gp});function Yp(){return this.rgb().formatHex()}function bE(){return this.rgb().formatHex8()}function NE(){return ty(this).formatHsl()}function Gp(){return this.rgb().formatRgb()}function Ds(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=mE.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Xp(t):n===3?new tt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?ji(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?ji(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=gE.exec(e))?new tt(t[1],t[2],t[3],1):(t=yE.exec(e))?new tt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=xE.exec(e))?ji(t[1],t[2],t[3],t[4]):(t=vE.exec(e))?ji(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=wE.exec(e))?Zp(t[1],t[2]/100,t[3]/100,1):(t=SE.exec(e))?Zp(t[1],t[2]/100,t[3]/100,t[4]):qp.hasOwnProperty(e)?Xp(qp[e]):e==="transparent"?new tt(NaN,NaN,NaN,0):null}function Xp(e){return new tt(e>>16&255,e>>8&255,e&255,1)}function ji(e,t,n,r){return r<=0&&(e=t=n=NaN),new tt(e,t,n,r)}function EE(e){return e instanceof oi||(e=Ds(e)),e?(e=e.rgb(),new tt(e.r,e.g,e.b,e.opacity)):new tt}function Cu(e,t,n,r){return arguments.length===1?EE(e):new tt(e,t,n,r??1)}function tt(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}Kd(tt,Cu,ey(oi,{brighter(e){return e=e==null?Il:Math.pow(Il,e),new tt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?zs:Math.pow(zs,e),new tt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new tt(fr(this.r),fr(this.g),fr(this.b),zl(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kp,formatHex:Kp,formatHex8:kE,formatRgb:Qp,toString:Qp}));function Kp(){return`#${lr(this.r)}${lr(this.g)}${lr(this.b)}`}function kE(){return`#${lr(this.r)}${lr(this.g)}${lr(this.b)}${lr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Qp(){const e=zl(this.opacity);return`${e===1?"rgb(":"rgba("}${fr(this.r)}, ${fr(this.g)}, ${fr(this.b)}${e===1?")":`, ${e})`}`}function zl(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function fr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function lr(e){return e=fr(e),(e<16?"0":"")+e.toString(16)}function Zp(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new At(e,t,n,r)}function ty(e){if(e instanceof At)return new At(e.h,e.s,e.l,e.opacity);if(e instanceof oi||(e=Ds(e)),!e)return new At;if(e instanceof At)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),s=Math.max(t,n,r),i=NaN,l=s-o,a=(s+o)/2;return l?(t===s?i=(n-r)/l+(n<r)*6:n===s?i=(r-t)/l+2:i=(t-n)/l+4,l/=a<.5?s+o:2-s-o,i*=60):l=a>0&&a<1?0:i,new At(i,l,a,e.opacity)}function _E(e,t,n,r){return arguments.length===1?ty(e):new At(e,t,n,r??1)}function At(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}Kd(At,_E,ey(oi,{brighter(e){return e=e==null?Il:Math.pow(Il,e),new At(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?zs:Math.pow(zs,e),new At(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new tt(ac(e>=240?e-240:e+120,o,r),ac(e,o,r),ac(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new At(Jp(this.h),Ri(this.s),Ri(this.l),zl(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=zl(this.opacity);return`${e===1?"hsl(":"hsla("}${Jp(this.h)}, ${Ri(this.s)*100}%, ${Ri(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Jp(e){return e=(e||0)%360,e<0?e+360:e}function Ri(e){return Math.max(0,Math.min(1,e||0))}function ac(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const ny=e=>()=>e;function CE(e,t){return function(n){return e+n*t}}function jE(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function RE(e){return(e=+e)==1?ry:function(t,n){return n-t?jE(t,n,e):ny(isNaN(t)?n:t)}}function ry(e,t){var n=t-e;return n?CE(e,n):ny(isNaN(e)?t:e)}const eh=function e(t){var n=RE(t);function r(o,s){var i=n((o=Cu(o)).r,(s=Cu(s)).r),l=n(o.g,s.g),a=n(o.b,s.b),c=ry(o.opacity,s.opacity);return function(u){return o.r=i(u),o.g=l(u),o.b=a(u),o.opacity=c(u),o+""}}return r.gamma=e,r}(1);function Sn(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var ju=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,cc=new RegExp(ju.source,"g");function AE(e){return function(){return e}}function TE(e){return function(t){return e(t)+""}}function PE(e,t){var n=ju.lastIndex=cc.lastIndex=0,r,o,s,i=-1,l=[],a=[];for(e=e+"",t=t+"";(r=ju.exec(e))&&(o=cc.exec(t));)(s=o.index)>n&&(s=t.slice(n,s),l[i]?l[i]+=s:l[++i]=s),(r=r[0])===(o=o[0])?l[i]?l[i]+=o:l[++i]=o:(l[++i]=null,a.push({i,x:Sn(r,o)})),n=cc.lastIndex;return n<t.length&&(s=t.slice(n),l[i]?l[i]+=s:l[++i]=s),l.length<2?a[0]?TE(a[0].x):AE(t):(t=a.length,function(c){for(var u=0,f;u<t;++u)l[(f=a[u]).i]=f.x(c);return l.join("")})}var th=180/Math.PI,Ru={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function oy(e,t,n,r,o,s){var i,l,a;return(i=Math.sqrt(e*e+t*t))&&(e/=i,t/=i),(a=e*n+t*r)&&(n-=e*a,r-=t*a),(l=Math.sqrt(n*n+r*r))&&(n/=l,r/=l,a/=l),e*r<t*n&&(e=-e,t=-t,a=-a,i=-i),{translateX:o,translateY:s,rotate:Math.atan2(t,e)*th,skewX:Math.atan(a)*th,scaleX:i,scaleY:l}}var Ai;function ME(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Ru:oy(t.a,t.b,t.c,t.d,t.e,t.f)}function $E(e){return e==null||(Ai||(Ai=document.createElementNS("http://www.w3.org/2000/svg","g")),Ai.setAttribute("transform",e),!(e=Ai.transform.baseVal.consolidate()))?Ru:(e=e.matrix,oy(e.a,e.b,e.c,e.d,e.e,e.f))}function sy(e,t,n,r){function o(c){return c.length?c.pop()+" ":""}function s(c,u,f,m,g,x){if(c!==f||u!==m){var y=g.push("translate(",null,t,null,n);x.push({i:y-4,x:Sn(c,f)},{i:y-2,x:Sn(u,m)})}else(f||m)&&g.push("translate("+f+t+m+n)}function i(c,u,f,m){c!==u?(c-u>180?u+=360:u-c>180&&(c+=360),m.push({i:f.push(o(f)+"rotate(",null,r)-2,x:Sn(c,u)})):u&&f.push(o(f)+"rotate("+u+r)}function l(c,u,f,m){c!==u?m.push({i:f.push(o(f)+"skewX(",null,r)-2,x:Sn(c,u)}):u&&f.push(o(f)+"skewX("+u+r)}function a(c,u,f,m,g,x){if(c!==f||u!==m){var y=g.push(o(g)+"scale(",null,",",null,")");x.push({i:y-4,x:Sn(c,f)},{i:y-2,x:Sn(u,m)})}else(f!==1||m!==1)&&g.push(o(g)+"scale("+f+","+m+")")}return function(c,u){var f=[],m=[];return c=e(c),u=e(u),s(c.translateX,c.translateY,u.translateX,u.translateY,f,m),i(c.rotate,u.rotate,f,m),l(c.skewX,u.skewX,f,m),a(c.scaleX,c.scaleY,u.scaleX,u.scaleY,f,m),c=u=null,function(g){for(var x=-1,y=m.length,v;++x<y;)f[(v=m[x]).i]=v.x(g);return f.join("")}}}var OE=sy(ME,"px, ","px)","deg)"),IE=sy($E,", ",")",")"),zE=1e-12;function nh(e){return((e=Math.exp(e))+1/e)/2}function LE(e){return((e=Math.exp(e))-1/e)/2}function DE(e){return((e=Math.exp(2*e))-1)/(e+1)}const FE=function e(t,n,r){function o(s,i){var l=s[0],a=s[1],c=s[2],u=i[0],f=i[1],m=i[2],g=u-l,x=f-a,y=g*g+x*x,v,h;if(y<zE)h=Math.log(m/c)/t,v=function(_){return[l+_*g,a+_*x,c*Math.exp(t*_*h)]};else{var p=Math.sqrt(y),w=(m*m-c*c+r*y)/(2*c*n*p),S=(m*m-c*c-r*y)/(2*m*n*p),N=Math.log(Math.sqrt(w*w+1)-w),E=Math.log(Math.sqrt(S*S+1)-S);h=(E-N)/t,v=function(_){var C=_*h,T=nh(N),P=c/(n*p)*(T*DE(t*C+N)-LE(N));return[l+P*g,a+P*x,c*T/nh(t*C+N)]}}return v.duration=h*1e3*t/Math.SQRT2,v}return o.rho=function(s){var i=Math.max(.001,+s),l=i*i,a=l*l;return e(i,l,a)},o}(Math.SQRT2,2,4);var So=0,ts=0,Ho=0,iy=1e3,Ll,ns,Dl=0,wr=0,ma=0,Fs=typeof performance=="object"&&performance.now?performance:Date,ly=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Qd(){return wr||(ly(UE),wr=Fs.now()+ma)}function UE(){wr=0}function Fl(){this._call=this._time=this._next=null}Fl.prototype=ay.prototype={constructor:Fl,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Qd():+n)+(t==null?0:+t),!this._next&&ns!==this&&(ns?ns._next=this:Ll=this,ns=this),this._call=e,this._time=n,Au()},stop:function(){this._call&&(this._call=null,this._time=1/0,Au())}};function ay(e,t,n){var r=new Fl;return r.restart(e,t,n),r}function BE(){Qd(),++So;for(var e=Ll,t;e;)(t=wr-e._time)>=0&&e._call.call(void 0,t),e=e._next;--So}function rh(){wr=(Dl=Fs.now())+ma,So=ts=0;try{BE()}finally{So=0,VE(),wr=0}}function HE(){var e=Fs.now(),t=e-Dl;t>iy&&(ma-=t,Dl=e)}function VE(){for(var e,t=Ll,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:Ll=n);ns=e,Au(r)}function Au(e){if(!So){ts&&(ts=clearTimeout(ts));var t=e-wr;t>24?(e<1/0&&(ts=setTimeout(rh,e-Fs.now()-ma)),Ho&&(Ho=clearInterval(Ho))):(Ho||(Dl=Fs.now(),Ho=setInterval(HE,iy)),So=1,ly(rh))}}function oh(e,t,n){var r=new Fl;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var WE=pa("start","end","cancel","interrupt"),qE=[],cy=0,sh=1,Tu=2,el=3,ih=4,Pu=5,tl=6;function ga(e,t,n,r,o,s){var i=e.__transition;if(!i)e.__transition={};else if(n in i)return;YE(e,n,{name:t,index:r,group:o,on:WE,tween:qE,time:s.time,delay:s.delay,duration:s.duration,ease:s.ease,timer:null,state:cy})}function Zd(e,t){var n=Ot(e,t);if(n.state>cy)throw new Error("too late; already scheduled");return n}function qt(e,t){var n=Ot(e,t);if(n.state>el)throw new Error("too late; already running");return n}function Ot(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function YE(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=ay(s,0,n.time);function s(c){n.state=sh,n.timer.restart(i,n.delay,n.time),n.delay<=c&&i(c-n.delay)}function i(c){var u,f,m,g;if(n.state!==sh)return a();for(u in r)if(g=r[u],g.name===n.name){if(g.state===el)return oh(i);g.state===ih?(g.state=tl,g.timer.stop(),g.on.call("interrupt",e,e.__data__,g.index,g.group),delete r[u]):+u<t&&(g.state=tl,g.timer.stop(),g.on.call("cancel",e,e.__data__,g.index,g.group),delete r[u])}if(oh(function(){n.state===el&&(n.state=ih,n.timer.restart(l,n.delay,n.time),l(c))}),n.state=Tu,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Tu){for(n.state=el,o=new Array(m=n.tween.length),u=0,f=-1;u<m;++u)(g=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(o[++f]=g);o.length=f+1}}function l(c){for(var u=c<n.duration?n.ease.call(null,c/n.duration):(n.timer.restart(a),n.state=Pu,1),f=-1,m=o.length;++f<m;)o[f].call(e,u);n.state===Pu&&(n.on.call("end",e,e.__data__,n.index,n.group),a())}function a(){n.state=tl,n.timer.stop(),delete r[t];for(var c in r)return;delete e.__transition}}function nl(e,t){var n=e.__transition,r,o,s=!0,i;if(n){t=t==null?null:t+"";for(i in n){if((r=n[i]).name!==t){s=!1;continue}o=r.state>Tu&&r.state<Pu,r.state=tl,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[i]}s&&delete e.__transition}}function GE(e){return this.each(function(){nl(this,e)})}function XE(e,t){var n,r;return function(){var o=qt(this,e),s=o.tween;if(s!==n){r=n=s;for(var i=0,l=r.length;i<l;++i)if(r[i].name===t){r=r.slice(),r.splice(i,1);break}}o.tween=r}}function KE(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var s=qt(this,e),i=s.tween;if(i!==r){o=(r=i).slice();for(var l={name:t,value:n},a=0,c=o.length;a<c;++a)if(o[a].name===t){o[a]=l;break}a===c&&o.push(l)}s.tween=o}}function QE(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Ot(this.node(),n).tween,o=0,s=r.length,i;o<s;++o)if((i=r[o]).name===e)return i.value;return null}return this.each((t==null?XE:KE)(n,e,t))}function Jd(e,t,n){var r=e._id;return e.each(function(){var o=qt(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Ot(o,r).value[t]}}function uy(e,t){var n;return(typeof t=="number"?Sn:t instanceof Ds?eh:(n=Ds(t))?(t=n,eh):PE)(e,t)}function ZE(e){return function(){this.removeAttribute(e)}}function JE(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ek(e,t,n){var r,o=n+"",s;return function(){var i=this.getAttribute(e);return i===o?null:i===r?s:s=t(r=i,n)}}function tk(e,t,n){var r,o=n+"",s;return function(){var i=this.getAttributeNS(e.space,e.local);return i===o?null:i===r?s:s=t(r=i,n)}}function nk(e,t,n){var r,o,s;return function(){var i,l=n(this),a;return l==null?void this.removeAttribute(e):(i=this.getAttribute(e),a=l+"",i===a?null:i===r&&a===o?s:(o=a,s=t(r=i,l)))}}function rk(e,t,n){var r,o,s;return function(){var i,l=n(this),a;return l==null?void this.removeAttributeNS(e.space,e.local):(i=this.getAttributeNS(e.space,e.local),a=l+"",i===a?null:i===r&&a===o?s:(o=a,s=t(r=i,l)))}}function ok(e,t){var n=ha(e),r=n==="transform"?IE:uy;return this.attrTween(e,typeof t=="function"?(n.local?rk:nk)(n,r,Jd(this,"attr."+e,t)):t==null?(n.local?JE:ZE)(n):(n.local?tk:ek)(n,r,t))}function sk(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function ik(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function lk(e,t){var n,r;function o(){var s=t.apply(this,arguments);return s!==r&&(n=(r=s)&&ik(e,s)),n}return o._value=t,o}function ak(e,t){var n,r;function o(){var s=t.apply(this,arguments);return s!==r&&(n=(r=s)&&sk(e,s)),n}return o._value=t,o}function ck(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=ha(e);return this.tween(n,(r.local?lk:ak)(r,t))}function uk(e,t){return function(){Zd(this,e).delay=+t.apply(this,arguments)}}function dk(e,t){return t=+t,function(){Zd(this,e).delay=t}}function fk(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?uk:dk)(t,e)):Ot(this.node(),t).delay}function pk(e,t){return function(){qt(this,e).duration=+t.apply(this,arguments)}}function hk(e,t){return t=+t,function(){qt(this,e).duration=t}}function mk(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?pk:hk)(t,e)):Ot(this.node(),t).duration}function gk(e,t){if(typeof t!="function")throw new Error;return function(){qt(this,e).ease=t}}function yk(e){var t=this._id;return arguments.length?this.each(gk(t,e)):Ot(this.node(),t).ease}function xk(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;qt(this,e).ease=n}}function vk(e){if(typeof e!="function")throw new Error;return this.each(xk(this._id,e))}function wk(e){typeof e!="function"&&(e=B0(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var s=t[o],i=s.length,l=r[o]=[],a,c=0;c<i;++c)(a=s[c])&&e.call(a,a.__data__,c,s)&&l.push(a);return new fn(r,this._parents,this._name,this._id)}function Sk(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,s=Math.min(r,o),i=new Array(r),l=0;l<s;++l)for(var a=t[l],c=n[l],u=a.length,f=i[l]=new Array(u),m,g=0;g<u;++g)(m=a[g]||c[g])&&(f[g]=m);for(;l<r;++l)i[l]=t[l];return new fn(i,this._parents,this._name,this._id)}function bk(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function Nk(e,t,n){var r,o,s=bk(t)?Zd:qt;return function(){var i=s(this,e),l=i.on;l!==r&&(o=(r=l).copy()).on(t,n),i.on=o}}function Ek(e,t){var n=this._id;return arguments.length<2?Ot(this.node(),n).on.on(e):this.each(Nk(n,e,t))}function kk(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function _k(){return this.on("end.remove",kk(this._id))}function Ck(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Gd(e));for(var r=this._groups,o=r.length,s=new Array(o),i=0;i<o;++i)for(var l=r[i],a=l.length,c=s[i]=new Array(a),u,f,m=0;m<a;++m)(u=l[m])&&(f=e.call(u,u.__data__,m,l))&&("__data__"in u&&(f.__data__=u.__data__),c[m]=f,ga(c[m],t,n,m,c,Ot(u,n)));return new fn(s,this._parents,t,n)}function jk(e){var t=this._name,n=this._id;typeof e!="function"&&(e=U0(e));for(var r=this._groups,o=r.length,s=[],i=[],l=0;l<o;++l)for(var a=r[l],c=a.length,u,f=0;f<c;++f)if(u=a[f]){for(var m=e.call(u,u.__data__,f,a),g,x=Ot(u,n),y=0,v=m.length;y<v;++y)(g=m[y])&&ga(g,t,n,y,m,x);s.push(m),i.push(u)}return new fn(s,i,t,n)}var Rk=ri.prototype.constructor;function Ak(){return new Rk(this._groups,this._parents)}function Tk(e,t){var n,r,o;return function(){var s=wo(this,e),i=(this.style.removeProperty(e),wo(this,e));return s===i?null:s===n&&i===r?o:o=t(n=s,r=i)}}function dy(e){return function(){this.style.removeProperty(e)}}function Pk(e,t,n){var r,o=n+"",s;return function(){var i=wo(this,e);return i===o?null:i===r?s:s=t(r=i,n)}}function Mk(e,t,n){var r,o,s;return function(){var i=wo(this,e),l=n(this),a=l+"";return l==null&&(a=l=(this.style.removeProperty(e),wo(this,e))),i===a?null:i===r&&a===o?s:(o=a,s=t(r=i,l))}}function $k(e,t){var n,r,o,s="style."+t,i="end."+s,l;return function(){var a=qt(this,e),c=a.on,u=a.value[s]==null?l||(l=dy(t)):void 0;(c!==n||o!==u)&&(r=(n=c).copy()).on(i,o=u),a.on=r}}function Ok(e,t,n){var r=(e+="")=="transform"?OE:uy;return t==null?this.styleTween(e,Tk(e,r)).on("end.style."+e,dy(e)):typeof t=="function"?this.styleTween(e,Mk(e,r,Jd(this,"style."+e,t))).each($k(this._id,e)):this.styleTween(e,Pk(e,r,t),n).on("end.style."+e,null)}function Ik(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function zk(e,t,n){var r,o;function s(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&Ik(e,i,n)),r}return s._value=t,s}function Lk(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,zk(e,t,n??""))}function Dk(e){return function(){this.textContent=e}}function Fk(e){return function(){var t=e(this);this.textContent=t??""}}function Uk(e){return this.tween("text",typeof e=="function"?Fk(Jd(this,"text",e)):Dk(e==null?"":e+""))}function Bk(e){return function(t){this.textContent=e.call(this,t)}}function Hk(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Bk(o)),t}return r._value=e,r}function Vk(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Hk(e))}function Wk(){for(var e=this._name,t=this._id,n=fy(),r=this._groups,o=r.length,s=0;s<o;++s)for(var i=r[s],l=i.length,a,c=0;c<l;++c)if(a=i[c]){var u=Ot(a,t);ga(a,e,n,c,i,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new fn(r,this._parents,e,n)}function qk(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(s,i){var l={value:i},a={value:function(){--o===0&&s()}};n.each(function(){var c=qt(this,r),u=c.on;u!==e&&(t=(e=u).copy(),t._.cancel.push(l),t._.interrupt.push(l),t._.end.push(a)),c.on=t}),o===0&&s()})}var Yk=0;function fn(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function fy(){return++Yk}var Qt=ri.prototype;fn.prototype={constructor:fn,select:Ck,selectAll:jk,selectChild:Qt.selectChild,selectChildren:Qt.selectChildren,filter:wk,merge:Sk,selection:Ak,transition:Wk,call:Qt.call,nodes:Qt.nodes,node:Qt.node,size:Qt.size,empty:Qt.empty,each:Qt.each,on:Ek,attr:ok,attrTween:ck,style:Ok,styleTween:Lk,text:Uk,textTween:Vk,remove:_k,tween:QE,delay:fk,duration:mk,ease:yk,easeVarying:vk,end:qk,[Symbol.iterator]:Qt[Symbol.iterator]};function Gk(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Xk={time:null,delay:0,duration:250,ease:Gk};function Kk(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Qk(e){var t,n;e instanceof fn?(t=e._id,e=e._name):(t=fy(),(n=Xk).time=Qd(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,s=0;s<o;++s)for(var i=r[s],l=i.length,a,c=0;c<l;++c)(a=i[c])&&ga(a,e,t,c,i,n||Kk(a,t));return new fn(r,this._parents,e,t)}ri.prototype.interrupt=GE;ri.prototype.transition=Qk;const Ti=e=>()=>e;function Zk(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function rn(e,t,n){this.k=e,this.x=t,this.y=n}rn.prototype={constructor:rn,scale:function(e){return e===1?this:new rn(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new rn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var sn=new rn(1,0,0);rn.prototype;function uc(e){e.stopImmediatePropagation()}function Vo(e){e.preventDefault(),e.stopImmediatePropagation()}function Jk(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function e_(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function lh(){return this.__zoom||sn}function t_(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function n_(){return navigator.maxTouchPoints||"ontouchstart"in this}function r_(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],s=e.invertY(t[0][1])-n[0][1],i=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),i>s?(s+i)/2:Math.min(0,s)||Math.max(0,i))}function py(){var e=Jk,t=e_,n=r_,r=t_,o=n_,s=[0,1/0],i=[[-1/0,-1/0],[1/0,1/0]],l=250,a=FE,c=pa("start","zoom","end"),u,f,m,g=500,x=150,y=0,v=10;function h(k){k.property("__zoom",lh).on("wheel.zoom",C,{passive:!1}).on("mousedown.zoom",T).on("dblclick.zoom",P).filter(o).on("touchstart.zoom",D).on("touchmove.zoom",U).on("touchend.zoom touchcancel.zoom",B).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}h.transform=function(k,A,R,F){var M=k.selection?k.selection():k;M.property("__zoom",lh),k!==M?N(k,A,R,F):M.interrupt().each(function(){E(this,arguments).event(F).start().zoom(null,typeof A=="function"?A.apply(this,arguments):A).end()})},h.scaleBy=function(k,A,R,F){h.scaleTo(k,function(){var M=this.__zoom.k,j=typeof A=="function"?A.apply(this,arguments):A;return M*j},R,F)},h.scaleTo=function(k,A,R,F){h.transform(k,function(){var M=t.apply(this,arguments),j=this.__zoom,O=R==null?S(M):typeof R=="function"?R.apply(this,arguments):R,$=j.invert(O),L=typeof A=="function"?A.apply(this,arguments):A;return n(w(p(j,L),O,$),M,i)},R,F)},h.translateBy=function(k,A,R,F){h.transform(k,function(){return n(this.__zoom.translate(typeof A=="function"?A.apply(this,arguments):A,typeof R=="function"?R.apply(this,arguments):R),t.apply(this,arguments),i)},null,F)},h.translateTo=function(k,A,R,F,M){h.transform(k,function(){var j=t.apply(this,arguments),O=this.__zoom,$=F==null?S(j):typeof F=="function"?F.apply(this,arguments):F;return n(sn.translate($[0],$[1]).scale(O.k).translate(typeof A=="function"?-A.apply(this,arguments):-A,typeof R=="function"?-R.apply(this,arguments):-R),j,i)},F,M)};function p(k,A){return A=Math.max(s[0],Math.min(s[1],A)),A===k.k?k:new rn(A,k.x,k.y)}function w(k,A,R){var F=A[0]-R[0]*k.k,M=A[1]-R[1]*k.k;return F===k.x&&M===k.y?k:new rn(k.k,F,M)}function S(k){return[(+k[0][0]+ +k[1][0])/2,(+k[0][1]+ +k[1][1])/2]}function N(k,A,R,F){k.on("start.zoom",function(){E(this,arguments).event(F).start()}).on("interrupt.zoom end.zoom",function(){E(this,arguments).event(F).end()}).tween("zoom",function(){var M=this,j=arguments,O=E(M,j).event(F),$=t.apply(M,j),L=R==null?S($):typeof R=="function"?R.apply(M,j):R,V=Math.max($[1][0]-$[0][0],$[1][1]-$[0][1]),W=M.__zoom,Y=typeof A=="function"?A.apply(M,j):A,K=a(W.invert(L).concat(V/W.k),Y.invert(L).concat(V/Y.k));return function(J){if(J===1)J=Y;else{var ie=K(J),se=V/ie[2];J=new rn(se,L[0]-ie[0]*se,L[1]-ie[1]*se)}O.zoom(null,J)}})}function E(k,A,R){return!R&&k.__zooming||new _(k,A)}function _(k,A){this.that=k,this.args=A,this.active=0,this.sourceEvent=null,this.extent=t.apply(k,A),this.taps=0}_.prototype={event:function(k){return k&&(this.sourceEvent=k),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(k,A){return this.mouse&&k!=="mouse"&&(this.mouse[1]=A.invert(this.mouse[0])),this.touch0&&k!=="touch"&&(this.touch0[1]=A.invert(this.touch0[0])),this.touch1&&k!=="touch"&&(this.touch1[1]=A.invert(this.touch1[0])),this.that.__zoom=A,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(k){var A=xt(this.that).datum();c.call(k,this.that,new Zk(k,{sourceEvent:this.sourceEvent,target:h,transform:this.that.__zoom,dispatch:c}),A)}};function C(k,...A){if(!e.apply(this,arguments))return;var R=E(this,A).event(k),F=this.__zoom,M=Math.max(s[0],Math.min(s[1],F.k*Math.pow(2,r.apply(this,arguments)))),j=jt(k);if(R.wheel)(R.mouse[0][0]!==j[0]||R.mouse[0][1]!==j[1])&&(R.mouse[1]=F.invert(R.mouse[0]=j)),clearTimeout(R.wheel);else{if(F.k===M)return;R.mouse=[j,F.invert(j)],nl(this),R.start()}Vo(k),R.wheel=setTimeout(O,x),R.zoom("mouse",n(w(p(F,M),R.mouse[0],R.mouse[1]),R.extent,i));function O(){R.wheel=null,R.end()}}function T(k,...A){if(m||!e.apply(this,arguments))return;var R=k.currentTarget,F=E(this,A,!0).event(k),M=xt(k.view).on("mousemove.zoom",L,!0).on("mouseup.zoom",V,!0),j=jt(k,R),O=k.clientX,$=k.clientY;Z0(k.view),uc(k),F.mouse=[j,this.__zoom.invert(j)],nl(this),F.start();function L(W){if(Vo(W),!F.moved){var Y=W.clientX-O,K=W.clientY-$;F.moved=Y*Y+K*K>y}F.event(W).zoom("mouse",n(w(F.that.__zoom,F.mouse[0]=jt(W,R),F.mouse[1]),F.extent,i))}function V(W){M.on("mousemove.zoom mouseup.zoom",null),J0(W.view,F.moved),Vo(W),F.event(W).end()}}function P(k,...A){if(e.apply(this,arguments)){var R=this.__zoom,F=jt(k.changedTouches?k.changedTouches[0]:k,this),M=R.invert(F),j=R.k*(k.shiftKey?.5:2),O=n(w(p(R,j),F,M),t.apply(this,A),i);Vo(k),l>0?xt(this).transition().duration(l).call(N,O,F,k):xt(this).call(h.transform,O,F,k)}}function D(k,...A){if(e.apply(this,arguments)){var R=k.touches,F=R.length,M=E(this,A,k.changedTouches.length===F).event(k),j,O,$,L;for(uc(k),O=0;O<F;++O)$=R[O],L=jt($,this),L=[L,this.__zoom.invert(L),$.identifier],M.touch0?!M.touch1&&M.touch0[2]!==L[2]&&(M.touch1=L,M.taps=0):(M.touch0=L,j=!0,M.taps=1+!!u);u&&(u=clearTimeout(u)),j&&(M.taps<2&&(f=L[0],u=setTimeout(function(){u=null},g)),nl(this),M.start())}}function U(k,...A){if(this.__zooming){var R=E(this,A).event(k),F=k.changedTouches,M=F.length,j,O,$,L;for(Vo(k),j=0;j<M;++j)O=F[j],$=jt(O,this),R.touch0&&R.touch0[2]===O.identifier?R.touch0[0]=$:R.touch1&&R.touch1[2]===O.identifier&&(R.touch1[0]=$);if(O=R.that.__zoom,R.touch1){var V=R.touch0[0],W=R.touch0[1],Y=R.touch1[0],K=R.touch1[1],J=(J=Y[0]-V[0])*J+(J=Y[1]-V[1])*J,ie=(ie=K[0]-W[0])*ie+(ie=K[1]-W[1])*ie;O=p(O,Math.sqrt(J/ie)),$=[(V[0]+Y[0])/2,(V[1]+Y[1])/2],L=[(W[0]+K[0])/2,(W[1]+K[1])/2]}else if(R.touch0)$=R.touch0[0],L=R.touch0[1];else return;R.zoom("touch",n(w(O,$,L),R.extent,i))}}function B(k,...A){if(this.__zooming){var R=E(this,A).event(k),F=k.changedTouches,M=F.length,j,O;for(uc(k),m&&clearTimeout(m),m=setTimeout(function(){m=null},g),j=0;j<M;++j)O=F[j],R.touch0&&R.touch0[2]===O.identifier?delete R.touch0:R.touch1&&R.touch1[2]===O.identifier&&delete R.touch1;if(R.touch1&&!R.touch0&&(R.touch0=R.touch1,delete R.touch1),R.touch0)R.touch0[1]=this.__zoom.invert(R.touch0[0]);else if(R.end(),R.taps===2&&(O=jt(O,this),Math.hypot(f[0]-O[0],f[1]-O[1])<v)){var $=xt(this).on("dblclick.zoom");$&&$.apply(this,arguments)}}}return h.wheelDelta=function(k){return arguments.length?(r=typeof k=="function"?k:Ti(+k),h):r},h.filter=function(k){return arguments.length?(e=typeof k=="function"?k:Ti(!!k),h):e},h.touchable=function(k){return arguments.length?(o=typeof k=="function"?k:Ti(!!k),h):o},h.extent=function(k){return arguments.length?(t=typeof k=="function"?k:Ti([[+k[0][0],+k[0][1]],[+k[1][0],+k[1][1]]]),h):t},h.scaleExtent=function(k){return arguments.length?(s[0]=+k[0],s[1]=+k[1],h):[s[0],s[1]]},h.translateExtent=function(k){return arguments.length?(i[0][0]=+k[0][0],i[1][0]=+k[1][0],i[0][1]=+k[0][1],i[1][1]=+k[1][1],h):[[i[0][0],i[0][1]],[i[1][0],i[1][1]]]},h.constrain=function(k){return arguments.length?(n=k,h):n},h.duration=function(k){return arguments.length?(l=+k,h):l},h.interpolate=function(k){return arguments.length?(a=k,h):a},h.on=function(){var k=c.on.apply(c,arguments);return k===c?h:k},h.clickDistance=function(k){return arguments.length?(y=(k=+k)*k,h):Math.sqrt(y)},h.tapDistance=function(k){return arguments.length?(v=+k,h):v},h}const ya=b.createContext(null),o_=ya.Provider,pn={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},hy=pn.error001();function ue(e,t){const n=b.useContext(ya);if(n===null)throw new Error(hy);return D0(n,e,t)}const Re=()=>{const e=b.useContext(ya);if(e===null)throw new Error(hy);return b.useMemo(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},s_=e=>e.userSelectionActive?"none":"all";function ef({position:e,children:t,className:n,style:r,...o}){const s=ue(s_),i=`${e}`.split("-");return I.createElement("div",{className:Le(["react-flow__panel",n,...i]),style:{...r,pointerEvents:s},...o},t)}function i_({proOptions:e,position:t="bottom-right"}){return e!=null&&e.hideAttribution?null:I.createElement(ef,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},I.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}const l_=({x:e,y:t,label:n,labelStyle:r={},labelShowBg:o=!0,labelBgStyle:s={},labelBgPadding:i=[2,4],labelBgBorderRadius:l=2,children:a,className:c,...u})=>{const f=b.useRef(null),[m,g]=b.useState({x:0,y:0,width:0,height:0}),x=Le(["react-flow__edge-textwrapper",c]);return b.useEffect(()=>{if(f.current){const y=f.current.getBBox();g({x:y.x,y:y.y,width:y.width,height:y.height})}},[n]),typeof n>"u"||!n?null:I.createElement("g",{transform:`translate(${e-m.width/2} ${t-m.height/2})`,className:x,visibility:m.width?"visible":"hidden",...u},o&&I.createElement("rect",{width:m.width+2*i[0],x:-i[0],y:-i[1],height:m.height+2*i[1],className:"react-flow__edge-textbg",style:s,rx:l,ry:l}),I.createElement("text",{className:"react-flow__edge-text",y:m.height/2,dy:"0.3em",ref:f,style:r},n),a)};var a_=b.memo(l_);const tf=e=>({width:e.offsetWidth,height:e.offsetHeight}),bo=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),nf=(e={x:0,y:0},t)=>({x:bo(e.x,t[0][0],t[1][0]),y:bo(e.y,t[0][1],t[1][1])}),ah=(e,t,n)=>e<t?bo(Math.abs(e-t),1,50)/50:e>n?-bo(Math.abs(e-n),1,50)/50:0,my=(e,t)=>{const n=ah(e.x,35,t.width-35)*20,r=ah(e.y,35,t.height-35)*20;return[n,r]},gy=e=>{var t;return((t=e.getRootNode)==null?void 0:t.call(e))||(window==null?void 0:window.document)},yy=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Us=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),xy=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),ch=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),c_=(e,t)=>xy(yy(Us(e),Us(t))),Mu=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),r=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*r)},u_=e=>wt(e.width)&&wt(e.height)&&wt(e.x)&&wt(e.y),wt=e=>!isNaN(e)&&isFinite(e),ve=Symbol.for("internals"),vy=["Enter"," ","Escape"],d_=(e,t)=>{},f_=e=>"nativeEvent"in e;function $u(e){var o,s;const t=f_(e)?e.nativeEvent:e,n=((s=(o=t.composedPath)==null?void 0:o.call(t))==null?void 0:s[0])||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n==null?void 0:n.nodeName)||(n==null?void 0:n.hasAttribute("contenteditable"))||!!(n!=null&&n.closest(".nokey"))}const wy=e=>"clientX"in e,Dn=(e,t)=>{var s,i;const n=wy(e),r=n?e.clientX:(s=e.touches)==null?void 0:s[0].clientX,o=n?e.clientY:(i=e.touches)==null?void 0:i[0].clientY;return{x:r-((t==null?void 0:t.left)??0),y:o-((t==null?void 0:t.top)??0)}},Ul=()=>{var e;return typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)==null?void 0:e.indexOf("Mac"))>=0},si=({id:e,path:t,labelX:n,labelY:r,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c,style:u,markerEnd:f,markerStart:m,interactionWidth:g=20})=>I.createElement(I.Fragment,null,I.createElement("path",{id:e,style:u,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:f,markerStart:m}),g&&I.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),o&&wt(n)&&wt(r)?I.createElement(a_,{x:n,y:r,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c}):null);si.displayName="BaseEdge";function Wo(e,t,n){return n===void 0?n:r=>{const o=t().edges.find(s=>s.id===e);o&&n(r,{...o})}}function Sy({sourceX:e,sourceY:t,targetX:n,targetY:r}){const o=Math.abs(n-e)/2,s=n<e?n+o:n-o,i=Math.abs(r-t)/2,l=r<t?r+i:r-i;return[s,l,o,i]}function by({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:s,targetControlX:i,targetControlY:l}){const a=e*.125+o*.375+i*.375+n*.125,c=t*.125+s*.375+l*.375+r*.125,u=Math.abs(a-e),f=Math.abs(c-t);return[a,c,u,f]}var Sr;(function(e){e.Strict="strict",e.Loose="loose"})(Sr||(Sr={}));var ar;(function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"})(ar||(ar={}));var Bs;(function(e){e.Partial="partial",e.Full="full"})(Bs||(Bs={}));var _n;(function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"})(_n||(_n={}));var Bl;(function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"})(Bl||(Bl={}));var G;(function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"})(G||(G={}));function uh({pos:e,x1:t,y1:n,x2:r,y2:o}){return e===G.Left||e===G.Right?[.5*(t+r),n]:[t,.5*(n+o)]}function Ny({sourceX:e,sourceY:t,sourcePosition:n=G.Bottom,targetX:r,targetY:o,targetPosition:s=G.Top}){const[i,l]=uh({pos:n,x1:e,y1:t,x2:r,y2:o}),[a,c]=uh({pos:s,x1:r,y1:o,x2:e,y2:t}),[u,f,m,g]=by({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:i,sourceControlY:l,targetControlX:a,targetControlY:c});return[`M${e},${t} C${i},${l} ${a},${c} ${r},${o}`,u,f,m,g]}const rf=b.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=G.Bottom,targetPosition:s=G.Top,label:i,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f,style:m,markerEnd:g,markerStart:x,interactionWidth:y})=>{const[v,h,p]=Ny({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:s});return I.createElement(si,{path:v,labelX:h,labelY:p,label:i,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f,style:m,markerEnd:g,markerStart:x,interactionWidth:y})});rf.displayName="SimpleBezierEdge";const dh={[G.Left]:{x:-1,y:0},[G.Right]:{x:1,y:0},[G.Top]:{x:0,y:-1},[G.Bottom]:{x:0,y:1}},p_=({source:e,sourcePosition:t=G.Bottom,target:n})=>t===G.Left||t===G.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},fh=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function h_({source:e,sourcePosition:t=G.Bottom,target:n,targetPosition:r=G.Top,center:o,offset:s}){const i=dh[t],l=dh[r],a={x:e.x+i.x*s,y:e.y+i.y*s},c={x:n.x+l.x*s,y:n.y+l.y*s},u=p_({source:a,sourcePosition:t,target:c}),f=u.x!==0?"x":"y",m=u[f];let g=[],x,y;const v={x:0,y:0},h={x:0,y:0},[p,w,S,N]=Sy({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(i[f]*l[f]===-1){x=o.x??p,y=o.y??w;const _=[{x,y:a.y},{x,y:c.y}],C=[{x:a.x,y},{x:c.x,y}];i[f]===m?g=f==="x"?_:C:g=f==="x"?C:_}else{const _=[{x:a.x,y:c.y}],C=[{x:c.x,y:a.y}];if(f==="x"?g=i.x===m?C:_:g=i.y===m?_:C,t===r){const B=Math.abs(e[f]-n[f]);if(B<=s){const k=Math.min(s-1,s-B);i[f]===m?v[f]=(a[f]>e[f]?-1:1)*k:h[f]=(c[f]>n[f]?-1:1)*k}}if(t!==r){const B=f==="x"?"y":"x",k=i[f]===l[B],A=a[B]>c[B],R=a[B]<c[B];(i[f]===1&&(!k&&A||k&&R)||i[f]!==1&&(!k&&R||k&&A))&&(g=f==="x"?_:C)}const T={x:a.x+v.x,y:a.y+v.y},P={x:c.x+h.x,y:c.y+h.y},D=Math.max(Math.abs(T.x-g[0].x),Math.abs(P.x-g[0].x)),U=Math.max(Math.abs(T.y-g[0].y),Math.abs(P.y-g[0].y));D>=U?(x=(T.x+P.x)/2,y=g[0].y):(x=g[0].x,y=(T.y+P.y)/2)}return[[e,{x:a.x+v.x,y:a.y+v.y},...g,{x:c.x+h.x,y:c.y+h.y},n],x,y,S,N]}function m_(e,t,n,r){const o=Math.min(fh(e,t)/2,fh(t,n)/2,r),{x:s,y:i}=t;if(e.x===s&&s===n.x||e.y===i&&i===n.y)return`L${s} ${i}`;if(e.y===i){const c=e.x<n.x?-1:1,u=e.y<n.y?1:-1;return`L ${s+o*c},${i}Q ${s},${i} ${s},${i+o*u}`}const l=e.x<n.x?1:-1,a=e.y<n.y?-1:1;return`L ${s},${i+o*a}Q ${s},${i} ${s+o*l},${i}`}function Ou({sourceX:e,sourceY:t,sourcePosition:n=G.Bottom,targetX:r,targetY:o,targetPosition:s=G.Top,borderRadius:i=5,centerX:l,centerY:a,offset:c=20}){const[u,f,m,g,x]=h_({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:s,center:{x:l,y:a},offset:c});return[u.reduce((v,h,p)=>{let w="";return p>0&&p<u.length-1?w=m_(u[p-1],h,u[p+1],i):w=`${p===0?"M":"L"}${h.x} ${h.y}`,v+=w,v},""),f,m,g,x]}const xa=b.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c,style:u,sourcePosition:f=G.Bottom,targetPosition:m=G.Top,markerEnd:g,markerStart:x,pathOptions:y,interactionWidth:v})=>{const[h,p,w]=Ou({sourceX:e,sourceY:t,sourcePosition:f,targetX:n,targetY:r,targetPosition:m,borderRadius:y==null?void 0:y.borderRadius,offset:y==null?void 0:y.offset});return I.createElement(si,{path:h,labelX:p,labelY:w,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c,style:u,markerEnd:g,markerStart:x,interactionWidth:v})});xa.displayName="SmoothStepEdge";const of=b.memo(e=>{var t;return I.createElement(xa,{...e,pathOptions:b.useMemo(()=>{var n;return{borderRadius:0,offset:(n=e.pathOptions)==null?void 0:n.offset}},[(t=e.pathOptions)==null?void 0:t.offset])})});of.displayName="StepEdge";function g_({sourceX:e,sourceY:t,targetX:n,targetY:r}){const[o,s,i,l]=Sy({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,s,i,l]}const sf=b.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c,style:u,markerEnd:f,markerStart:m,interactionWidth:g})=>{const[x,y,v]=g_({sourceX:e,sourceY:t,targetX:n,targetY:r});return I.createElement(si,{path:x,labelX:y,labelY:v,label:o,labelStyle:s,labelShowBg:i,labelBgStyle:l,labelBgPadding:a,labelBgBorderRadius:c,style:u,markerEnd:f,markerStart:m,interactionWidth:g})});sf.displayName="StraightEdge";function Pi(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function ph({pos:e,x1:t,y1:n,x2:r,y2:o,c:s}){switch(e){case G.Left:return[t-Pi(t-r,s),n];case G.Right:return[t+Pi(r-t,s),n];case G.Top:return[t,n-Pi(n-o,s)];case G.Bottom:return[t,n+Pi(o-n,s)]}}function Ey({sourceX:e,sourceY:t,sourcePosition:n=G.Bottom,targetX:r,targetY:o,targetPosition:s=G.Top,curvature:i=.25}){const[l,a]=ph({pos:n,x1:e,y1:t,x2:r,y2:o,c:i}),[c,u]=ph({pos:s,x1:r,y1:o,x2:e,y2:t,c:i}),[f,m,g,x]=by({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:l,sourceControlY:a,targetControlX:c,targetControlY:u});return[`M${e},${t} C${l},${a} ${c},${u} ${r},${o}`,f,m,g,x]}const Hl=b.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=G.Bottom,targetPosition:s=G.Top,label:i,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f,style:m,markerEnd:g,markerStart:x,pathOptions:y,interactionWidth:v})=>{const[h,p,w]=Ey({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:s,curvature:y==null?void 0:y.curvature});return I.createElement(si,{path:h,labelX:p,labelY:w,label:i,labelStyle:l,labelShowBg:a,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:f,style:m,markerEnd:g,markerStart:x,interactionWidth:v})});Hl.displayName="BezierEdge";const lf=b.createContext(null),y_=lf.Provider;lf.Consumer;const x_=()=>b.useContext(lf),v_=e=>"id"in e&&"source"in e&&"target"in e,w_=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`reactflow__edge-${e}${t||""}-${n}${r||""}`,Iu=(e,t)=>typeof e>"u"?"":typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(r=>`${r}=${e[r]}`).join("&")}`,S_=(e,t)=>t.some(n=>n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle)),ky=(e,t)=>{if(!e.source||!e.target)return t;let n;return v_(e)?n={...e}:n={...e,id:w_(e)},S_(n,t)?t:t.concat(n)},zu=({x:e,y:t},[n,r,o],s,[i,l])=>{const a={x:(e-n)/o,y:(t-r)/o};return s?{x:i*Math.round(a.x/i),y:l*Math.round(a.y/l)}:a},_y=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),pr=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],r=(e.height??0)*t[1],o={x:e.position.x-n,y:e.position.y-r};return{...o,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-r}:o}},va=(e,t=[0,0])=>{if(e.length===0)return{x:0,y:0,width:0,height:0};const n=e.reduce((r,o)=>{const{x:s,y:i}=pr(o,t).positionAbsolute;return yy(r,Us({x:s,y:i,width:o.width||0,height:o.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return xy(n)},Cy=(e,t,[n,r,o]=[0,0,1],s=!1,i=!1,l=[0,0])=>{const a={x:(t.x-n)/o,y:(t.y-r)/o,width:t.width/o,height:t.height/o},c=[];return e.forEach(u=>{const{width:f,height:m,selectable:g=!0,hidden:x=!1}=u;if(i&&!g||x)return!1;const{positionAbsolute:y}=pr(u,l),v={x:y.x,y:y.y,width:f||0,height:m||0},h=Mu(a,v),p=typeof f>"u"||typeof m>"u"||f===null||m===null,w=s&&h>0,S=(f||0)*(m||0);(p||w||h>=S||u.dragging)&&c.push(u)}),c},jy=(e,t)=>{const n=e.map(r=>r.id);return t.filter(r=>n.includes(r.source)||n.includes(r.target))},Ry=(e,t,n,r,o,s=.1)=>{const i=t/(e.width*(1+s)),l=n/(e.height*(1+s)),a=Math.min(i,l),c=bo(a,r,o),u=e.x+e.width/2,f=e.y+e.height/2,m=t/2-u*c,g=n/2-f*c;return{x:m,y:g,zoom:c}},nr=(e,t=0)=>e.transition().duration(t);function hh(e,t,n,r){return(t[n]||[]).reduce((o,s)=>{var i,l;return`${e.id}-${s.id}-${n}`!==r&&o.push({id:s.id||null,type:n,nodeId:e.id,x:(((i=e.positionAbsolute)==null?void 0:i.x)??0)+s.x+s.width/2,y:(((l=e.positionAbsolute)==null?void 0:l.y)??0)+s.y+s.height/2}),o},[])}function b_(e,t,n,r,o,s){const{x:i,y:l}=Dn(e),c=t.elementsFromPoint(i,l).find(x=>x.classList.contains("react-flow__handle"));if(c){const x=c.getAttribute("data-nodeid");if(x){const y=af(void 0,c),v=c.getAttribute("data-handleid"),h=s({nodeId:x,id:v,type:y});if(h){const p=o.find(w=>w.nodeId===x&&w.type===y&&w.id===v);return{handle:{id:v,type:y,nodeId:x,x:(p==null?void 0:p.x)||n.x,y:(p==null?void 0:p.y)||n.y},validHandleResult:h}}}}let u=[],f=1/0;if(o.forEach(x=>{const y=Math.sqrt((x.x-n.x)**2+(x.y-n.y)**2);if(y<=r){const v=s(x);y<=f&&(y<f?u=[{handle:x,validHandleResult:v}]:y===f&&u.push({handle:x,validHandleResult:v}),f=y)}}),!u.length)return{handle:null,validHandleResult:Ay()};if(u.length===1)return u[0];const m=u.some(({validHandleResult:x})=>x.isValid),g=u.some(({handle:x})=>x.type==="target");return u.find(({handle:x,validHandleResult:y})=>g?x.type==="target":m?y.isValid:!0)||u[0]}const N_={source:null,target:null,sourceHandle:null,targetHandle:null},Ay=()=>({handleDomNode:null,isValid:!1,connection:N_,endHandle:null});function Ty(e,t,n,r,o,s,i){const l=o==="target",a=i.querySelector(`.react-flow__handle[data-id="${e==null?void 0:e.nodeId}-${e==null?void 0:e.id}-${e==null?void 0:e.type}"]`),c={...Ay(),handleDomNode:a};if(a){const u=af(void 0,a),f=a.getAttribute("data-nodeid"),m=a.getAttribute("data-handleid"),g=a.classList.contains("connectable"),x=a.classList.contains("connectableend"),y={source:l?f:n,sourceHandle:l?m:r,target:l?n:f,targetHandle:l?r:m};c.connection=y,g&&x&&(t===Sr.Strict?l&&u==="source"||!l&&u==="target":f!==n||m!==r)&&(c.endHandle={nodeId:f,handleId:m,type:u},c.isValid=s(y))}return c}function E_({nodes:e,nodeId:t,handleId:n,handleType:r}){return e.reduce((o,s)=>{if(s[ve]){const{handleBounds:i}=s[ve];let l=[],a=[];i&&(l=hh(s,i,"source",`${t}-${n}-${r}`),a=hh(s,i,"target",`${t}-${n}-${r}`)),o.push(...l,...a)}return o},[])}function af(e,t){return e||(t!=null&&t.classList.contains("target")?"target":t!=null&&t.classList.contains("source")?"source":null)}function dc(e){e==null||e.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function k_(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Py({event:e,handleId:t,nodeId:n,onConnect:r,isTarget:o,getState:s,setState:i,isValidConnection:l,edgeUpdaterType:a,onReconnectEnd:c}){const u=gy(e.target),{connectionMode:f,domNode:m,autoPanOnConnect:g,connectionRadius:x,onConnectStart:y,panBy:v,getNodes:h,cancelConnection:p}=s();let w=0,S;const{x:N,y:E}=Dn(e),_=u==null?void 0:u.elementFromPoint(N,E),C=af(a,_),T=m==null?void 0:m.getBoundingClientRect();if(!T||!C)return;let P,D=Dn(e,T),U=!1,B=null,k=!1,A=null;const R=E_({nodes:h(),nodeId:n,handleId:t,handleType:C}),F=()=>{if(!g)return;const[O,$]=my(D,T);v({x:O,y:$}),w=requestAnimationFrame(F)};i({connectionPosition:D,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:C,connectionStartHandle:{nodeId:n,handleId:t,type:C},connectionEndHandle:null}),y==null||y(e,{nodeId:n,handleId:t,handleType:C});function M(O){const{transform:$}=s();D=Dn(O,T);const{handle:L,validHandleResult:V}=b_(O,u,zu(D,$,!1,[1,1]),x,R,W=>Ty(W,f,n,t,o?"target":"source",l,u));if(S=L,U||(F(),U=!0),A=V.handleDomNode,B=V.connection,k=V.isValid,i({connectionPosition:S&&k?_y({x:S.x,y:S.y},$):D,connectionStatus:k_(!!S,k),connectionEndHandle:V.endHandle}),!S&&!k&&!A)return dc(P);B.source!==B.target&&A&&(dc(P),P=A,A.classList.add("connecting","react-flow__handle-connecting"),A.classList.toggle("valid",k),A.classList.toggle("react-flow__handle-valid",k))}function j(O){var $,L;(S||A)&&B&&k&&(r==null||r(B)),(L=($=s()).onConnectEnd)==null||L.call($,O),a&&(c==null||c(O)),dc(P),p(),cancelAnimationFrame(w),U=!1,k=!1,B=null,A=null,u.removeEventListener("mousemove",M),u.removeEventListener("mouseup",j),u.removeEventListener("touchmove",M),u.removeEventListener("touchend",j)}u.addEventListener("mousemove",M),u.addEventListener("mouseup",j),u.addEventListener("touchmove",M),u.addEventListener("touchend",j)}const mh=()=>!0,__=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),C_=(e,t,n)=>r=>{const{connectionStartHandle:o,connectionEndHandle:s,connectionClickStartHandle:i}=r;return{connecting:(o==null?void 0:o.nodeId)===e&&(o==null?void 0:o.handleId)===t&&(o==null?void 0:o.type)===n||(s==null?void 0:s.nodeId)===e&&(s==null?void 0:s.handleId)===t&&(s==null?void 0:s.type)===n,clickConnecting:(i==null?void 0:i.nodeId)===e&&(i==null?void 0:i.handleId)===t&&(i==null?void 0:i.type)===n}},My=b.forwardRef(({type:e="source",position:t=G.Top,isValidConnection:n,isConnectable:r=!0,isConnectableStart:o=!0,isConnectableEnd:s=!0,id:i,onConnect:l,children:a,className:c,onMouseDown:u,onTouchStart:f,...m},g)=>{var T,P;const x=i||null,y=e==="target",v=Re(),h=x_(),{connectOnClick:p,noPanClassName:w}=ue(__,Pe),{connecting:S,clickConnecting:N}=ue(C_(h,x,e),Pe);h||(P=(T=v.getState()).onError)==null||P.call(T,"010",pn.error010());const E=D=>{const{defaultEdgeOptions:U,onConnect:B,hasDefaultEdges:k}=v.getState(),A={...U,...D};if(k){const{edges:R,setEdges:F}=v.getState();F(ky(A,R))}B==null||B(A),l==null||l(A)},_=D=>{if(!h)return;const U=wy(D);o&&(U&&D.button===0||!U)&&Py({event:D,handleId:x,nodeId:h,onConnect:E,isTarget:y,getState:v.getState,setState:v.setState,isValidConnection:n||v.getState().isValidConnection||mh}),U?u==null||u(D):f==null||f(D)},C=D=>{const{onClickConnectStart:U,onClickConnectEnd:B,connectionClickStartHandle:k,connectionMode:A,isValidConnection:R}=v.getState();if(!h||!k&&!o)return;if(!k){U==null||U(D,{nodeId:h,handleId:x,handleType:e}),v.setState({connectionClickStartHandle:{nodeId:h,type:e,handleId:x}});return}const F=gy(D.target),M=n||R||mh,{connection:j,isValid:O}=Ty({nodeId:h,id:x,type:e},A,k.nodeId,k.handleId||null,k.type,M,F);O&&E(j),B==null||B(D),v.setState({connectionClickStartHandle:null})};return I.createElement("div",{"data-handleid":x,"data-nodeid":h,"data-handlepos":t,"data-id":`${h}-${x}-${e}`,className:Le(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",w,c,{source:!y,target:y,connectable:r,connectablestart:o,connectableend:s,connecting:N,connectionindicator:r&&(o&&!S||s&&S)}]),onMouseDown:_,onTouchStart:_,onClick:p?C:void 0,ref:g,...m},a)});My.displayName="Handle";var No=b.memo(My);const $y=({data:e,isConnectable:t,targetPosition:n=G.Top,sourcePosition:r=G.Bottom})=>I.createElement(I.Fragment,null,I.createElement(No,{type:"target",position:n,isConnectable:t}),e==null?void 0:e.label,I.createElement(No,{type:"source",position:r,isConnectable:t}));$y.displayName="DefaultNode";var Lu=b.memo($y);const Oy=({data:e,isConnectable:t,sourcePosition:n=G.Bottom})=>I.createElement(I.Fragment,null,e==null?void 0:e.label,I.createElement(No,{type:"source",position:n,isConnectable:t}));Oy.displayName="InputNode";var Iy=b.memo(Oy);const zy=({data:e,isConnectable:t,targetPosition:n=G.Top})=>I.createElement(I.Fragment,null,I.createElement(No,{type:"target",position:n,isConnectable:t}),e==null?void 0:e.label);zy.displayName="OutputNode";var Ly=b.memo(zy);const cf=()=>null;cf.displayName="GroupNode";const j_=e=>({selectedNodes:e.getNodes().filter(t=>t.selected),selectedEdges:e.edges.filter(t=>t.selected).map(t=>({...t}))}),Mi=e=>e.id;function R_(e,t){return Pe(e.selectedNodes.map(Mi),t.selectedNodes.map(Mi))&&Pe(e.selectedEdges.map(Mi),t.selectedEdges.map(Mi))}const Dy=b.memo(({onSelectionChange:e})=>{const t=Re(),{selectedNodes:n,selectedEdges:r}=ue(j_,R_);return b.useEffect(()=>{const o={nodes:n,edges:r};e==null||e(o),t.getState().onSelectionChange.forEach(s=>s(o))},[n,r,e]),null});Dy.displayName="SelectionListener";const A_=e=>!!e.onSelectionChange;function T_({onSelectionChange:e}){const t=ue(A_);return e||t?I.createElement(Dy,{onSelectionChange:e}):null}const P_=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function Mr(e,t){b.useEffect(()=>{typeof e<"u"&&t(e)},[e])}function ee(e,t,n){b.useEffect(()=>{typeof t<"u"&&n({[e]:t})},[t])}const M_=({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:o,onConnectStart:s,onConnectEnd:i,onClickConnectStart:l,onClickConnectEnd:a,nodesDraggable:c,nodesConnectable:u,nodesFocusable:f,edgesFocusable:m,edgesUpdatable:g,elevateNodesOnSelect:x,minZoom:y,maxZoom:v,nodeExtent:h,onNodesChange:p,onEdgesChange:w,elementsSelectable:S,connectionMode:N,snapGrid:E,snapToGrid:_,translateExtent:C,connectOnClick:T,defaultEdgeOptions:P,fitView:D,fitViewOptions:U,onNodesDelete:B,onEdgesDelete:k,onNodeDrag:A,onNodeDragStart:R,onNodeDragStop:F,onSelectionDrag:M,onSelectionDragStart:j,onSelectionDragStop:O,noPanClassName:$,nodeOrigin:L,rfId:V,autoPanOnConnect:W,autoPanOnNodeDrag:Y,onError:K,connectionRadius:J,isValidConnection:ie,nodeDragThreshold:se})=>{const{setNodes:oe,setEdges:Me,setDefaultNodesAndEdges:_e,setMinZoom:qe,setMaxZoom:De,setTranslateExtent:we,setNodeExtent:lt,reset:ae}=ue(P_,Pe),X=Re();return b.useEffect(()=>{const Ye=r==null?void 0:r.map(Yt=>({...Yt,...P}));return _e(n,Ye),()=>{ae()}},[]),ee("defaultEdgeOptions",P,X.setState),ee("connectionMode",N,X.setState),ee("onConnect",o,X.setState),ee("onConnectStart",s,X.setState),ee("onConnectEnd",i,X.setState),ee("onClickConnectStart",l,X.setState),ee("onClickConnectEnd",a,X.setState),ee("nodesDraggable",c,X.setState),ee("nodesConnectable",u,X.setState),ee("nodesFocusable",f,X.setState),ee("edgesFocusable",m,X.setState),ee("edgesUpdatable",g,X.setState),ee("elementsSelectable",S,X.setState),ee("elevateNodesOnSelect",x,X.setState),ee("snapToGrid",_,X.setState),ee("snapGrid",E,X.setState),ee("onNodesChange",p,X.setState),ee("onEdgesChange",w,X.setState),ee("connectOnClick",T,X.setState),ee("fitViewOnInit",D,X.setState),ee("fitViewOnInitOptions",U,X.setState),ee("onNodesDelete",B,X.setState),ee("onEdgesDelete",k,X.setState),ee("onNodeDrag",A,X.setState),ee("onNodeDragStart",R,X.setState),ee("onNodeDragStop",F,X.setState),ee("onSelectionDrag",M,X.setState),ee("onSelectionDragStart",j,X.setState),ee("onSelectionDragStop",O,X.setState),ee("noPanClassName",$,X.setState),ee("nodeOrigin",L,X.setState),ee("rfId",V,X.setState),ee("autoPanOnConnect",W,X.setState),ee("autoPanOnNodeDrag",Y,X.setState),ee("onError",K,X.setState),ee("connectionRadius",J,X.setState),ee("isValidConnection",ie,X.setState),ee("nodeDragThreshold",se,X.setState),Mr(e,oe),Mr(t,Me),Mr(y,qe),Mr(v,De),Mr(C,we),Mr(h,lt),null},gh={display:"none"},$_={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},Fy="react-flow__node-desc",Uy="react-flow__edge-desc",O_="react-flow__aria-live",I_=e=>e.ariaLiveMessage;function z_({rfId:e}){const t=ue(I_);return I.createElement("div",{id:`${O_}-${e}`,"aria-live":"assertive","aria-atomic":"true",style:$_},t)}function L_({rfId:e,disableKeyboardA11y:t}){return I.createElement(I.Fragment,null,I.createElement("div",{id:`${Fy}-${e}`,style:gh},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),I.createElement("div",{id:`${Uy}-${e}`,style:gh},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&I.createElement(z_,{rfId:e}))}var Hs=(e=null,t={actInsideInputWithModifier:!0})=>{const[n,r]=b.useState(!1),o=b.useRef(!1),s=b.useRef(new Set([])),[i,l]=b.useMemo(()=>{if(e!==null){const c=(Array.isArray(e)?e:[e]).filter(f=>typeof f=="string").map(f=>f.split("+")),u=c.reduce((f,m)=>f.concat(...m),[]);return[c,u]}return[[],[]]},[e]);return b.useEffect(()=>{const a=typeof document<"u"?document:null,c=(t==null?void 0:t.target)||a;if(e!==null){const u=g=>{if(o.current=g.ctrlKey||g.metaKey||g.shiftKey,(!o.current||o.current&&!t.actInsideInputWithModifier)&&$u(g))return!1;const y=xh(g.code,l);s.current.add(g[y]),yh(i,s.current,!1)&&(g.preventDefault(),r(!0))},f=g=>{if((!o.current||o.current&&!t.actInsideInputWithModifier)&&$u(g))return!1;const y=xh(g.code,l);yh(i,s.current,!0)?(r(!1),s.current.clear()):s.current.delete(g[y]),g.key==="Meta"&&s.current.clear(),o.current=!1},m=()=>{s.current.clear(),r(!1)};return c==null||c.addEventListener("keydown",u),c==null||c.addEventListener("keyup",f),window.addEventListener("blur",m),()=>{c==null||c.removeEventListener("keydown",u),c==null||c.removeEventListener("keyup",f),window.removeEventListener("blur",m)}}},[e,r]),n};function yh(e,t,n){return e.filter(r=>n||r.length===t.size).some(r=>r.every(o=>t.has(o)))}function xh(e,t){return t.includes(e)?"code":"key"}function By(e,t,n,r){var l,a;const o=e.parentNode||e.parentId;if(!o)return n;const s=t.get(o),i=pr(s,r);return By(s,t,{x:(n.x??0)+i.x,y:(n.y??0)+i.y,z:(((l=s[ve])==null?void 0:l.z)??0)>(n.z??0)?((a=s[ve])==null?void 0:a.z)??0:n.z??0},r)}function Hy(e,t,n){e.forEach(r=>{var s;const o=r.parentNode||r.parentId;if(o&&!e.has(o))throw new Error(`Parent node ${o} not found`);if(o||n!=null&&n[r.id]){const{x:i,y:l,z:a}=By(r,e,{...r.position,z:((s=r[ve])==null?void 0:s.z)??0},t);r.positionAbsolute={x:i,y:l},r[ve].z=a,n!=null&&n[r.id]&&(r[ve].isParent=!0)}})}function fc(e,t,n,r){const o=new Map,s={},i=r?1e3:0;return e.forEach(l=>{var g;const a=(wt(l.zIndex)?l.zIndex:0)+(l.selected?i:0),c=t.get(l.id),u={...l,positionAbsolute:{x:l.position.x,y:l.position.y}},f=l.parentNode||l.parentId;f&&(s[f]=!0);const m=(c==null?void 0:c.type)&&(c==null?void 0:c.type)!==l.type;Object.defineProperty(u,ve,{enumerable:!1,value:{handleBounds:m||(g=c==null?void 0:c[ve])==null?void 0:g.handleBounds,z:a}}),o.set(l.id,u)}),Hy(o,n,s),o}function Vy(e,t={}){const{getNodes:n,width:r,height:o,minZoom:s,maxZoom:i,d3Zoom:l,d3Selection:a,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:f}=e(),m=t.initial&&!c&&u;if(l&&a&&(m||!t.initial)){const x=n().filter(v=>{var p;const h=t.includeHiddenNodes?v.width&&v.height:!v.hidden;return(p=t.nodes)!=null&&p.length?h&&t.nodes.some(w=>w.id===v.id):h}),y=x.every(v=>v.width&&v.height);if(x.length>0&&y){const v=va(x,f),{x:h,y:p,zoom:w}=Ry(v,r,o,t.minZoom??s,t.maxZoom??i,t.padding??.1),S=sn.translate(h,p).scale(w);return typeof t.duration=="number"&&t.duration>0?l.transform(nr(a,t.duration),S):l.transform(a,S),!0}}return!1}function D_(e,t){return e.forEach(n=>{const r=t.get(n.id);r&&t.set(r.id,{...r,[ve]:r[ve],selected:n.selected})}),new Map(t)}function F_(e,t){return t.map(n=>{const r=e.find(o=>o.id===n.id);return r&&(n.selected=r.selected),n})}function $i({changedNodes:e,changedEdges:t,get:n,set:r}){const{nodeInternals:o,edges:s,onNodesChange:i,onEdgesChange:l,hasDefaultNodes:a,hasDefaultEdges:c}=n();e!=null&&e.length&&(a&&r({nodeInternals:D_(e,o)}),i==null||i(e)),t!=null&&t.length&&(c&&r({edges:F_(t,s)}),l==null||l(t))}const $r=()=>{},U_={zoomIn:$r,zoomOut:$r,zoomTo:$r,getZoom:()=>1,setViewport:$r,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:$r,fitBounds:$r,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},B_=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),H_=()=>{const e=Re(),{d3Zoom:t,d3Selection:n}=ue(B_,Pe);return b.useMemo(()=>n&&t?{zoomIn:o=>t.scaleBy(nr(n,o==null?void 0:o.duration),1.2),zoomOut:o=>t.scaleBy(nr(n,o==null?void 0:o.duration),1/1.2),zoomTo:(o,s)=>t.scaleTo(nr(n,s==null?void 0:s.duration),o),getZoom:()=>e.getState().transform[2],setViewport:(o,s)=>{const[i,l,a]=e.getState().transform,c=sn.translate(o.x??i,o.y??l).scale(o.zoom??a);t.transform(nr(n,s==null?void 0:s.duration),c)},getViewport:()=>{const[o,s,i]=e.getState().transform;return{x:o,y:s,zoom:i}},fitView:o=>Vy(e.getState,o),setCenter:(o,s,i)=>{const{width:l,height:a,maxZoom:c}=e.getState(),u=typeof(i==null?void 0:i.zoom)<"u"?i.zoom:c,f=l/2-o*u,m=a/2-s*u,g=sn.translate(f,m).scale(u);t.transform(nr(n,i==null?void 0:i.duration),g)},fitBounds:(o,s)=>{const{width:i,height:l,minZoom:a,maxZoom:c}=e.getState(),{x:u,y:f,zoom:m}=Ry(o,i,l,a,c,(s==null?void 0:s.padding)??.1),g=sn.translate(u,f).scale(m);t.transform(nr(n,s==null?void 0:s.duration),g)},project:o=>{const{transform:s,snapToGrid:i,snapGrid:l}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),zu(o,s,i,l)},screenToFlowPosition:o=>{const{transform:s,snapToGrid:i,snapGrid:l,domNode:a}=e.getState();if(!a)return o;const{x:c,y:u}=a.getBoundingClientRect(),f={x:o.x-c,y:o.y-u};return zu(f,s,i,l)},flowToScreenPosition:o=>{const{transform:s,domNode:i}=e.getState();if(!i)return o;const{x:l,y:a}=i.getBoundingClientRect(),c=_y(o,s);return{x:c.x+l,y:c.y+a}},viewportInitialized:!0}:U_,[t,n])};function uf(){const e=H_(),t=Re(),n=b.useCallback(()=>t.getState().getNodes().map(y=>({...y})),[]),r=b.useCallback(y=>t.getState().nodeInternals.get(y),[]),o=b.useCallback(()=>{const{edges:y=[]}=t.getState();return y.map(v=>({...v}))},[]),s=b.useCallback(y=>{const{edges:v=[]}=t.getState();return v.find(h=>h.id===y)},[]),i=b.useCallback(y=>{const{getNodes:v,setNodes:h,hasDefaultNodes:p,onNodesChange:w}=t.getState(),S=v(),N=typeof y=="function"?y(S):y;if(p)h(N);else if(w){const E=N.length===0?S.map(_=>({type:"remove",id:_.id})):N.map(_=>({item:_,type:"reset"}));w(E)}},[]),l=b.useCallback(y=>{const{edges:v=[],setEdges:h,hasDefaultEdges:p,onEdgesChange:w}=t.getState(),S=typeof y=="function"?y(v):y;if(p)h(S);else if(w){const N=S.length===0?v.map(E=>({type:"remove",id:E.id})):S.map(E=>({item:E,type:"reset"}));w(N)}},[]),a=b.useCallback(y=>{const v=Array.isArray(y)?y:[y],{getNodes:h,setNodes:p,hasDefaultNodes:w,onNodesChange:S}=t.getState();if(w){const E=[...h(),...v];p(E)}else if(S){const N=v.map(E=>({item:E,type:"add"}));S(N)}},[]),c=b.useCallback(y=>{const v=Array.isArray(y)?y:[y],{edges:h=[],setEdges:p,hasDefaultEdges:w,onEdgesChange:S}=t.getState();if(w)p([...h,...v]);else if(S){const N=v.map(E=>({item:E,type:"add"}));S(N)}},[]),u=b.useCallback(()=>{const{getNodes:y,edges:v=[],transform:h}=t.getState(),[p,w,S]=h;return{nodes:y().map(N=>({...N})),edges:v.map(N=>({...N})),viewport:{x:p,y:w,zoom:S}}},[]),f=b.useCallback(({nodes:y,edges:v})=>{const{nodeInternals:h,getNodes:p,edges:w,hasDefaultNodes:S,hasDefaultEdges:N,onNodesDelete:E,onEdgesDelete:_,onNodesChange:C,onEdgesChange:T}=t.getState(),P=(y||[]).map(A=>A.id),D=(v||[]).map(A=>A.id),U=p().reduce((A,R)=>{const F=R.parentNode||R.parentId,M=!P.includes(R.id)&&F&&A.find(O=>O.id===F);return(typeof R.deletable=="boolean"?R.deletable:!0)&&(P.includes(R.id)||M)&&A.push(R),A},[]),B=w.filter(A=>typeof A.deletable=="boolean"?A.deletable:!0),k=B.filter(A=>D.includes(A.id));if(U||k){const A=jy(U,B),R=[...k,...A],F=R.reduce((M,j)=>(M.includes(j.id)||M.push(j.id),M),[]);if((N||S)&&(N&&t.setState({edges:w.filter(M=>!F.includes(M.id))}),S&&(U.forEach(M=>{h.delete(M.id)}),t.setState({nodeInternals:new Map(h)}))),F.length>0&&(_==null||_(R),T&&T(F.map(M=>({id:M,type:"remove"})))),U.length>0&&(E==null||E(U),C)){const M=U.map(j=>({id:j.id,type:"remove"}));C(M)}}},[]),m=b.useCallback(y=>{const v=u_(y),h=v?null:t.getState().nodeInternals.get(y.id);return!v&&!h?[null,null,v]:[v?y:ch(h),h,v]},[]),g=b.useCallback((y,v=!0,h)=>{const[p,w,S]=m(y);return p?(h||t.getState().getNodes()).filter(N=>{if(!S&&(N.id===w.id||!N.positionAbsolute))return!1;const E=ch(N),_=Mu(E,p);return v&&_>0||_>=p.width*p.height}):[]},[]),x=b.useCallback((y,v,h=!0)=>{const[p]=m(y);if(!p)return!1;const w=Mu(p,v);return h&&w>0||w>=p.width*p.height},[]);return b.useMemo(()=>({...e,getNodes:n,getNode:r,getEdges:o,getEdge:s,setNodes:i,setEdges:l,addNodes:a,addEdges:c,toObject:u,deleteElements:f,getIntersectingNodes:g,isNodeIntersecting:x}),[e,n,r,o,s,i,l,a,c,u,f,g,x])}const V_={actInsideInputWithModifier:!1};var W_=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{const n=Re(),{deleteElements:r}=uf(),o=Hs(e,V_),s=Hs(t);b.useEffect(()=>{if(o){const{edges:i,getNodes:l}=n.getState(),a=l().filter(u=>u.selected),c=i.filter(u=>u.selected);r({nodes:a,edges:c}),n.setState({nodesSelectionActive:!1})}},[o]),b.useEffect(()=>{n.setState({multiSelectionActive:s})},[s])};function q_(e){const t=Re();b.useEffect(()=>{let n;const r=()=>{var s,i;if(!e.current)return;const o=tf(e.current);(o.height===0||o.width===0)&&((i=(s=t.getState()).onError)==null||i.call(s,"004",pn.error004())),t.setState({width:o.width||500,height:o.height||500})};return r(),window.addEventListener("resize",r),e.current&&(n=new ResizeObserver(()=>r()),n.observe(e.current)),()=>{window.removeEventListener("resize",r),n&&e.current&&n.unobserve(e.current)}},[])}const df={position:"absolute",width:"100%",height:"100%",top:0,left:0},Y_=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,Oi=e=>({x:e.x,y:e.y,zoom:e.k}),Or=(e,t)=>e.target.closest(`.${t}`),vh=(e,t)=>t===2&&Array.isArray(e)&&e.includes(2),wh=e=>{const t=e.ctrlKey&&Ul()?10:1;return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*t},G_=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),X_=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:r,zoomOnScroll:o=!0,zoomOnPinch:s=!0,panOnScroll:i=!1,panOnScrollSpeed:l=.5,panOnScrollMode:a=ar.Free,zoomOnDoubleClick:c=!0,elementsSelectable:u,panOnDrag:f=!0,defaultViewport:m,translateExtent:g,minZoom:x,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:h=!0,children:p,noWheelClassName:w,noPanClassName:S})=>{const N=b.useRef(),E=Re(),_=b.useRef(!1),C=b.useRef(!1),T=b.useRef(null),P=b.useRef({x:0,y:0,zoom:0}),{d3Zoom:D,d3Selection:U,d3ZoomHandler:B,userSelectionActive:k}=ue(G_,Pe),A=Hs(v),R=b.useRef(0),F=b.useRef(!1),M=b.useRef();return q_(T),b.useEffect(()=>{if(T.current){const j=T.current.getBoundingClientRect(),O=py().scaleExtent([x,y]).translateExtent(g),$=xt(T.current).call(O),L=sn.translate(m.x,m.y).scale(bo(m.zoom,x,y)),V=[[0,0],[j.width,j.height]],W=O.constrain()(L,V,g);O.transform($,W),O.wheelDelta(wh),E.setState({d3Zoom:O,d3Selection:$,d3ZoomHandler:$.on("wheel.zoom"),transform:[W.x,W.y,W.k],domNode:T.current.closest(".react-flow")})}},[]),b.useEffect(()=>{U&&D&&(i&&!A&&!k?U.on("wheel.zoom",j=>{if(Or(j,w))return!1;j.preventDefault(),j.stopImmediatePropagation();const O=U.property("__zoom").k||1;if(j.ctrlKey&&s){const ie=jt(j),se=wh(j),oe=O*Math.pow(2,se);D.scaleTo(U,oe,ie,j);return}const $=j.deltaMode===1?20:1;let L=a===ar.Vertical?0:j.deltaX*$,V=a===ar.Horizontal?0:j.deltaY*$;!Ul()&&j.shiftKey&&a!==ar.Vertical&&(L=j.deltaY*$,V=0),D.translateBy(U,-(L/O)*l,-(V/O)*l,{internal:!0});const W=Oi(U.property("__zoom")),{onViewportChangeStart:Y,onViewportChange:K,onViewportChangeEnd:J}=E.getState();clearTimeout(M.current),F.current||(F.current=!0,t==null||t(j,W),Y==null||Y(W)),F.current&&(e==null||e(j,W),K==null||K(W),M.current=setTimeout(()=>{n==null||n(j,W),J==null||J(W),F.current=!1},150))},{passive:!1}):typeof B<"u"&&U.on("wheel.zoom",function(j,O){if(!h&&j.type==="wheel"&&!j.ctrlKey||Or(j,w))return null;j.preventDefault(),B.call(this,j,O)},{passive:!1}))},[k,i,a,U,D,B,A,s,h,w,t,e,n]),b.useEffect(()=>{D&&D.on("start",j=>{var L,V;if(!j.sourceEvent||j.sourceEvent.internal)return null;R.current=(L=j.sourceEvent)==null?void 0:L.button;const{onViewportChangeStart:O}=E.getState(),$=Oi(j.transform);_.current=!0,P.current=$,((V=j.sourceEvent)==null?void 0:V.type)==="mousedown"&&E.setState({paneDragging:!0}),O==null||O($),t==null||t(j.sourceEvent,$)})},[D,t]),b.useEffect(()=>{D&&(k&&!_.current?D.on("zoom",null):k||D.on("zoom",j=>{var $;const{onViewportChange:O}=E.getState();if(E.setState({transform:[j.transform.x,j.transform.y,j.transform.k]}),C.current=!!(r&&vh(f,R.current??0)),(e||O)&&!(($=j.sourceEvent)!=null&&$.internal)){const L=Oi(j.transform);O==null||O(L),e==null||e(j.sourceEvent,L)}}))},[k,D,e,f,r]),b.useEffect(()=>{D&&D.on("end",j=>{if(!j.sourceEvent||j.sourceEvent.internal)return null;const{onViewportChangeEnd:O}=E.getState();if(_.current=!1,E.setState({paneDragging:!1}),r&&vh(f,R.current??0)&&!C.current&&r(j.sourceEvent),C.current=!1,(n||O)&&Y_(P.current,j.transform)){const $=Oi(j.transform);P.current=$,clearTimeout(N.current),N.current=setTimeout(()=>{O==null||O($),n==null||n(j.sourceEvent,$)},i?150:0)}})},[D,i,f,n,r]),b.useEffect(()=>{D&&D.filter(j=>{const O=A||o,$=s&&j.ctrlKey;if((f===!0||Array.isArray(f)&&f.includes(1))&&j.button===1&&j.type==="mousedown"&&(Or(j,"react-flow__node")||Or(j,"react-flow__edge")))return!0;if(!f&&!O&&!i&&!c&&!s||k||!c&&j.type==="dblclick"||Or(j,w)&&j.type==="wheel"||Or(j,S)&&(j.type!=="wheel"||i&&j.type==="wheel"&&!A)||!s&&j.ctrlKey&&j.type==="wheel"||!O&&!i&&!$&&j.type==="wheel"||!f&&(j.type==="mousedown"||j.type==="touchstart")||Array.isArray(f)&&!f.includes(j.button)&&j.type==="mousedown")return!1;const L=Array.isArray(f)&&f.includes(j.button)||!j.button||j.button<=1;return(!j.ctrlKey||j.type==="wheel")&&L})},[k,D,o,s,i,c,f,u,A]),I.createElement("div",{className:"react-flow__renderer",ref:T,style:df},p)},K_=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Q_(){const{userSelectionActive:e,userSelectionRect:t}=ue(K_,Pe);return e&&t?I.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function Sh(e,t){const n=t.parentNode||t.parentId,r=e.find(o=>o.id===n);if(r){const o=t.position.x+t.width-r.width,s=t.position.y+t.height-r.height;if(o>0||s>0||t.position.x<0||t.position.y<0){if(r.style={...r.style},r.style.width=r.style.width??r.width,r.style.height=r.style.height??r.height,o>0&&(r.style.width+=o),s>0&&(r.style.height+=s),t.position.x<0){const i=Math.abs(t.position.x);r.position.x=r.position.x-i,r.style.width+=i,t.position.x=0}if(t.position.y<0){const i=Math.abs(t.position.y);r.position.y=r.position.y-i,r.style.height+=i,t.position.y=0}r.width=r.style.width,r.height=r.style.height}}}function Wy(e,t){if(e.some(r=>r.type==="reset"))return e.filter(r=>r.type==="reset").map(r=>r.item);const n=e.filter(r=>r.type==="add").map(r=>r.item);return t.reduce((r,o)=>{const s=e.filter(l=>l.id===o.id);if(s.length===0)return r.push(o),r;const i={...o};for(const l of s)if(l)switch(l.type){case"select":{i.selected=l.selected;break}case"position":{typeof l.position<"u"&&(i.position=l.position),typeof l.positionAbsolute<"u"&&(i.positionAbsolute=l.positionAbsolute),typeof l.dragging<"u"&&(i.dragging=l.dragging),i.expandParent&&Sh(r,i);break}case"dimensions":{typeof l.dimensions<"u"&&(i.width=l.dimensions.width,i.height=l.dimensions.height),typeof l.updateStyle<"u"&&(i.style={...i.style||{},...l.dimensions}),typeof l.resizing=="boolean"&&(i.resizing=l.resizing),i.expandParent&&Sh(r,i);break}case"remove":return r}return r.push(i),r},n)}function qy(e,t){return Wy(e,t)}function Z_(e,t){return Wy(e,t)}const bn=(e,t)=>({id:e,type:"select",selected:t});function Zr(e,t){return e.reduce((n,r)=>{const o=t.includes(r.id);return!r.selected&&o?(r.selected=!0,n.push(bn(r.id,!0))):r.selected&&!o&&(r.selected=!1,n.push(bn(r.id,!1))),n},[])}const pc=(e,t)=>n=>{n.target===t.current&&(e==null||e(n))},J_=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),Yy=b.memo(({isSelecting:e,selectionMode:t=Bs.Full,panOnDrag:n,onSelectionStart:r,onSelectionEnd:o,onPaneClick:s,onPaneContextMenu:i,onPaneScroll:l,onPaneMouseEnter:a,onPaneMouseMove:c,onPaneMouseLeave:u,children:f})=>{const m=b.useRef(null),g=Re(),x=b.useRef(0),y=b.useRef(0),v=b.useRef(),{userSelectionActive:h,elementsSelectable:p,dragging:w}=ue(J_,Pe),S=()=>{g.setState({userSelectionActive:!1,userSelectionRect:null}),x.current=0,y.current=0},N=B=>{s==null||s(B),g.getState().resetSelectedElements(),g.setState({nodesSelectionActive:!1})},E=B=>{if(Array.isArray(n)&&(n!=null&&n.includes(2))){B.preventDefault();return}i==null||i(B)},_=l?B=>l(B):void 0,C=B=>{const{resetSelectedElements:k,domNode:A}=g.getState();if(v.current=A==null?void 0:A.getBoundingClientRect(),!p||!e||B.button!==0||B.target!==m.current||!v.current)return;const{x:R,y:F}=Dn(B,v.current);k(),g.setState({userSelectionRect:{width:0,height:0,startX:R,startY:F,x:R,y:F}}),r==null||r(B)},T=B=>{const{userSelectionRect:k,nodeInternals:A,edges:R,transform:F,onNodesChange:M,onEdgesChange:j,nodeOrigin:O,getNodes:$}=g.getState();if(!e||!v.current||!k)return;g.setState({userSelectionActive:!0,nodesSelectionActive:!1});const L=Dn(B,v.current),V=k.startX??0,W=k.startY??0,Y={...k,x:L.x<V?L.x:V,y:L.y<W?L.y:W,width:Math.abs(L.x-V),height:Math.abs(L.y-W)},K=$(),J=Cy(A,Y,F,t===Bs.Partial,!0,O),ie=jy(J,R).map(oe=>oe.id),se=J.map(oe=>oe.id);if(x.current!==se.length){x.current=se.length;const oe=Zr(K,se);oe.length&&(M==null||M(oe))}if(y.current!==ie.length){y.current=ie.length;const oe=Zr(R,ie);oe.length&&(j==null||j(oe))}g.setState({userSelectionRect:Y})},P=B=>{if(B.button!==0)return;const{userSelectionRect:k}=g.getState();!h&&k&&B.target===m.current&&(N==null||N(B)),g.setState({nodesSelectionActive:x.current>0}),S(),o==null||o(B)},D=B=>{h&&(g.setState({nodesSelectionActive:x.current>0}),o==null||o(B)),S()},U=p&&(e||h);return I.createElement("div",{className:Le(["react-flow__pane",{dragging:w,selection:e}]),onClick:U?void 0:pc(N,m),onContextMenu:pc(E,m),onWheel:pc(_,m),onMouseEnter:U?void 0:a,onMouseDown:U?C:void 0,onMouseMove:U?T:c,onMouseUp:U?P:void 0,onMouseLeave:U?D:u,ref:m,style:df},f,I.createElement(Q_,null))});Yy.displayName="Pane";function Gy(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const r=t.get(n);return r?r.selected?!0:Gy(r,t):!1}function bh(e,t,n){let r=e;do{if(r!=null&&r.matches(t))return!0;if(r===n.current)return!1;r=r.parentElement}while(r);return!1}function eC(e,t,n,r){return Array.from(e.values()).filter(o=>(o.selected||o.id===r)&&(!o.parentNode||o.parentId||!Gy(o,e))&&(o.draggable||t&&typeof o.draggable>"u")).map(o=>{var s,i;return{id:o.id,position:o.position||{x:0,y:0},positionAbsolute:o.positionAbsolute||{x:0,y:0},distance:{x:n.x-(((s=o.positionAbsolute)==null?void 0:s.x)??0),y:n.y-(((i=o.positionAbsolute)==null?void 0:i.y)??0)},delta:{x:0,y:0},extent:o.extent,parentNode:o.parentNode||o.parentId,parentId:o.parentNode||o.parentId,width:o.width,height:o.height,expandParent:o.expandParent}})}function tC(e,t){return!t||t==="parent"?t:[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]}function Xy(e,t,n,r,o=[0,0],s){const i=tC(e,e.extent||r);let l=i;const a=e.parentNode||e.parentId;if(e.extent==="parent"&&!e.expandParent)if(a&&e.width&&e.height){const f=n.get(a),{x:m,y:g}=pr(f,o).positionAbsolute;l=f&&wt(m)&&wt(g)&&wt(f.width)&&wt(f.height)?[[m+e.width*o[0],g+e.height*o[1]],[m+f.width-e.width+e.width*o[0],g+f.height-e.height+e.height*o[1]]]:l}else s==null||s("005",pn.error005()),l=i;else if(e.extent&&a&&e.extent!=="parent"){const f=n.get(a),{x:m,y:g}=pr(f,o).positionAbsolute;l=[[e.extent[0][0]+m,e.extent[0][1]+g],[e.extent[1][0]+m,e.extent[1][1]+g]]}let c={x:0,y:0};if(a){const f=n.get(a);c=pr(f,o).positionAbsolute}const u=l&&l!=="parent"?nf(t,l):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function hc({nodeId:e,dragItems:t,nodeInternals:n}){const r=t.map(o=>({...n.get(o.id),position:o.position,positionAbsolute:o.positionAbsolute}));return[e?r.find(o=>o.id===e):r[0],r]}const Nh=(e,t,n,r)=>{const o=t.querySelectorAll(e);if(!o||!o.length)return null;const s=Array.from(o),i=t.getBoundingClientRect(),l={x:i.width*r[0],y:i.height*r[1]};return s.map(a=>{const c=a.getBoundingClientRect();return{id:a.getAttribute("data-handleid"),position:a.getAttribute("data-handlepos"),x:(c.left-i.left-l.x)/n,y:(c.top-i.top-l.y)/n,...tf(a)}})};function qo(e,t,n){return n===void 0?n:r=>{const o=t().nodeInternals.get(e);o&&n(r,{...o})}}function Du({id:e,store:t,unselect:n=!1,nodeRef:r}){const{addSelectedNodes:o,unselectNodesAndEdges:s,multiSelectionActive:i,nodeInternals:l,onError:a}=t.getState(),c=l.get(e);if(!c){a==null||a("012",pn.error012(e));return}t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&i)&&(s({nodes:[c],edges:[]}),requestAnimationFrame(()=>{var u;return(u=r==null?void 0:r.current)==null?void 0:u.blur()})):o([e])}function nC(){const e=Re();return b.useCallback(({sourceEvent:n})=>{const{transform:r,snapGrid:o,snapToGrid:s}=e.getState(),i=n.touches?n.touches[0].clientX:n.clientX,l=n.touches?n.touches[0].clientY:n.clientY,a={x:(i-r[0])/r[2],y:(l-r[1])/r[2]};return{xSnapped:s?o[0]*Math.round(a.x/o[0]):a.x,ySnapped:s?o[1]*Math.round(a.y/o[1]):a.y,...a}},[])}function mc(e){return(t,n,r)=>e==null?void 0:e(t,r)}function Ky({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:r,nodeId:o,isSelectable:s,selectNodesOnDrag:i}){const l=Re(),[a,c]=b.useState(!1),u=b.useRef([]),f=b.useRef({x:null,y:null}),m=b.useRef(0),g=b.useRef(null),x=b.useRef({x:0,y:0}),y=b.useRef(null),v=b.useRef(!1),h=b.useRef(!1),p=b.useRef(!1),w=nC();return b.useEffect(()=>{if(e!=null&&e.current){const S=xt(e.current),N=({x:C,y:T})=>{const{nodeInternals:P,onNodeDrag:D,onSelectionDrag:U,updateNodePositions:B,nodeExtent:k,snapGrid:A,snapToGrid:R,nodeOrigin:F,onError:M}=l.getState();f.current={x:C,y:T};let j=!1,O={x:0,y:0,x2:0,y2:0};if(u.current.length>1&&k){const L=va(u.current,F);O=Us(L)}if(u.current=u.current.map(L=>{const V={x:C-L.distance.x,y:T-L.distance.y};R&&(V.x=A[0]*Math.round(V.x/A[0]),V.y=A[1]*Math.round(V.y/A[1]));const W=[[k[0][0],k[0][1]],[k[1][0],k[1][1]]];u.current.length>1&&k&&!L.extent&&(W[0][0]=L.positionAbsolute.x-O.x+k[0][0],W[1][0]=L.positionAbsolute.x+(L.width??0)-O.x2+k[1][0],W[0][1]=L.positionAbsolute.y-O.y+k[0][1],W[1][1]=L.positionAbsolute.y+(L.height??0)-O.y2+k[1][1]);const Y=Xy(L,V,P,W,F,M);return j=j||L.position.x!==Y.position.x||L.position.y!==Y.position.y,L.position=Y.position,L.positionAbsolute=Y.positionAbsolute,L}),!j)return;B(u.current,!0,!0),c(!0);const $=o?D:mc(U);if($&&y.current){const[L,V]=hc({nodeId:o,dragItems:u.current,nodeInternals:P});$(y.current,L,V)}},E=()=>{if(!g.current)return;const[C,T]=my(x.current,g.current);if(C!==0||T!==0){const{transform:P,panBy:D}=l.getState();f.current.x=(f.current.x??0)-C/P[2],f.current.y=(f.current.y??0)-T/P[2],D({x:C,y:T})&&N(f.current)}m.current=requestAnimationFrame(E)},_=C=>{var F;const{nodeInternals:T,multiSelectionActive:P,nodesDraggable:D,unselectNodesAndEdges:U,onNodeDragStart:B,onSelectionDragStart:k}=l.getState();h.current=!0;const A=o?B:mc(k);(!i||!s)&&!P&&o&&((F=T.get(o))!=null&&F.selected||U()),o&&s&&i&&Du({id:o,store:l,nodeRef:e});const R=w(C);if(f.current=R,u.current=eC(T,D,R,o),A&&u.current){const[M,j]=hc({nodeId:o,dragItems:u.current,nodeInternals:T});A(C.sourceEvent,M,j)}};if(t)S.on(".drag",null);else{const C=hE().on("start",T=>{const{domNode:P,nodeDragThreshold:D}=l.getState();D===0&&_(T),p.current=!1;const U=w(T);f.current=U,g.current=(P==null?void 0:P.getBoundingClientRect())||null,x.current=Dn(T.sourceEvent,g.current)}).on("drag",T=>{var B,k;const P=w(T),{autoPanOnNodeDrag:D,nodeDragThreshold:U}=l.getState();if(T.sourceEvent.type==="touchmove"&&T.sourceEvent.touches.length>1&&(p.current=!0),!p.current){if(!v.current&&h.current&&D&&(v.current=!0,E()),!h.current){const A=P.xSnapped-(((B=f==null?void 0:f.current)==null?void 0:B.x)??0),R=P.ySnapped-(((k=f==null?void 0:f.current)==null?void 0:k.y)??0);Math.sqrt(A*A+R*R)>U&&_(T)}(f.current.x!==P.xSnapped||f.current.y!==P.ySnapped)&&u.current&&h.current&&(y.current=T.sourceEvent,x.current=Dn(T.sourceEvent,g.current),N(P))}}).on("end",T=>{if(!(!h.current||p.current)&&(c(!1),v.current=!1,h.current=!1,cancelAnimationFrame(m.current),u.current)){const{updateNodePositions:P,nodeInternals:D,onNodeDragStop:U,onSelectionDragStop:B}=l.getState(),k=o?U:mc(B);if(P(u.current,!1,!1),k){const[A,R]=hc({nodeId:o,dragItems:u.current,nodeInternals:D});k(T.sourceEvent,A,R)}}}).filter(T=>{const P=T.target;return!T.button&&(!n||!bh(P,`.${n}`,e))&&(!r||bh(P,r,e))});return S.call(C),()=>{S.on(".drag",null)}}}},[e,t,n,r,s,l,o,i,w]),a}function Qy(){const e=Re();return b.useCallback(n=>{const{nodeInternals:r,nodeExtent:o,updateNodePositions:s,getNodes:i,snapToGrid:l,snapGrid:a,onError:c,nodesDraggable:u}=e.getState(),f=i().filter(p=>p.selected&&(p.draggable||u&&typeof p.draggable>"u")),m=l?a[0]:5,g=l?a[1]:5,x=n.isShiftPressed?4:1,y=n.x*m*x,v=n.y*g*x,h=f.map(p=>{if(p.positionAbsolute){const w={x:p.positionAbsolute.x+y,y:p.positionAbsolute.y+v};l&&(w.x=a[0]*Math.round(w.x/a[0]),w.y=a[1]*Math.round(w.y/a[1]));const{positionAbsolute:S,position:N}=Xy(p,w,r,o,void 0,c);p.position=N,p.positionAbsolute=S}return p});s(h,!0,!1)},[])}const co={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var Yo=e=>{const t=({id:n,type:r,data:o,xPos:s,yPos:i,xPosOrigin:l,yPosOrigin:a,selected:c,onClick:u,onMouseEnter:f,onMouseMove:m,onMouseLeave:g,onContextMenu:x,onDoubleClick:y,style:v,className:h,isDraggable:p,isSelectable:w,isConnectable:S,isFocusable:N,selectNodesOnDrag:E,sourcePosition:_,targetPosition:C,hidden:T,resizeObserver:P,dragHandle:D,zIndex:U,isParent:B,noDragClassName:k,noPanClassName:A,initialized:R,disableKeyboardA11y:F,ariaLabel:M,rfId:j,hasHandleBounds:O})=>{const $=Re(),L=b.useRef(null),V=b.useRef(null),W=b.useRef(_),Y=b.useRef(C),K=b.useRef(r),J=w||p||u||f||m||g,ie=Qy(),se=qo(n,$.getState,f),oe=qo(n,$.getState,m),Me=qo(n,$.getState,g),_e=qo(n,$.getState,x),qe=qo(n,$.getState,y),De=ae=>{const{nodeDragThreshold:X}=$.getState();if(w&&(!E||!p||X>0)&&Du({id:n,store:$,nodeRef:L}),u){const Ye=$.getState().nodeInternals.get(n);Ye&&u(ae,{...Ye})}},we=ae=>{if(!$u(ae)&&!F)if(vy.includes(ae.key)&&w){const X=ae.key==="Escape";Du({id:n,store:$,unselect:X,nodeRef:L})}else p&&c&&Object.prototype.hasOwnProperty.call(co,ae.key)&&($.setState({ariaLiveMessage:`Moved selected node ${ae.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~s}, y: ${~~i}`}),ie({x:co[ae.key].x,y:co[ae.key].y,isShiftPressed:ae.shiftKey}))};b.useEffect(()=>()=>{V.current&&(P==null||P.unobserve(V.current),V.current=null)},[]),b.useEffect(()=>{if(L.current&&!T){const ae=L.current;(!R||!O||V.current!==ae)&&(V.current&&(P==null||P.unobserve(V.current)),P==null||P.observe(ae),V.current=ae)}},[T,R,O]),b.useEffect(()=>{const ae=K.current!==r,X=W.current!==_,Ye=Y.current!==C;L.current&&(ae||X||Ye)&&(ae&&(K.current=r),X&&(W.current=_),Ye&&(Y.current=C),$.getState().updateNodeDimensions([{id:n,nodeElement:L.current,forceUpdate:!0}]))},[n,r,_,C]);const lt=Ky({nodeRef:L,disabled:T||!p,noDragClassName:k,handleSelector:D,nodeId:n,isSelectable:w,selectNodesOnDrag:E});return T?null:I.createElement("div",{className:Le(["react-flow__node",`react-flow__node-${r}`,{[A]:p},h,{selected:c,selectable:w,parent:B,dragging:lt}]),ref:L,style:{zIndex:U,transform:`translate(${l}px,${a}px)`,pointerEvents:J?"all":"none",visibility:R?"visible":"hidden",...v},"data-id":n,"data-testid":`rf__node-${n}`,onMouseEnter:se,onMouseMove:oe,onMouseLeave:Me,onContextMenu:_e,onClick:De,onDoubleClick:qe,onKeyDown:N?we:void 0,tabIndex:N?0:void 0,role:N?"button":void 0,"aria-describedby":F?void 0:`${Fy}-${j}`,"aria-label":M},I.createElement(y_,{value:n},I.createElement(e,{id:n,data:o,type:r,xPos:s,yPos:i,selected:c,isConnectable:S,sourcePosition:_,targetPosition:C,dragging:lt,dragHandle:D,zIndex:U})))};return t.displayName="NodeWrapper",b.memo(t)};const rC=e=>{const t=e.getNodes().filter(n=>n.selected);return{...va(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};function oC({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){const r=Re(),{width:o,height:s,x:i,y:l,transformString:a,userSelectionActive:c}=ue(rC,Pe),u=Qy(),f=b.useRef(null);if(b.useEffect(()=>{var x;n||(x=f.current)==null||x.focus({preventScroll:!0})},[n]),Ky({nodeRef:f}),c||!o||!s)return null;const m=e?x=>{const y=r.getState().getNodes().filter(v=>v.selected);e(x,y)}:void 0,g=x=>{Object.prototype.hasOwnProperty.call(co,x.key)&&u({x:co[x.key].x,y:co[x.key].y,isShiftPressed:x.shiftKey})};return I.createElement("div",{className:Le(["react-flow__nodesselection","react-flow__container",t]),style:{transform:a}},I.createElement("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:m,tabIndex:n?void 0:-1,onKeyDown:n?void 0:g,style:{width:o,height:s,top:l,left:i}}))}var sC=b.memo(oC);const iC=e=>e.nodesSelectionActive,Zy=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:s,onPaneScroll:i,deleteKeyCode:l,onMove:a,onMoveStart:c,onMoveEnd:u,selectionKeyCode:f,selectionOnDrag:m,selectionMode:g,onSelectionStart:x,onSelectionEnd:y,multiSelectionKeyCode:v,panActivationKeyCode:h,zoomActivationKeyCode:p,elementsSelectable:w,zoomOnScroll:S,zoomOnPinch:N,panOnScroll:E,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:T,panOnDrag:P,defaultViewport:D,translateExtent:U,minZoom:B,maxZoom:k,preventScrolling:A,onSelectionContextMenu:R,noWheelClassName:F,noPanClassName:M,disableKeyboardA11y:j})=>{const O=ue(iC),$=Hs(f),L=Hs(h),V=L||P,W=L||E,Y=$||m&&V!==!0;return W_({deleteKeyCode:l,multiSelectionKeyCode:v}),I.createElement(X_,{onMove:a,onMoveStart:c,onMoveEnd:u,onPaneContextMenu:s,elementsSelectable:w,zoomOnScroll:S,zoomOnPinch:N,panOnScroll:W,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:T,panOnDrag:!$&&V,defaultViewport:D,translateExtent:U,minZoom:B,maxZoom:k,zoomActivationKeyCode:p,preventScrolling:A,noWheelClassName:F,noPanClassName:M},I.createElement(Yy,{onSelectionStart:x,onSelectionEnd:y,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:s,onPaneScroll:i,panOnDrag:V,isSelecting:!!Y,selectionMode:g},e,O&&I.createElement(sC,{onSelectionContextMenu:R,noPanClassName:M,disableKeyboardA11y:j})))};Zy.displayName="FlowRenderer";var lC=b.memo(Zy);function aC(e){return ue(b.useCallback(n=>e?Cy(n.nodeInternals,{x:0,y:0,width:n.width,height:n.height},n.transform,!0):n.getNodes(),[e]))}function cC(e){const t={input:Yo(e.input||Iy),default:Yo(e.default||Lu),output:Yo(e.output||Ly),group:Yo(e.group||cf)},n={},r=Object.keys(e).filter(o=>!["input","default","output","group"].includes(o)).reduce((o,s)=>(o[s]=Yo(e[s]||Lu),o),n);return{...t,...r}}const uC=({x:e,y:t,width:n,height:r,origin:o})=>!n||!r?{x:e,y:t}:o[0]<0||o[1]<0||o[0]>1||o[1]>1?{x:e,y:t}:{x:e-n*o[0],y:t-r*o[1]},dC=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),Jy=e=>{const{nodesDraggable:t,nodesConnectable:n,nodesFocusable:r,elementsSelectable:o,updateNodeDimensions:s,onError:i}=ue(dC,Pe),l=aC(e.onlyRenderVisibleElements),a=b.useRef(),c=b.useMemo(()=>{if(typeof ResizeObserver>"u")return null;const u=new ResizeObserver(f=>{const m=f.map(g=>({id:g.target.getAttribute("data-id"),nodeElement:g.target,forceUpdate:!0}));s(m)});return a.current=u,u},[]);return b.useEffect(()=>()=>{var u;(u=a==null?void 0:a.current)==null||u.disconnect()},[]),I.createElement("div",{className:"react-flow__nodes",style:df},l.map(u=>{var N,E,_;let f=u.type||"default";e.nodeTypes[f]||(i==null||i("003",pn.error003(f)),f="default");const m=e.nodeTypes[f]||e.nodeTypes.default,g=!!(u.draggable||t&&typeof u.draggable>"u"),x=!!(u.selectable||o&&typeof u.selectable>"u"),y=!!(u.connectable||n&&typeof u.connectable>"u"),v=!!(u.focusable||r&&typeof u.focusable>"u"),h=e.nodeExtent?nf(u.positionAbsolute,e.nodeExtent):u.positionAbsolute,p=(h==null?void 0:h.x)??0,w=(h==null?void 0:h.y)??0,S=uC({x:p,y:w,width:u.width??0,height:u.height??0,origin:e.nodeOrigin});return I.createElement(m,{key:u.id,id:u.id,className:u.className,style:u.style,type:f,data:u.data,sourcePosition:u.sourcePosition||G.Bottom,targetPosition:u.targetPosition||G.Top,hidden:u.hidden,xPos:p,yPos:w,xPosOrigin:S.x,yPosOrigin:S.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!u.selected,isDraggable:g,isSelectable:x,isConnectable:y,isFocusable:v,resizeObserver:c,dragHandle:u.dragHandle,zIndex:((N=u[ve])==null?void 0:N.z)??0,isParent:!!((E=u[ve])!=null&&E.isParent),noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!u.width&&!!u.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:u.ariaLabel,hasHandleBounds:!!((_=u[ve])!=null&&_.handleBounds)})}))};Jy.displayName="NodeRenderer";var fC=b.memo(Jy);const pC=(e,t,n)=>n===G.Left?e-t:n===G.Right?e+t:e,hC=(e,t,n)=>n===G.Top?e-t:n===G.Bottom?e+t:e,Eh="react-flow__edgeupdater",kh=({position:e,centerX:t,centerY:n,radius:r=10,onMouseDown:o,onMouseEnter:s,onMouseOut:i,type:l})=>I.createElement("circle",{onMouseDown:o,onMouseEnter:s,onMouseOut:i,className:Le([Eh,`${Eh}-${l}`]),cx:pC(t,r,e),cy:hC(n,r,e),r,stroke:"transparent",fill:"transparent"}),mC=()=>!0;var Ir=e=>{const t=({id:n,className:r,type:o,data:s,onClick:i,onEdgeDoubleClick:l,selected:a,animated:c,label:u,labelStyle:f,labelShowBg:m,labelBgStyle:g,labelBgPadding:x,labelBgBorderRadius:y,style:v,source:h,target:p,sourceX:w,sourceY:S,targetX:N,targetY:E,sourcePosition:_,targetPosition:C,elementsSelectable:T,hidden:P,sourceHandleId:D,targetHandleId:U,onContextMenu:B,onMouseEnter:k,onMouseMove:A,onMouseLeave:R,reconnectRadius:F,onReconnect:M,onReconnectStart:j,onReconnectEnd:O,markerEnd:$,markerStart:L,rfId:V,ariaLabel:W,isFocusable:Y,isReconnectable:K,pathOptions:J,interactionWidth:ie,disableKeyboardA11y:se})=>{const oe=b.useRef(null),[Me,_e]=b.useState(!1),[qe,De]=b.useState(!1),we=Re(),lt=b.useMemo(()=>`url('#${Iu(L,V)}')`,[L,V]),ae=b.useMemo(()=>`url('#${Iu($,V)}')`,[$,V]);if(P)return null;const X=Fe=>{var zt;const{edges:Et,addSelectedEdges:Kn,unselectNodesAndEdges:Qn,multiSelectionActive:Zn}=we.getState(),Xt=Et.find(Mo=>Mo.id===n);Xt&&(T&&(we.setState({nodesSelectionActive:!1}),Xt.selected&&Zn?(Qn({nodes:[],edges:[Xt]}),(zt=oe.current)==null||zt.blur()):Kn([n])),i&&i(Fe,Xt))},Ye=Wo(n,we.getState,l),Yt=Wo(n,we.getState,B),To=Wo(n,we.getState,k),Cr=Wo(n,we.getState,A),jr=Wo(n,we.getState,R),Gt=(Fe,Et)=>{if(Fe.button!==0)return;const{edges:Kn,isValidConnection:Qn}=we.getState(),Zn=Et?p:h,Xt=(Et?U:D)||null,zt=Et?"target":"source",Mo=Qn||mC,Aa=Et,$o=Kn.find(Jn=>Jn.id===n);De(!0),j==null||j(Fe,$o,zt);const Ta=Jn=>{De(!1),O==null||O(Jn,$o,zt)};Py({event:Fe,handleId:Xt,nodeId:Zn,onConnect:Jn=>M==null?void 0:M($o,Jn),isTarget:Aa,getState:we.getState,setState:we.setState,isValidConnection:Mo,edgeUpdaterType:zt,onReconnectEnd:Ta})},Rr=Fe=>Gt(Fe,!0),Gn=Fe=>Gt(Fe,!1),Xn=()=>_e(!0),Ar=()=>_e(!1),Tr=!T&&!i,Po=Fe=>{var Et;if(!se&&vy.includes(Fe.key)&&T){const{unselectNodesAndEdges:Kn,addSelectedEdges:Qn,edges:Zn}=we.getState();Fe.key==="Escape"?((Et=oe.current)==null||Et.blur(),Kn({edges:[Zn.find(zt=>zt.id===n)]})):Qn([n])}};return I.createElement("g",{className:Le(["react-flow__edge",`react-flow__edge-${o}`,r,{selected:a,animated:c,inactive:Tr,updating:Me}]),onClick:X,onDoubleClick:Ye,onContextMenu:Yt,onMouseEnter:To,onMouseMove:Cr,onMouseLeave:jr,onKeyDown:Y?Po:void 0,tabIndex:Y?0:void 0,role:Y?"button":"img","data-testid":`rf__edge-${n}`,"aria-label":W===null?void 0:W||`Edge from ${h} to ${p}`,"aria-describedby":Y?`${Uy}-${V}`:void 0,ref:oe},!qe&&I.createElement(e,{id:n,source:h,target:p,selected:a,animated:c,label:u,labelStyle:f,labelShowBg:m,labelBgStyle:g,labelBgPadding:x,labelBgBorderRadius:y,data:s,style:v,sourceX:w,sourceY:S,targetX:N,targetY:E,sourcePosition:_,targetPosition:C,sourceHandleId:D,targetHandleId:U,markerStart:lt,markerEnd:ae,pathOptions:J,interactionWidth:ie}),K&&I.createElement(I.Fragment,null,(K==="source"||K===!0)&&I.createElement(kh,{position:_,centerX:w,centerY:S,radius:F,onMouseDown:Rr,onMouseEnter:Xn,onMouseOut:Ar,type:"source"}),(K==="target"||K===!0)&&I.createElement(kh,{position:C,centerX:N,centerY:E,radius:F,onMouseDown:Gn,onMouseEnter:Xn,onMouseOut:Ar,type:"target"})))};return t.displayName="EdgeWrapper",b.memo(t)};function gC(e){const t={default:Ir(e.default||Hl),straight:Ir(e.bezier||sf),step:Ir(e.step||of),smoothstep:Ir(e.step||xa),simplebezier:Ir(e.simplebezier||rf)},n={},r=Object.keys(e).filter(o=>!["default","bezier"].includes(o)).reduce((o,s)=>(o[s]=Ir(e[s]||Hl),o),n);return{...t,...r}}function _h(e,t,n=null){const r=((n==null?void 0:n.x)||0)+t.x,o=((n==null?void 0:n.y)||0)+t.y,s=(n==null?void 0:n.width)||t.width,i=(n==null?void 0:n.height)||t.height;switch(e){case G.Top:return{x:r+s/2,y:o};case G.Right:return{x:r+s,y:o+i/2};case G.Bottom:return{x:r+s/2,y:o+i};case G.Left:return{x:r,y:o+i/2}}}function Ch(e,t){return e?e.length===1||!t?e[0]:t&&e.find(n=>n.id===t)||null:null}const yC=(e,t,n,r,o,s)=>{const i=_h(n,e,t),l=_h(s,r,o);return{sourceX:i.x,sourceY:i.y,targetX:l.x,targetY:l.y}};function xC({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:r,targetWidth:o,targetHeight:s,width:i,height:l,transform:a}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+r,t.y+s)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=Us({x:(0-a[0])/a[2],y:(0-a[1])/a[2],width:i/a[2],height:l/a[2]}),f=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),m=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(f*m)>0}function jh(e){var r,o,s,i,l;const t=((r=e==null?void 0:e[ve])==null?void 0:r.handleBounds)||null,n=t&&(e==null?void 0:e.width)&&(e==null?void 0:e.height)&&typeof((o=e==null?void 0:e.positionAbsolute)==null?void 0:o.x)<"u"&&typeof((s=e==null?void 0:e.positionAbsolute)==null?void 0:s.y)<"u";return[{x:((i=e==null?void 0:e.positionAbsolute)==null?void 0:i.x)||0,y:((l=e==null?void 0:e.positionAbsolute)==null?void 0:l.y)||0,width:(e==null?void 0:e.width)||0,height:(e==null?void 0:e.height)||0},t,!!n]}const vC=[{level:0,isMaxLevel:!0,edges:[]}];function wC(e,t,n=!1){let r=-1;const o=e.reduce((i,l)=>{var u,f;const a=wt(l.zIndex);let c=a?l.zIndex:0;if(n){const m=t.get(l.target),g=t.get(l.source),x=l.selected||(m==null?void 0:m.selected)||(g==null?void 0:g.selected),y=Math.max(((u=g==null?void 0:g[ve])==null?void 0:u.z)||0,((f=m==null?void 0:m[ve])==null?void 0:f.z)||0,1e3);c=(a?l.zIndex:0)+(x?y:0)}return i[c]?i[c].push(l):i[c]=[l],r=c>r?c:r,i},{}),s=Object.entries(o).map(([i,l])=>{const a=+i;return{edges:l,level:a,isMaxLevel:a===r}});return s.length===0?vC:s}function SC(e,t,n){const r=ue(b.useCallback(o=>e?o.edges.filter(s=>{const i=t.get(s.source),l=t.get(s.target);return(i==null?void 0:i.width)&&(i==null?void 0:i.height)&&(l==null?void 0:l.width)&&(l==null?void 0:l.height)&&xC({sourcePos:i.positionAbsolute||{x:0,y:0},targetPos:l.positionAbsolute||{x:0,y:0},sourceWidth:i.width,sourceHeight:i.height,targetWidth:l.width,targetHeight:l.height,width:o.width,height:o.height,transform:o.transform})}):o.edges,[e,t]));return wC(r,t,n)}const bC=({color:e="none",strokeWidth:t=1})=>I.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),NC=({color:e="none",strokeWidth:t=1})=>I.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"}),Rh={[Bl.Arrow]:bC,[Bl.ArrowClosed]:NC};function EC(e){const t=Re();return b.useMemo(()=>{var o,s;return Object.prototype.hasOwnProperty.call(Rh,e)?Rh[e]:((s=(o=t.getState()).onError)==null||s.call(o,"009",pn.error009(e)),null)},[e])}const kC=({id:e,type:t,color:n,width:r=12.5,height:o=12.5,markerUnits:s="strokeWidth",strokeWidth:i,orient:l="auto-start-reverse"})=>{const a=EC(t);return a?I.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${o}`,viewBox:"-10 -10 20 20",markerUnits:s,orient:l,refX:"0",refY:"0"},I.createElement(a,{color:n,strokeWidth:i})):null},_C=({defaultColor:e,rfId:t})=>n=>{const r=[];return n.edges.reduce((o,s)=>([s.markerStart,s.markerEnd].forEach(i=>{if(i&&typeof i=="object"){const l=Iu(i,t);r.includes(l)||(o.push({id:l,color:i.color||e,...i}),r.push(l))}}),o),[]).sort((o,s)=>o.id.localeCompare(s.id))},ex=({defaultColor:e,rfId:t})=>{const n=ue(b.useCallback(_C({defaultColor:e,rfId:t}),[e,t]),(r,o)=>!(r.length!==o.length||r.some((s,i)=>s.id!==o[i].id)));return I.createElement("defs",null,n.map(r=>I.createElement(kC,{id:r.id,key:r.id,type:r.type,color:r.color,width:r.width,height:r.height,markerUnits:r.markerUnits,strokeWidth:r.strokeWidth,orient:r.orient})))};ex.displayName="MarkerDefinitions";var CC=b.memo(ex);const jC=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),tx=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:o,noPanClassName:s,onEdgeContextMenu:i,onEdgeMouseEnter:l,onEdgeMouseMove:a,onEdgeMouseLeave:c,onEdgeClick:u,onEdgeDoubleClick:f,onReconnect:m,onReconnectStart:g,onReconnectEnd:x,reconnectRadius:y,children:v,disableKeyboardA11y:h})=>{const{edgesFocusable:p,edgesUpdatable:w,elementsSelectable:S,width:N,height:E,connectionMode:_,nodeInternals:C,onError:T}=ue(jC,Pe),P=SC(t,C,n);return N?I.createElement(I.Fragment,null,P.map(({level:D,edges:U,isMaxLevel:B})=>I.createElement("svg",{key:D,style:{zIndex:D},width:N,height:E,className:"react-flow__edges react-flow__container"},B&&I.createElement(CC,{defaultColor:e,rfId:r}),I.createElement("g",null,U.map(k=>{const[A,R,F]=jh(C.get(k.source)),[M,j,O]=jh(C.get(k.target));if(!F||!O)return null;let $=k.type||"default";o[$]||(T==null||T("011",pn.error011($)),$="default");const L=o[$]||o.default,V=_===Sr.Strict?j.target:(j.target??[]).concat(j.source??[]),W=Ch(R.source,k.sourceHandle),Y=Ch(V,k.targetHandle),K=(W==null?void 0:W.position)||G.Bottom,J=(Y==null?void 0:Y.position)||G.Top,ie=!!(k.focusable||p&&typeof k.focusable>"u"),se=k.reconnectable||k.updatable,oe=typeof m<"u"&&(se||w&&typeof se>"u");if(!W||!Y)return T==null||T("008",pn.error008(W,k)),null;const{sourceX:Me,sourceY:_e,targetX:qe,targetY:De}=yC(A,W,K,M,Y,J);return I.createElement(L,{key:k.id,id:k.id,className:Le([k.className,s]),type:$,data:k.data,selected:!!k.selected,animated:!!k.animated,hidden:!!k.hidden,label:k.label,labelStyle:k.labelStyle,labelShowBg:k.labelShowBg,labelBgStyle:k.labelBgStyle,labelBgPadding:k.labelBgPadding,labelBgBorderRadius:k.labelBgBorderRadius,style:k.style,source:k.source,target:k.target,sourceHandleId:k.sourceHandle,targetHandleId:k.targetHandle,markerEnd:k.markerEnd,markerStart:k.markerStart,sourceX:Me,sourceY:_e,targetX:qe,targetY:De,sourcePosition:K,targetPosition:J,elementsSelectable:S,onContextMenu:i,onMouseEnter:l,onMouseMove:a,onMouseLeave:c,onClick:u,onEdgeDoubleClick:f,onReconnect:m,onReconnectStart:g,onReconnectEnd:x,reconnectRadius:y,rfId:r,ariaLabel:k.ariaLabel,isFocusable:ie,isReconnectable:oe,pathOptions:"pathOptions"in k?k.pathOptions:void 0,interactionWidth:k.interactionWidth,disableKeyboardA11y:h})})))),v):null};tx.displayName="EdgeRenderer";var RC=b.memo(tx);const AC=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function TC({children:e}){const t=ue(AC);return I.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}function PC(e){const t=uf(),n=b.useRef(!1);b.useEffect(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}const MC={[G.Left]:G.Right,[G.Right]:G.Left,[G.Top]:G.Bottom,[G.Bottom]:G.Top},nx=({nodeId:e,handleType:t,style:n,type:r=_n.Bezier,CustomComponent:o,connectionStatus:s})=>{var E,_,C;const{fromNode:i,handleId:l,toX:a,toY:c,connectionMode:u}=ue(b.useCallback(T=>({fromNode:T.nodeInternals.get(e),handleId:T.connectionHandleId,toX:(T.connectionPosition.x-T.transform[0])/T.transform[2],toY:(T.connectionPosition.y-T.transform[1])/T.transform[2],connectionMode:T.connectionMode}),[e]),Pe),f=(E=i==null?void 0:i[ve])==null?void 0:E.handleBounds;let m=f==null?void 0:f[t];if(u===Sr.Loose&&(m=m||(f==null?void 0:f[t==="source"?"target":"source"])),!i||!m)return null;const g=l?m.find(T=>T.id===l):m[0],x=g?g.x+g.width/2:(i.width??0)/2,y=g?g.y+g.height/2:i.height??0,v=(((_=i.positionAbsolute)==null?void 0:_.x)??0)+x,h=(((C=i.positionAbsolute)==null?void 0:C.y)??0)+y,p=g==null?void 0:g.position,w=p?MC[p]:null;if(!p||!w)return null;if(o)return I.createElement(o,{connectionLineType:r,connectionLineStyle:n,fromNode:i,fromHandle:g,fromX:v,fromY:h,toX:a,toY:c,fromPosition:p,toPosition:w,connectionStatus:s});let S="";const N={sourceX:v,sourceY:h,sourcePosition:p,targetX:a,targetY:c,targetPosition:w};return r===_n.Bezier?[S]=Ey(N):r===_n.Step?[S]=Ou({...N,borderRadius:0}):r===_n.SmoothStep?[S]=Ou(N):r===_n.SimpleBezier?[S]=Ny(N):S=`M${v},${h} ${a},${c}`,I.createElement("path",{d:S,fill:"none",className:"react-flow__connection-path",style:n})};nx.displayName="ConnectionLine";const $C=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function OC({containerStyle:e,style:t,type:n,component:r}){const{nodeId:o,handleType:s,nodesConnectable:i,width:l,height:a,connectionStatus:c}=ue($C,Pe);return!(o&&s&&l&&i)?null:I.createElement("svg",{style:e,width:l,height:a,className:"react-flow__edges react-flow__connectionline react-flow__container"},I.createElement("g",{className:Le(["react-flow__connection",c])},I.createElement(nx,{nodeId:o,handleType:s,style:t,type:n,CustomComponent:r,connectionStatus:c})))}function Ah(e,t){return b.useRef(null),Re(),b.useMemo(()=>t(e),[e])}const rx=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:r,onMoveEnd:o,onInit:s,onNodeClick:i,onEdgeClick:l,onNodeDoubleClick:a,onEdgeDoubleClick:c,onNodeMouseEnter:u,onNodeMouseMove:f,onNodeMouseLeave:m,onNodeContextMenu:g,onSelectionContextMenu:x,onSelectionStart:y,onSelectionEnd:v,connectionLineType:h,connectionLineStyle:p,connectionLineComponent:w,connectionLineContainerStyle:S,selectionKeyCode:N,selectionOnDrag:E,selectionMode:_,multiSelectionKeyCode:C,panActivationKeyCode:T,zoomActivationKeyCode:P,deleteKeyCode:D,onlyRenderVisibleElements:U,elementsSelectable:B,selectNodesOnDrag:k,defaultViewport:A,translateExtent:R,minZoom:F,maxZoom:M,preventScrolling:j,defaultMarkerColor:O,zoomOnScroll:$,zoomOnPinch:L,panOnScroll:V,panOnScrollSpeed:W,panOnScrollMode:Y,zoomOnDoubleClick:K,panOnDrag:J,onPaneClick:ie,onPaneMouseEnter:se,onPaneMouseMove:oe,onPaneMouseLeave:Me,onPaneScroll:_e,onPaneContextMenu:qe,onEdgeContextMenu:De,onEdgeMouseEnter:we,onEdgeMouseMove:lt,onEdgeMouseLeave:ae,onReconnect:X,onReconnectStart:Ye,onReconnectEnd:Yt,reconnectRadius:To,noDragClassName:Cr,noWheelClassName:jr,noPanClassName:Gt,elevateEdgesOnSelect:Rr,disableKeyboardA11y:Gn,nodeOrigin:Xn,nodeExtent:Ar,rfId:Tr})=>{const Po=Ah(e,cC),Fe=Ah(t,gC);return PC(s),I.createElement(lC,{onPaneClick:ie,onPaneMouseEnter:se,onPaneMouseMove:oe,onPaneMouseLeave:Me,onPaneContextMenu:qe,onPaneScroll:_e,deleteKeyCode:D,selectionKeyCode:N,selectionOnDrag:E,selectionMode:_,onSelectionStart:y,onSelectionEnd:v,multiSelectionKeyCode:C,panActivationKeyCode:T,zoomActivationKeyCode:P,elementsSelectable:B,onMove:n,onMoveStart:r,onMoveEnd:o,zoomOnScroll:$,zoomOnPinch:L,zoomOnDoubleClick:K,panOnScroll:V,panOnScrollSpeed:W,panOnScrollMode:Y,panOnDrag:J,defaultViewport:A,translateExtent:R,minZoom:F,maxZoom:M,onSelectionContextMenu:x,preventScrolling:j,noDragClassName:Cr,noWheelClassName:jr,noPanClassName:Gt,disableKeyboardA11y:Gn},I.createElement(TC,null,I.createElement(RC,{edgeTypes:Fe,onEdgeClick:l,onEdgeDoubleClick:c,onlyRenderVisibleElements:U,onEdgeContextMenu:De,onEdgeMouseEnter:we,onEdgeMouseMove:lt,onEdgeMouseLeave:ae,onReconnect:X,onReconnectStart:Ye,onReconnectEnd:Yt,reconnectRadius:To,defaultMarkerColor:O,noPanClassName:Gt,elevateEdgesOnSelect:!!Rr,disableKeyboardA11y:Gn,rfId:Tr},I.createElement(OC,{style:p,type:h,component:w,containerStyle:S})),I.createElement("div",{className:"react-flow__edgelabel-renderer"}),I.createElement(fC,{nodeTypes:Po,onNodeClick:i,onNodeDoubleClick:a,onNodeMouseEnter:u,onNodeMouseMove:f,onNodeMouseLeave:m,onNodeContextMenu:g,selectNodesOnDrag:k,onlyRenderVisibleElements:U,noPanClassName:Gt,noDragClassName:Cr,disableKeyboardA11y:Gn,nodeOrigin:Xn,nodeExtent:Ar,rfId:Tr})))};rx.displayName="GraphView";var IC=b.memo(rx);const Fu=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],gn={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Fu,nodeExtent:Fu,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:Sr.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:d_,isValidConnection:void 0},zC=()=>_2((e,t)=>({...gn,setNodes:n=>{const{nodeInternals:r,nodeOrigin:o,elevateNodesOnSelect:s}=t();e({nodeInternals:fc(n,r,o,s)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:r={}}=t();e({edges:n.map(o=>({...r,...o}))})},setDefaultNodesAndEdges:(n,r)=>{const o=typeof n<"u",s=typeof r<"u",i=o?fc(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:i,edges:s?r:[],hasDefaultNodes:o,hasDefaultEdges:s})},updateNodeDimensions:n=>{const{onNodesChange:r,nodeInternals:o,fitViewOnInit:s,fitViewOnInitDone:i,fitViewOnInitOptions:l,domNode:a,nodeOrigin:c}=t(),u=a==null?void 0:a.querySelector(".react-flow__viewport");if(!u)return;const f=window.getComputedStyle(u),{m22:m}=new window.DOMMatrixReadOnly(f.transform),g=n.reduce((y,v)=>{const h=o.get(v.id);if(h!=null&&h.hidden)o.set(h.id,{...h,[ve]:{...h[ve],handleBounds:void 0}});else if(h){const p=tf(v.nodeElement);!!(p.width&&p.height&&(h.width!==p.width||h.height!==p.height||v.forceUpdate))&&(o.set(h.id,{...h,[ve]:{...h[ve],handleBounds:{source:Nh(".source",v.nodeElement,m,c),target:Nh(".target",v.nodeElement,m,c)}},...p}),y.push({id:h.id,type:"dimensions",dimensions:p}))}return y},[]);Hy(o,c);const x=i||s&&!i&&Vy(t,{initial:!0,...l});e({nodeInternals:new Map(o),fitViewOnInitDone:x}),(g==null?void 0:g.length)>0&&(r==null||r(g))},updateNodePositions:(n,r=!0,o=!1)=>{const{triggerNodeChanges:s}=t(),i=n.map(l=>{const a={id:l.id,type:"position",dragging:o};return r&&(a.positionAbsolute=l.positionAbsolute,a.position=l.position),a});s(i)},triggerNodeChanges:n=>{const{onNodesChange:r,nodeInternals:o,hasDefaultNodes:s,nodeOrigin:i,getNodes:l,elevateNodesOnSelect:a}=t();if(n!=null&&n.length){if(s){const c=qy(n,l()),u=fc(c,o,i,a);e({nodeInternals:u})}r==null||r(n)}},addSelectedNodes:n=>{const{multiSelectionActive:r,edges:o,getNodes:s}=t();let i,l=null;r?i=n.map(a=>bn(a,!0)):(i=Zr(s(),n),l=Zr(o,[])),$i({changedNodes:i,changedEdges:l,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:r,edges:o,getNodes:s}=t();let i,l=null;r?i=n.map(a=>bn(a,!0)):(i=Zr(o,n),l=Zr(s(),[])),$i({changedNodes:l,changedEdges:i,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:r}={})=>{const{edges:o,getNodes:s}=t(),i=n||s(),l=r||o,a=i.map(u=>(u.selected=!1,bn(u.id,!1))),c=l.map(u=>bn(u.id,!1));$i({changedNodes:a,changedEdges:c,get:t,set:e})},setMinZoom:n=>{const{d3Zoom:r,maxZoom:o}=t();r==null||r.scaleExtent([n,o]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:r,minZoom:o}=t();r==null||r.scaleExtent([o,n]),e({maxZoom:n})},setTranslateExtent:n=>{var r;(r=t().d3Zoom)==null||r.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:r}=t(),s=r().filter(l=>l.selected).map(l=>bn(l.id,!1)),i=n.filter(l=>l.selected).map(l=>bn(l.id,!1));$i({changedNodes:s,changedEdges:i,get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:r}=t();r.forEach(o=>{o.positionAbsolute=nf(o.position,n)}),e({nodeExtent:n,nodeInternals:new Map(r)})},panBy:n=>{const{transform:r,width:o,height:s,d3Zoom:i,d3Selection:l,translateExtent:a}=t();if(!i||!l||!n.x&&!n.y)return!1;const c=sn.translate(r[0]+n.x,r[1]+n.y).scale(r[2]),u=[[0,0],[o,s]],f=i==null?void 0:i.constrain()(c,u,a);return i.transform(l,f),r[0]!==f.x||r[1]!==f.y||r[2]!==f.k},cancelConnection:()=>e({connectionNodeId:gn.connectionNodeId,connectionHandleId:gn.connectionHandleId,connectionHandleType:gn.connectionHandleType,connectionStatus:gn.connectionStatus,connectionStartHandle:gn.connectionStartHandle,connectionEndHandle:gn.connectionEndHandle}),reset:()=>e({...gn})}),Object.is),ff=({children:e})=>{const t=b.useRef(null);return t.current||(t.current=zC()),I.createElement(o_,{value:t.current},e)};ff.displayName="ReactFlowProvider";const ox=({children:e})=>b.useContext(ya)?I.createElement(I.Fragment,null,e):I.createElement(ff,null,e);ox.displayName="ReactFlowWrapper";const LC={input:Iy,default:Lu,output:Ly,group:cf},DC={default:Hl,straight:sf,step:of,smoothstep:xa,simplebezier:rf},FC=[0,0],UC=[15,15],BC={x:0,y:0,zoom:1},HC={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},sx=b.forwardRef(({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,className:o,nodeTypes:s=LC,edgeTypes:i=DC,onNodeClick:l,onEdgeClick:a,onInit:c,onMove:u,onMoveStart:f,onMoveEnd:m,onConnect:g,onConnectStart:x,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:h,onNodeMouseEnter:p,onNodeMouseMove:w,onNodeMouseLeave:S,onNodeContextMenu:N,onNodeDoubleClick:E,onNodeDragStart:_,onNodeDrag:C,onNodeDragStop:T,onNodesDelete:P,onEdgesDelete:D,onSelectionChange:U,onSelectionDragStart:B,onSelectionDrag:k,onSelectionDragStop:A,onSelectionContextMenu:R,onSelectionStart:F,onSelectionEnd:M,connectionMode:j=Sr.Strict,connectionLineType:O=_n.Bezier,connectionLineStyle:$,connectionLineComponent:L,connectionLineContainerStyle:V,deleteKeyCode:W="Backspace",selectionKeyCode:Y="Shift",selectionOnDrag:K=!1,selectionMode:J=Bs.Full,panActivationKeyCode:ie="Space",multiSelectionKeyCode:se=Ul()?"Meta":"Control",zoomActivationKeyCode:oe=Ul()?"Meta":"Control",snapToGrid:Me=!1,snapGrid:_e=UC,onlyRenderVisibleElements:qe=!1,selectNodesOnDrag:De=!0,nodesDraggable:we,nodesConnectable:lt,nodesFocusable:ae,nodeOrigin:X=FC,edgesFocusable:Ye,edgesUpdatable:Yt,elementsSelectable:To,defaultViewport:Cr=BC,minZoom:jr=.5,maxZoom:Gt=2,translateExtent:Rr=Fu,preventScrolling:Gn=!0,nodeExtent:Xn,defaultMarkerColor:Ar="#b1b1b7",zoomOnScroll:Tr=!0,zoomOnPinch:Po=!0,panOnScroll:Fe=!1,panOnScrollSpeed:Et=.5,panOnScrollMode:Kn=ar.Free,zoomOnDoubleClick:Qn=!0,panOnDrag:Zn=!0,onPaneClick:Xt,onPaneMouseEnter:zt,onPaneMouseMove:Mo,onPaneMouseLeave:Aa,onPaneScroll:$o,onPaneContextMenu:Ta,children:vf,onEdgeContextMenu:Jn,onEdgeDoubleClick:tv,onEdgeMouseEnter:nv,onEdgeMouseMove:rv,onEdgeMouseLeave:ov,onEdgeUpdate:sv,onEdgeUpdateStart:iv,onEdgeUpdateEnd:lv,onReconnect:av,onReconnectStart:cv,onReconnectEnd:uv,reconnectRadius:dv=10,edgeUpdaterRadius:fv=10,onNodesChange:pv,onEdgesChange:hv,noDragClassName:mv="nodrag",noWheelClassName:gv="nowheel",noPanClassName:wf="nopan",fitView:yv=!1,fitViewOptions:xv,connectOnClick:vv=!0,attributionPosition:wv,proOptions:Sv,defaultEdgeOptions:bv,elevateNodesOnSelect:Nv=!0,elevateEdgesOnSelect:Ev=!1,disableKeyboardA11y:Sf=!1,autoPanOnConnect:kv=!0,autoPanOnNodeDrag:_v=!0,connectionRadius:Cv=20,isValidConnection:jv,onError:Rv,style:Av,id:bf,nodeDragThreshold:Tv,...Pv},Mv)=>{const Pa=bf||"1";return I.createElement("div",{...Pv,style:{...Av,...HC},ref:Mv,className:Le(["react-flow",o]),"data-testid":"rf__wrapper",id:bf},I.createElement(ox,null,I.createElement(IC,{onInit:c,onMove:u,onMoveStart:f,onMoveEnd:m,onNodeClick:l,onEdgeClick:a,onNodeMouseEnter:p,onNodeMouseMove:w,onNodeMouseLeave:S,onNodeContextMenu:N,onNodeDoubleClick:E,nodeTypes:s,edgeTypes:i,connectionLineType:O,connectionLineStyle:$,connectionLineComponent:L,connectionLineContainerStyle:V,selectionKeyCode:Y,selectionOnDrag:K,selectionMode:J,deleteKeyCode:W,multiSelectionKeyCode:se,panActivationKeyCode:ie,zoomActivationKeyCode:oe,onlyRenderVisibleElements:qe,selectNodesOnDrag:De,defaultViewport:Cr,translateExtent:Rr,minZoom:jr,maxZoom:Gt,preventScrolling:Gn,zoomOnScroll:Tr,zoomOnPinch:Po,zoomOnDoubleClick:Qn,panOnScroll:Fe,panOnScrollSpeed:Et,panOnScrollMode:Kn,panOnDrag:Zn,onPaneClick:Xt,onPaneMouseEnter:zt,onPaneMouseMove:Mo,onPaneMouseLeave:Aa,onPaneScroll:$o,onPaneContextMenu:Ta,onSelectionContextMenu:R,onSelectionStart:F,onSelectionEnd:M,onEdgeContextMenu:Jn,onEdgeDoubleClick:tv,onEdgeMouseEnter:nv,onEdgeMouseMove:rv,onEdgeMouseLeave:ov,onReconnect:av??sv,onReconnectStart:cv??iv,onReconnectEnd:uv??lv,reconnectRadius:dv??fv,defaultMarkerColor:Ar,noDragClassName:mv,noWheelClassName:gv,noPanClassName:wf,elevateEdgesOnSelect:Ev,rfId:Pa,disableKeyboardA11y:Sf,nodeOrigin:X,nodeExtent:Xn}),I.createElement(M_,{nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:g,onConnectStart:x,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:h,nodesDraggable:we,nodesConnectable:lt,nodesFocusable:ae,edgesFocusable:Ye,edgesUpdatable:Yt,elementsSelectable:To,elevateNodesOnSelect:Nv,minZoom:jr,maxZoom:Gt,nodeExtent:Xn,onNodesChange:pv,onEdgesChange:hv,snapToGrid:Me,snapGrid:_e,connectionMode:j,translateExtent:Rr,connectOnClick:vv,defaultEdgeOptions:bv,fitView:yv,fitViewOptions:xv,onNodesDelete:P,onEdgesDelete:D,onNodeDragStart:_,onNodeDrag:C,onNodeDragStop:T,onSelectionDrag:k,onSelectionDragStart:B,onSelectionDragStop:A,noPanClassName:wf,nodeOrigin:X,rfId:Pa,autoPanOnConnect:kv,autoPanOnNodeDrag:_v,onError:Rv,connectionRadius:Cv,isValidConnection:jv,nodeDragThreshold:Tv}),I.createElement(T_,{onSelectionChange:U}),vf,I.createElement(i_,{proOptions:Sv,position:wv}),I.createElement(L_,{rfId:Pa,disableKeyboardA11y:Sf})))});sx.displayName="ReactFlow";function ix(e){return t=>{const[n,r]=b.useState(t),o=b.useCallback(s=>r(i=>e(s,i)),[]);return[n,r,o]}}const VC=ix(qy),WC=ix(Z_),lx=({id:e,x:t,y:n,width:r,height:o,style:s,color:i,strokeColor:l,strokeWidth:a,className:c,borderRadius:u,shapeRendering:f,onClick:m,selected:g})=>{const{background:x,backgroundColor:y}=s||{},v=i||x||y;return I.createElement("rect",{className:Le(["react-flow__minimap-node",{selected:g},c]),x:t,y:n,rx:u,ry:u,width:r,height:o,fill:v,stroke:l,strokeWidth:a,shapeRendering:f,onClick:m?h=>m(h,e):void 0})};lx.displayName="MiniMapNode";var qC=b.memo(lx);const YC=e=>e.nodeOrigin,GC=e=>e.getNodes().filter(t=>!t.hidden&&t.width&&t.height),gc=e=>e instanceof Function?e:()=>e;function XC({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:r=5,nodeStrokeWidth:o=2,nodeComponent:s=qC,onClick:i}){const l=ue(GC,Pe),a=ue(YC),c=gc(t),u=gc(e),f=gc(n),m=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision";return I.createElement(I.Fragment,null,l.map(g=>{const{x,y}=pr(g,a).positionAbsolute;return I.createElement(s,{key:g.id,x,y,width:g.width,height:g.height,style:g.style,selected:g.selected,className:f(g),color:c(g),borderRadius:r,strokeColor:u(g),strokeWidth:o,shapeRendering:m,onClick:i,id:g.id})}))}var KC=b.memo(XC);const QC=200,ZC=150,JC=e=>{const t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?c_(va(t,e.nodeOrigin),n):n,rfId:e.rfId}},ej="react-flow__minimap-desc";function ax({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:r="#e2e2e2",nodeClassName:o="",nodeBorderRadius:s=5,nodeStrokeWidth:i=2,nodeComponent:l,maskColor:a="rgb(240, 240, 240, 0.6)",maskStrokeColor:c="none",maskStrokeWidth:u=1,position:f="bottom-right",onClick:m,onNodeClick:g,pannable:x=!1,zoomable:y=!1,ariaLabel:v="React Flow mini map",inversePan:h=!1,zoomStep:p=10,offsetScale:w=5}){const S=Re(),N=b.useRef(null),{boundingRect:E,viewBB:_,rfId:C}=ue(JC,Pe),T=(e==null?void 0:e.width)??QC,P=(e==null?void 0:e.height)??ZC,D=E.width/T,U=E.height/P,B=Math.max(D,U),k=B*T,A=B*P,R=w*B,F=E.x-(k-E.width)/2-R,M=E.y-(A-E.height)/2-R,j=k+R*2,O=A+R*2,$=`${ej}-${C}`,L=b.useRef(0);L.current=B,b.useEffect(()=>{if(N.current){const Y=xt(N.current),K=se=>{const{transform:oe,d3Selection:Me,d3Zoom:_e}=S.getState();if(se.sourceEvent.type!=="wheel"||!Me||!_e)return;const qe=-se.sourceEvent.deltaY*(se.sourceEvent.deltaMode===1?.05:se.sourceEvent.deltaMode?1:.002)*p,De=oe[2]*Math.pow(2,qe);_e.scaleTo(Me,De)},J=se=>{const{transform:oe,d3Selection:Me,d3Zoom:_e,translateExtent:qe,width:De,height:we}=S.getState();if(se.sourceEvent.type!=="mousemove"||!Me||!_e)return;const lt=L.current*Math.max(1,oe[2])*(h?-1:1),ae={x:oe[0]-se.sourceEvent.movementX*lt,y:oe[1]-se.sourceEvent.movementY*lt},X=[[0,0],[De,we]],Ye=sn.translate(ae.x,ae.y).scale(oe[2]),Yt=_e.constrain()(Ye,X,qe);_e.transform(Me,Yt)},ie=py().on("zoom",x?J:null).on("zoom.wheel",y?K:null);return Y.call(ie),()=>{Y.on("zoom",null)}}},[x,y,h,p]);const V=m?Y=>{const K=jt(Y);m(Y,{x:K[0],y:K[1]})}:void 0,W=g?(Y,K)=>{const J=S.getState().nodeInternals.get(K);g(Y,J)}:void 0;return I.createElement(ef,{position:f,style:e,className:Le(["react-flow__minimap",t]),"data-testid":"rf__minimap"},I.createElement("svg",{width:T,height:P,viewBox:`${F} ${M} ${j} ${O}`,role:"img","aria-labelledby":$,ref:N,onClick:V},v&&I.createElement("title",{id:$},v),I.createElement(KC,{onClick:W,nodeColor:r,nodeStrokeColor:n,nodeBorderRadius:s,nodeClassName:o,nodeStrokeWidth:i,nodeComponent:l}),I.createElement("path",{className:"react-flow__minimap-mask",d:`M${F-R},${M-R}h${j+R*2}v${O+R*2}h${-j-R*2}z
        M${_.x},${_.y}h${_.width}v${_.height}h${-_.width}z`,fill:a,fillRule:"evenodd",stroke:c,strokeWidth:u,pointerEvents:"none"})))}ax.displayName="MiniMap";var tj=b.memo(ax);function nj(){return I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},I.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function rj(){return I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},I.createElement("path",{d:"M0 0h32v4.2H0z"}))}function oj(){return I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},I.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function sj(){return I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},I.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function ij(){return I.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},I.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}const rs=({children:e,className:t,...n})=>I.createElement("button",{type:"button",className:Le(["react-flow__controls-button",t]),...n},e);rs.displayName="ControlButton";const lj=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),cx=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:r=!0,fitViewOptions:o,onZoomIn:s,onZoomOut:i,onFitView:l,onInteractiveChange:a,className:c,children:u,position:f="bottom-left"})=>{const m=Re(),[g,x]=b.useState(!1),{isInteractive:y,minZoomReached:v,maxZoomReached:h}=ue(lj,Pe),{zoomIn:p,zoomOut:w,fitView:S}=uf();if(b.useEffect(()=>{x(!0)},[]),!g)return null;const N=()=>{p(),s==null||s()},E=()=>{w(),i==null||i()},_=()=>{S(o),l==null||l()},C=()=>{m.setState({nodesDraggable:!y,nodesConnectable:!y,elementsSelectable:!y}),a==null||a(!y)};return I.createElement(ef,{className:Le(["react-flow__controls",c]),position:f,style:e,"data-testid":"rf__controls"},t&&I.createElement(I.Fragment,null,I.createElement(rs,{onClick:N,className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:h},I.createElement(nj,null)),I.createElement(rs,{onClick:E,className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:v},I.createElement(rj,null))),n&&I.createElement(rs,{className:"react-flow__controls-fitview",onClick:_,title:"fit view","aria-label":"fit view"},I.createElement(oj,null)),r&&I.createElement(rs,{className:"react-flow__controls-interactive",onClick:C,title:"toggle interactivity","aria-label":"toggle interactivity"},y?I.createElement(ij,null):I.createElement(sj,null)),u)};cx.displayName="Controls";var aj=b.memo(cx),Mt;(function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"})(Mt||(Mt={}));function cj({color:e,dimensions:t,lineWidth:n}){return I.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function uj({color:e,radius:t}){return I.createElement("circle",{cx:t,cy:t,r:t,fill:e})}const dj={[Mt.Dots]:"#91919a",[Mt.Lines]:"#eee",[Mt.Cross]:"#e2e2e2"},fj={[Mt.Dots]:1,[Mt.Lines]:1,[Mt.Cross]:6},pj=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function ux({id:e,variant:t=Mt.Dots,gap:n=20,size:r,lineWidth:o=1,offset:s=2,color:i,style:l,className:a}){const c=b.useRef(null),{transform:u,patternId:f}=ue(pj,Pe),m=i||dj[t],g=r||fj[t],x=t===Mt.Dots,y=t===Mt.Cross,v=Array.isArray(n)?n:[n,n],h=[v[0]*u[2]||1,v[1]*u[2]||1],p=g*u[2],w=y?[p,p]:h,S=x?[p/s,p/s]:[w[0]/s,w[1]/s];return I.createElement("svg",{className:Le(["react-flow__background",a]),style:{...l,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:c,"data-testid":"rf__background"},I.createElement("pattern",{id:f+e,x:u[0]%h[0],y:u[1]%h[1],width:h[0],height:h[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${S[0]},-${S[1]})`},x?I.createElement(uj,{color:m,radius:p/s}):I.createElement(cj,{dimensions:w,color:m,lineWidth:o})),I.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${f+e})`}))}ux.displayName="Background";var hj=b.memo(ux);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var mj={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gj=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ne=(e,t)=>{const n=b.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:l="",children:a,...c},u)=>b.createElement("svg",{ref:u,...mj,width:o,height:o,stroke:r,strokeWidth:i?Number(s)*24/Number(o):s,className:["lucide",`lucide-${gj(e)}`,l].join(" "),...c},[...t.map(([f,m])=>b.createElement(f,m)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yj=ne("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wa=ne("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Th=ne("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xj=ne("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vs=ne("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vj=ne("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uu=ne("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wj=ne("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sj=ne("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bu=ne("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=ne("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=ne("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=ne("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hu=ne("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bj=ne("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nj=ne("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ej=ne("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kj=ne("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _j=ne("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yc=ne("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cj=ne("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jj=ne("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=ne("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=ne("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rj=ne("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Aj=ne("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=ne("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tj=ne("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const px=ne("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pf=ne("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=ne("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=ne("Workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pj=ne("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rl=ne("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function mx(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=mx(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Mj(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=mx(e))&&(r&&(r+=" "),r+=t);return r}const hf="-",$j=e=>{const t=Ij(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const l=i.split(hf);return l[0]===""&&l.length!==1&&l.shift(),gx(l,t)||Oj(i)},getConflictingClassGroupIds:(i,l)=>{const a=n[i]||[];return l&&r[i]?[...a,...r[i]]:a}}},gx=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?gx(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(hf);return(i=t.validators.find(({validator:l})=>l(s)))==null?void 0:i.classGroupId},Mh=/^\[(.+)\]$/,Oj=e=>{if(Mh.test(e)){const t=Mh.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Ij=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Lj(Object.entries(e.classGroups),n).forEach(([s,i])=>{Vu(i,r,s,t)}),r},Vu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:$h(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(zj(o)){Vu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Vu(i,$h(t,s),n,r)})})},$h=(e,t)=>{let n=e;return t.split(hf).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},zj=e=>e.isThemeGetter,Lj=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,l])=>[t+i,l])):s);return[n,o]}):e,Dj=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},yx="!",Fj=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=l=>{const a=[];let c=0,u=0,f;for(let v=0;v<l.length;v++){let h=l[v];if(c===0){if(h===o&&(r||l.slice(v,v+s)===t)){a.push(l.slice(u,v)),u=v+s;continue}if(h==="/"){f=v;continue}}h==="["?c++:h==="]"&&c--}const m=a.length===0?l:l.substring(u),g=m.startsWith(yx),x=g?m.substring(1):m,y=f&&f>u?f-u:void 0;return{modifiers:a,hasImportantModifier:g,baseClassName:x,maybePostfixModifierPosition:y}};return n?l=>n({className:l,parseClassName:i}):i},Uj=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Bj=e=>({cache:Dj(e.cacheSize),parseClassName:Fj(e),...$j(e)}),Hj=/\s+/,Vj=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(Hj);let l="";for(let a=i.length-1;a>=0;a-=1){const c=i[a],{modifiers:u,hasImportantModifier:f,baseClassName:m,maybePostfixModifierPosition:g}=n(c);let x=!!g,y=r(x?m.substring(0,g):m);if(!y){if(!x){l=c+(l.length>0?" "+l:l);continue}if(y=r(m),!y){l=c+(l.length>0?" "+l:l);continue}x=!1}const v=Uj(u).join(":"),h=f?v+yx:v,p=h+y;if(s.includes(p))continue;s.push(p);const w=o(y,x);for(let S=0;S<w.length;++S){const N=w[S];s.push(h+N)}l=c+(l.length>0?" "+l:l)}return l};function Wj(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=xx(t))&&(r&&(r+=" "),r+=n);return r}const xx=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=xx(e[r]))&&(n&&(n+=" "),n+=t);return n};function qj(e,...t){let n,r,o,s=i;function i(a){const c=t.reduce((u,f)=>f(u),e());return n=Bj(c),r=n.cache.get,o=n.cache.set,s=l,l(a)}function l(a){const c=r(a);if(c)return c;const u=Vj(a,n);return o(a,u),u}return function(){return s(Wj.apply(null,arguments))}}const de=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},vx=/^\[(?:([a-z-]+):)?(.+)\]$/i,Yj=/^\d+\/\d+$/,Gj=new Set(["px","full","screen"]),Xj=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Kj=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Qj=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Zj=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Jj=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Zt=e=>uo(e)||Gj.has(e)||Yj.test(e),yn=e=>jo(e,"length",l3),uo=e=>!!e&&!Number.isNaN(Number(e)),xc=e=>jo(e,"number",uo),Go=e=>!!e&&Number.isInteger(Number(e)),e3=e=>e.endsWith("%")&&uo(e.slice(0,-1)),Z=e=>vx.test(e),xn=e=>Xj.test(e),t3=new Set(["length","size","percentage"]),n3=e=>jo(e,t3,wx),r3=e=>jo(e,"position",wx),o3=new Set(["image","url"]),s3=e=>jo(e,o3,c3),i3=e=>jo(e,"",a3),Xo=()=>!0,jo=(e,t,n)=>{const r=vx.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},l3=e=>Kj.test(e)&&!Qj.test(e),wx=()=>!1,a3=e=>Zj.test(e),c3=e=>Jj.test(e),u3=()=>{const e=de("colors"),t=de("spacing"),n=de("blur"),r=de("brightness"),o=de("borderColor"),s=de("borderRadius"),i=de("borderSpacing"),l=de("borderWidth"),a=de("contrast"),c=de("grayscale"),u=de("hueRotate"),f=de("invert"),m=de("gap"),g=de("gradientColorStops"),x=de("gradientColorStopPositions"),y=de("inset"),v=de("margin"),h=de("opacity"),p=de("padding"),w=de("saturate"),S=de("scale"),N=de("sepia"),E=de("skew"),_=de("space"),C=de("translate"),T=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],D=()=>["auto",Z,t],U=()=>[Z,t],B=()=>["",Zt,yn],k=()=>["auto",uo,Z],A=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],F=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],M=()=>["start","end","center","between","around","evenly","stretch"],j=()=>["","0",Z],O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],$=()=>[uo,Z];return{cacheSize:500,separator:":",theme:{colors:[Xo],spacing:[Zt,yn],blur:["none","",xn,Z],brightness:$(),borderColor:[e],borderRadius:["none","","full",xn,Z],borderSpacing:U(),borderWidth:B(),contrast:$(),grayscale:j(),hueRotate:$(),invert:j(),gap:U(),gradientColorStops:[e],gradientColorStopPositions:[e3,yn],inset:D(),margin:D(),opacity:$(),padding:U(),saturate:$(),scale:$(),sepia:j(),skew:$(),space:U(),translate:U()},classGroups:{aspect:[{aspect:["auto","square","video",Z]}],container:["container"],columns:[{columns:[xn]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...A(),Z]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Go,Z]}],basis:[{basis:D()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Z]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",Go,Z]}],"grid-cols":[{"grid-cols":[Xo]}],"col-start-end":[{col:["auto",{span:["full",Go,Z]},Z]}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":[Xo]}],"row-start-end":[{row:["auto",{span:[Go,Z]},Z]}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Z]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...M()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...M(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...M(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Z,t]}],"min-w":[{"min-w":[Z,t,"min","max","fit"]}],"max-w":[{"max-w":[Z,t,"none","full","min","max","fit","prose",{screen:[xn]},xn]}],h:[{h:[Z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",xn,yn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",xc]}],"font-family":[{font:[Xo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Z]}],"line-clamp":[{"line-clamp":["none",uo,xc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Zt,Z]}],"list-image":[{"list-image":["none",Z]}],"list-style-type":[{list:["none","disc","decimal",Z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Zt,yn]}],"underline-offset":[{"underline-offset":["auto",Zt,Z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...A(),r3]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",n3]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},s3]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:R()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[Zt,Z]}],"outline-w":[{outline:[Zt,yn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Zt,yn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",xn,i3]}],"shadow-color":[{shadow:[Xo]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...F(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":F()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",xn,Z]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[f]}],saturate:[{saturate:[w]}],sepia:[{sepia:[N]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Z]}],duration:[{duration:$()}],ease:[{ease:["linear","in","out","in-out",Z]}],delay:[{delay:$()}],animate:[{animate:["none","spin","ping","pulse","bounce",Z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Go,Z]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Zt,yn,xc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},d3=qj(u3);function Ee(...e){return d3(Mj(e))}function f3(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}function Vl(){return Math.random().toString(36).substring(2)+Date.now().toString(36)}function p3(e){if(navigator.clipboard&&window.isSecureContext)return navigator.clipboard.writeText(e);{const t=document.createElement("textarea");t.value=e,t.style.position="absolute",t.style.left="-999999px",document.body.prepend(t),t.select();try{document.execCommand("copy")}catch(n){throw console.error("Failed to copy text: ",n),n}finally{t.remove()}return Promise.resolve()}}function mf(e,t,n="text/plain"){const r=new Blob([e],{type:n}),o=URL.createObjectURL(r),s=document.createElement("a");s.href=o,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o)}const Oh=[{name:"Dashboard",href:"/",icon:Ej},{name:"Workflow Builder",href:"/builder",icon:Lr},{name:"Documentation",href:"/docs",icon:xj},{name:"Settings",href:"/settings",icon:px}];function h3({children:e}){const t=ti(),[n,r]=b.useState(!1);return d.jsxs("div",{className:"min-h-screen bg-secondary-50",children:[d.jsxs("div",{className:Ee("fixed inset-0 z-50 lg:hidden",n?"block":"hidden"),children:[d.jsx("div",{className:"fixed inset-0 bg-black/20",onClick:()=>r(!1)}),d.jsxs("div",{className:"fixed left-0 top-0 h-full w-64 bg-white shadow-xl",children:[d.jsxs("div",{className:"flex h-16 items-center justify-between px-4",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(rl,{className:"h-8 w-8 text-primary-600"}),d.jsx("span",{className:"text-lg font-semibold",children:"Moveworks Assistant"})]}),d.jsx("button",{onClick:()=>r(!1),className:"rounded-md p-2 hover:bg-secondary-100",children:d.jsx(Pj,{className:"h-5 w-5"})})]}),d.jsx("nav",{className:"mt-8 px-4",children:d.jsx("ul",{className:"space-y-2",children:Oh.map(o=>{const s=t.pathname===o.href;return d.jsx("li",{children:d.jsxs(Ml,{to:o.href,onClick:()=>r(!1),className:Ee("flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",s?"bg-primary-100 text-primary-700":"text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900"),children:[d.jsx(o.icon,{className:"h-5 w-5"}),d.jsx("span",{children:o.name})]})},o.name)})})})]})]}),d.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col",children:d.jsxs("div",{className:"flex grow flex-col gap-y-5 overflow-y-auto border-r border-secondary-200 bg-white px-6",children:[d.jsx("div",{className:"flex h-16 shrink-0 items-center",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(rl,{className:"h-8 w-8 text-primary-600"}),d.jsx("span",{className:"text-lg font-semibold",children:"Moveworks Assistant"})]})}),d.jsx("nav",{className:"flex flex-1 flex-col",children:d.jsxs("ul",{role:"list",className:"flex flex-1 flex-col gap-y-7",children:[d.jsx("li",{children:d.jsx("ul",{role:"list",className:"-mx-2 space-y-1",children:Oh.map(o=>{const s=t.pathname===o.href;return d.jsx("li",{children:d.jsxs(Ml,{to:o.href,className:Ee("group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors",s?"bg-primary-50 text-primary-700":"text-secondary-700 hover:text-primary-700 hover:bg-primary-50"),children:[d.jsx(o.icon,{className:"h-6 w-6 shrink-0"}),o.name]})},o.name)})})}),d.jsx("li",{className:"mt-auto",children:d.jsxs("a",{href:"https://github.com/moveworks/compound-action-assistant",target:"_blank",rel:"noopener noreferrer",className:"group -mx-2 flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-secondary-700 hover:bg-secondary-50 hover:text-primary-700",children:[d.jsx(Nj,{className:"h-6 w-6 shrink-0"}),"GitHub"]})})]})})]})}),d.jsxs("div",{className:"lg:pl-64",children:[d.jsxs("div",{className:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-secondary-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:hidden",children:[d.jsxs("button",{type:"button",className:"-m-2.5 p-2.5 text-secondary-700 lg:hidden",onClick:()=>r(!0),children:[d.jsx("span",{className:"sr-only",children:"Open sidebar"}),d.jsx(Cj,{className:"h-6 w-6"})]}),d.jsx("div",{className:"flex flex-1 items-center justify-between",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(rl,{className:"h-6 w-6 text-primary-600"}),d.jsx("span",{className:"font-semibold",children:"Moveworks Assistant"})]})})]}),d.jsx("main",{className:"min-h-screen",children:e})]})]})}const Sx={},{useDebugValue:m3}=I,{useSyncExternalStoreWithSelector:g3}=z0;let Ih=!1;const y3=e=>e;function x3(e,t=y3,n){(Sx?"production":void 0)!=="production"&&n&&!Ih&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Ih=!0);const r=g3(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return m3(r),r}const v3=e=>{(Sx?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?L0(e):e,n=(r,o)=>x3(t,r,o);return Object.assign(n,t),n},w3=e=>v3,ol={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_URL:"http://localhost:3001/api",VITE_APP_NAME:"Moveworks Compound Action Assistant",VITE_APP_VERSION:"1.0.0",VITE_ENABLE_ANALYTICS:"false",VITE_ENABLE_DEBUG:"true"},Wu=new Map,Ii=e=>{const t=Wu.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([n,r])=>[n,r.getState()])):{}},S3=(e,t,n)=>{if(e===void 0)return{type:"untracked",connection:t.connect(n)};const r=Wu.get(n.name);if(r)return{type:"tracked",store:e,...r};const o={connection:t.connect(n),stores:{}};return Wu.set(n.name,o),{type:"tracked",store:e,...o}},b3=(e,t={})=>(n,r,o)=>{const{enabled:s,anonymousActionType:i,store:l,...a}=t;let c;try{c=(s??(ol?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!c)return(ol?"production":void 0)!=="production"&&s&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,o);const{connection:u,...f}=S3(l,c,a);let m=!0;o.setState=(y,v,h)=>{const p=n(y,v);if(!m)return p;const w=h===void 0?{type:i||"anonymous"}:typeof h=="string"?{type:h}:h;return l===void 0?(u==null||u.send(w,r()),p):(u==null||u.send({...w,type:`${l}/${w.type}`},{...Ii(a.name),[l]:o.getState()}),p)};const g=(...y)=>{const v=m;m=!1,n(...y),m=v},x=e(o.setState,r,o);if(f.type==="untracked"?u==null||u.init(x):(f.stores[f.store]=o,u==null||u.init(Object.fromEntries(Object.entries(f.stores).map(([y,v])=>[y,y===f.store?x:v.getState()])))),o.dispatchFromDevtools&&typeof o.dispatch=="function"){let y=!1;const v=o.dispatch;o.dispatch=(...h)=>{(ol?"production":void 0)!=="production"&&h[0].type==="__setState"&&!y&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),y=!0),v(...h)}}return u.subscribe(y=>{var v;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return vc(y.payload,h=>{if(h.type==="__setState"){if(l===void 0){g(h.state);return}Object.keys(h.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const p=h.state[l];if(p==null)return;JSON.stringify(o.getState())!==JSON.stringify(p)&&g(p);return}o.dispatchFromDevtools&&typeof o.dispatch=="function"&&o.dispatch(h)});case"DISPATCH":switch(y.payload.type){case"RESET":return g(x),l===void 0?u==null?void 0:u.init(o.getState()):u==null?void 0:u.init(Ii(a.name));case"COMMIT":if(l===void 0){u==null||u.init(o.getState());return}return u==null?void 0:u.init(Ii(a.name));case"ROLLBACK":return vc(y.state,h=>{if(l===void 0){g(h),u==null||u.init(o.getState());return}g(h[l]),u==null||u.init(Ii(a.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return vc(y.state,h=>{if(l===void 0){g(h);return}JSON.stringify(o.getState())!==JSON.stringify(h[l])&&g(h[l])});case"IMPORT_STATE":{const{nextLiftedState:h}=y.payload,p=(v=h.computedStates.slice(-1)[0])==null?void 0:v.state;if(!p)return;g(l===void 0?p:p[l]),u==null||u.send(null,h);return}case"PAUSE_RECORDING":return m=!m}return}}),x},N3=b3,vc=(e,t)=>{let n;try{n=JSON.parse(e)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&t(n)};function E3(e,t){let n;try{n=e()}catch{return}return{getItem:o=>{var s;const i=a=>a===null?null:JSON.parse(a,void 0),l=(s=n.getItem(o))!=null?s:null;return l instanceof Promise?l.then(i):i(l)},setItem:(o,s)=>n.setItem(o,JSON.stringify(s,void 0)),removeItem:o=>n.removeItem(o)}}const qs=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return qs(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return qs(r)(n)}}}},k3=(e,t)=>(n,r,o)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:v=>v,version:0,merge:(v,h)=>({...h,...v}),...t},i=!1;const l=new Set,a=new Set;let c;try{c=s.getStorage()}catch{}if(!c)return e((...v)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...v)},r,o);const u=qs(s.serialize),f=()=>{const v=s.partialize({...r()});let h;const p=u({state:v,version:s.version}).then(w=>c.setItem(s.name,w)).catch(w=>{h=w});if(h)throw h;return p},m=o.setState;o.setState=(v,h)=>{m(v,h),f()};const g=e((...v)=>{n(...v),f()},r,o);let x;const y=()=>{var v;if(!c)return;i=!1,l.forEach(p=>p(r()));const h=((v=s.onRehydrateStorage)==null?void 0:v.call(s,r()))||void 0;return qs(c.getItem.bind(c))(s.name).then(p=>{if(p)return s.deserialize(p)}).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==s.version){if(s.migrate)return s.migrate(p.state,p.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return p.state}).then(p=>{var w;return x=s.merge(p,(w=r())!=null?w:g),n(x,!0),f()}).then(()=>{h==null||h(x,void 0),i=!0,a.forEach(p=>p(x))}).catch(p=>{h==null||h(void 0,p)})};return o.persist={setOptions:v=>{s={...s,...v},v.getStorage&&(c=v.getStorage())},clearStorage:()=>{c==null||c.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>y(),hasHydrated:()=>i,onHydrate:v=>(l.add(v),()=>{l.delete(v)}),onFinishHydration:v=>(a.add(v),()=>{a.delete(v)})},y(),x||g},_3=(e,t)=>(n,r,o)=>{let s={storage:E3(()=>localStorage),partialize:y=>y,version:0,merge:(y,v)=>({...v,...y}),...t},i=!1;const l=new Set,a=new Set;let c=s.storage;if(!c)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...y)},r,o);const u=()=>{const y=s.partialize({...r()});return c.setItem(s.name,{state:y,version:s.version})},f=o.setState;o.setState=(y,v)=>{f(y,v),u()};const m=e((...y)=>{n(...y),u()},r,o);o.getInitialState=()=>m;let g;const x=()=>{var y,v;if(!c)return;i=!1,l.forEach(p=>{var w;return p((w=r())!=null?w:m)});const h=((v=s.onRehydrateStorage)==null?void 0:v.call(s,(y=r())!=null?y:m))||void 0;return qs(c.getItem.bind(c))(s.name).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==s.version){if(s.migrate)return[!0,s.migrate(p.state,p.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,p.state];return[!1,void 0]}).then(p=>{var w;const[S,N]=p;if(g=s.merge(N,(w=r())!=null?w:m),n(g,!0),S)return u()}).then(()=>{h==null||h(g,void 0),g=r(),i=!0,a.forEach(p=>p(g))}).catch(p=>{h==null||h(void 0,p)})};return o.persist={setOptions:y=>{s={...s,...y},y.storage&&(c=y.storage)},clearStorage:()=>{c==null||c.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>x(),hasHydrated:()=>i,onHydrate:y=>(l.add(y),()=>{l.delete(y)}),onFinishHydration:y=>(a.add(y),()=>{a.delete(y)})},s.skipHydration||x(),g||m},C3=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((ol?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),k3(e,t)):_3(e,t),j3=C3;function bx(e,t){return function(){return e.apply(t,arguments)}}const{toString:R3}=Object.prototype,{getPrototypeOf:gf}=Object,{iterator:ba,toStringTag:Nx}=Symbol,Na=(e=>t=>{const n=R3.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),It=e=>(e=e.toLowerCase(),t=>Na(t)===e),Ea=e=>t=>typeof t===e,{isArray:Ro}=Array,Ys=Ea("undefined");function A3(e){return e!==null&&!Ys(e)&&e.constructor!==null&&!Ys(e.constructor)&&st(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ex=It("ArrayBuffer");function T3(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ex(e.buffer),t}const P3=Ea("string"),st=Ea("function"),kx=Ea("number"),ka=e=>e!==null&&typeof e=="object",M3=e=>e===!0||e===!1,sl=e=>{if(Na(e)!=="object")return!1;const t=gf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Nx in e)&&!(ba in e)},$3=It("Date"),O3=It("File"),I3=It("Blob"),z3=It("FileList"),L3=e=>ka(e)&&st(e.pipe),D3=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||st(e.append)&&((t=Na(e))==="formdata"||t==="object"&&st(e.toString)&&e.toString()==="[object FormData]"))},F3=It("URLSearchParams"),[U3,B3,H3,V3]=["ReadableStream","Request","Response","Headers"].map(It),W3=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ii(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),Ro(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let l;for(r=0;r<i;r++)l=s[r],t.call(null,e[l],l,e)}}function _x(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const cr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Cx=e=>!Ys(e)&&e!==cr;function qu(){const{caseless:e}=Cx(this)&&this||{},t={},n=(r,o)=>{const s=e&&_x(t,o)||o;sl(t[s])&&sl(r)?t[s]=qu(t[s],r):sl(r)?t[s]=qu({},r):Ro(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&ii(arguments[r],n);return t}const q3=(e,t,n,{allOwnKeys:r}={})=>(ii(t,(o,s)=>{n&&st(o)?e[s]=bx(o,n):e[s]=o},{allOwnKeys:r}),e),Y3=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),G3=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},X3=(e,t,n,r)=>{let o,s,i;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&gf(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},K3=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Q3=e=>{if(!e)return null;if(Ro(e))return e;let t=e.length;if(!kx(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Z3=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&gf(Uint8Array)),J3=(e,t)=>{const r=(e&&e[ba]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},e4=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},t4=It("HTMLFormElement"),n4=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),zh=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),r4=It("RegExp"),jx=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ii(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},o4=e=>{jx(e,(t,n)=>{if(st(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(st(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},s4=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return Ro(e)?r(e):r(String(e).split(t)),n},i4=()=>{},l4=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function a4(e){return!!(e&&st(e.append)&&e[Nx]==="FormData"&&e[ba])}const c4=e=>{const t=new Array(10),n=(r,o)=>{if(ka(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=Ro(r)?[]:{};return ii(r,(i,l)=>{const a=n(i,o+1);!Ys(a)&&(s[l]=a)}),t[o]=void 0,s}}return r};return n(e,0)},u4=It("AsyncFunction"),d4=e=>e&&(ka(e)||st(e))&&st(e.then)&&st(e.catch),Rx=((e,t)=>e?setImmediate:t?((n,r)=>(cr.addEventListener("message",({source:o,data:s})=>{o===cr&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),cr.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",st(cr.postMessage)),f4=typeof queueMicrotask<"u"?queueMicrotask.bind(cr):typeof process<"u"&&process.nextTick||Rx,p4=e=>e!=null&&st(e[ba]),z={isArray:Ro,isArrayBuffer:Ex,isBuffer:A3,isFormData:D3,isArrayBufferView:T3,isString:P3,isNumber:kx,isBoolean:M3,isObject:ka,isPlainObject:sl,isReadableStream:U3,isRequest:B3,isResponse:H3,isHeaders:V3,isUndefined:Ys,isDate:$3,isFile:O3,isBlob:I3,isRegExp:r4,isFunction:st,isStream:L3,isURLSearchParams:F3,isTypedArray:Z3,isFileList:z3,forEach:ii,merge:qu,extend:q3,trim:W3,stripBOM:Y3,inherits:G3,toFlatObject:X3,kindOf:Na,kindOfTest:It,endsWith:K3,toArray:Q3,forEachEntry:J3,matchAll:e4,isHTMLForm:t4,hasOwnProperty:zh,hasOwnProp:zh,reduceDescriptors:jx,freezeMethods:o4,toObjectSet:s4,toCamelCase:n4,noop:i4,toFiniteNumber:l4,findKey:_x,global:cr,isContextDefined:Cx,isSpecCompliantForm:a4,toJSONObject:c4,isAsyncFn:u4,isThenable:d4,setImmediate:Rx,asap:f4,isIterable:p4};function Q(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}z.inherits(Q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const Ax=Q.prototype,Tx={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Tx[e]={value:e}});Object.defineProperties(Q,Tx);Object.defineProperty(Ax,"isAxiosError",{value:!0});Q.from=(e,t,n,r,o,s)=>{const i=Object.create(Ax);return z.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),Q.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const h4=null;function Yu(e){return z.isPlainObject(e)||z.isArray(e)}function Px(e){return z.endsWith(e,"[]")?e.slice(0,-2):e}function Lh(e,t,n){return e?e.concat(t).map(function(o,s){return o=Px(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function m4(e){return z.isArray(e)&&!e.some(Yu)}const g4=z.toFlatObject(z,{},null,function(t){return/^is[A-Z]/.test(t)});function _a(e,t,n){if(!z.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=z.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,v){return!z.isUndefined(v[y])});const r=n.metaTokens,o=n.visitor||u,s=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&z.isSpecCompliantForm(t);if(!z.isFunction(o))throw new TypeError("visitor must be a function");function c(x){if(x===null)return"";if(z.isDate(x))return x.toISOString();if(!a&&z.isBlob(x))throw new Q("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(x)||z.isTypedArray(x)?a&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function u(x,y,v){let h=x;if(x&&!v&&typeof x=="object"){if(z.endsWith(y,"{}"))y=r?y:y.slice(0,-2),x=JSON.stringify(x);else if(z.isArray(x)&&m4(x)||(z.isFileList(x)||z.endsWith(y,"[]"))&&(h=z.toArray(x)))return y=Px(y),h.forEach(function(w,S){!(z.isUndefined(w)||w===null)&&t.append(i===!0?Lh([y],S,s):i===null?y:y+"[]",c(w))}),!1}return Yu(x)?!0:(t.append(Lh(v,y,s),c(x)),!1)}const f=[],m=Object.assign(g4,{defaultVisitor:u,convertValue:c,isVisitable:Yu});function g(x,y){if(!z.isUndefined(x)){if(f.indexOf(x)!==-1)throw Error("Circular reference detected in "+y.join("."));f.push(x),z.forEach(x,function(h,p){(!(z.isUndefined(h)||h===null)&&o.call(t,h,z.isString(p)?p.trim():p,y,m))===!0&&g(h,y?y.concat(p):[p])}),f.pop()}}if(!z.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Dh(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function yf(e,t){this._pairs=[],e&&_a(e,this,t)}const Mx=yf.prototype;Mx.append=function(t,n){this._pairs.push([t,n])};Mx.toString=function(t){const n=t?function(r){return t.call(this,r,Dh)}:Dh;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function y4(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $x(e,t,n){if(!t)return e;const r=n&&n.encode||y4;z.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=z.isURLSearchParams(t)?t.toString():new yf(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Fh{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){z.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ox={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},x4=typeof URLSearchParams<"u"?URLSearchParams:yf,v4=typeof FormData<"u"?FormData:null,w4=typeof Blob<"u"?Blob:null,S4={isBrowser:!0,classes:{URLSearchParams:x4,FormData:v4,Blob:w4},protocols:["http","https","file","blob","url","data"]},xf=typeof window<"u"&&typeof document<"u",Gu=typeof navigator=="object"&&navigator||void 0,b4=xf&&(!Gu||["ReactNative","NativeScript","NS"].indexOf(Gu.product)<0),N4=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",E4=xf&&window.location.href||"http://localhost",k4=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:xf,hasStandardBrowserEnv:b4,hasStandardBrowserWebWorkerEnv:N4,navigator:Gu,origin:E4},Symbol.toStringTag,{value:"Module"})),Ve={...k4,...S4};function _4(e,t){return _a(e,new Ve.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return Ve.isNode&&z.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function C4(e){return z.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function j4(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function Ix(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=s>=n.length;return i=!i&&z.isArray(o)?o.length:i,a?(z.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!l):((!o[i]||!z.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&z.isArray(o[i])&&(o[i]=j4(o[i])),!l)}if(z.isFormData(e)&&z.isFunction(e.entries)){const n={};return z.forEachEntry(e,(r,o)=>{t(C4(r),o,n,0)}),n}return null}function R4(e,t,n){if(z.isString(e))try{return(t||JSON.parse)(e),z.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const li={transitional:Ox,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=z.isObject(t);if(s&&z.isHTMLForm(t)&&(t=new FormData(t)),z.isFormData(t))return o?JSON.stringify(Ix(t)):t;if(z.isArrayBuffer(t)||z.isBuffer(t)||z.isStream(t)||z.isFile(t)||z.isBlob(t)||z.isReadableStream(t))return t;if(z.isArrayBufferView(t))return t.buffer;if(z.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return _4(t,this.formSerializer).toString();if((l=z.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return _a(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),R4(t)):t}],transformResponse:[function(t){const n=this.transitional||li.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(z.isResponse(t)||z.isReadableStream(t))return t;if(t&&z.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?Q.from(l,Q.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ve.classes.FormData,Blob:Ve.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],e=>{li.headers[e]={}});const A4=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),T4=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&A4[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Uh=Symbol("internals");function Ko(e){return e&&String(e).trim().toLowerCase()}function il(e){return e===!1||e==null?e:z.isArray(e)?e.map(il):String(e)}function P4(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const M4=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function wc(e,t,n,r,o){if(z.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!z.isString(t)){if(z.isString(r))return t.indexOf(r)!==-1;if(z.isRegExp(r))return r.test(t)}}function $4(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function O4(e,t){const n=z.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}let it=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(l,a,c){const u=Ko(a);if(!u)throw new Error("header name must be a non-empty string");const f=z.findKey(o,u);(!f||o[f]===void 0||c===!0||c===void 0&&o[f]!==!1)&&(o[f||a]=il(l))}const i=(l,a)=>z.forEach(l,(c,u)=>s(c,u,a));if(z.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(z.isString(t)&&(t=t.trim())&&!M4(t))i(T4(t),n);else if(z.isObject(t)&&z.isIterable(t)){let l={},a,c;for(const u of t){if(!z.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[c=u[0]]=(a=l[c])?z.isArray(a)?[...a,u[1]]:[a,u[1]]:u[1]}i(l,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=Ko(t),t){const r=z.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return P4(o);if(z.isFunction(n))return n.call(this,o,r);if(z.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ko(t),t){const r=z.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||wc(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=Ko(i),i){const l=z.findKey(r,i);l&&(!n||wc(r,r[l],l,n))&&(delete r[l],o=!0)}}return z.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||wc(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return z.forEach(this,(o,s)=>{const i=z.findKey(r,s);if(i){n[i]=il(o),delete n[s];return}const l=t?$4(s):String(s).trim();l!==s&&delete n[s],n[l]=il(o),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return z.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&z.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Uh]=this[Uh]={accessors:{}}).accessors,o=this.prototype;function s(i){const l=Ko(i);r[l]||(O4(o,i),r[l]=!0)}return z.isArray(t)?t.forEach(s):s(t),this}};it.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(it.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});z.freezeMethods(it);function Sc(e,t){const n=this||li,r=t||n,o=it.from(r.headers);let s=r.data;return z.forEach(e,function(l){s=l.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function zx(e){return!!(e&&e.__CANCEL__)}function Ao(e,t,n){Q.call(this,e??"canceled",Q.ERR_CANCELED,t,n),this.name="CanceledError"}z.inherits(Ao,Q,{__CANCEL__:!0});function Lx(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Q("Request failed with status code "+n.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function I4(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function z4(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(a){const c=Date.now(),u=r[s];i||(i=c),n[o]=a,r[o]=c;let f=s,m=0;for(;f!==o;)m+=n[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const g=u&&c-u;return g?Math.round(m*1e3/g):void 0}}function L4(e,t){let n=0,r=1e3/t,o,s;const i=(c,u=Date.now())=>{n=u,o=null,s&&(clearTimeout(s),s=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),f=u-n;f>=r?i(c,u):(o=c,s||(s=setTimeout(()=>{s=null,i(o)},r-f)))},()=>o&&i(o)]}const Wl=(e,t,n=3)=>{let r=0;const o=z4(50,250);return L4(s=>{const i=s.loaded,l=s.lengthComputable?s.total:void 0,a=i-r,c=o(a),u=i<=l;r=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:c||void 0,estimated:c&&l&&u?(l-i)/c:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Bh=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Hh=e=>(...t)=>z.asap(()=>e(...t)),D4=Ve.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ve.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ve.origin),Ve.navigator&&/(msie|trident)/i.test(Ve.navigator.userAgent)):()=>!0,F4=Ve.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];z.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),z.isString(r)&&i.push("path="+r),z.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function U4(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function B4(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Dx(e,t,n){let r=!U4(t);return e&&(r||n==!1)?B4(e,t):t}const Vh=e=>e instanceof it?{...e}:e;function br(e,t){t=t||{};const n={};function r(c,u,f,m){return z.isPlainObject(c)&&z.isPlainObject(u)?z.merge.call({caseless:m},c,u):z.isPlainObject(u)?z.merge({},u):z.isArray(u)?u.slice():u}function o(c,u,f,m){if(z.isUndefined(u)){if(!z.isUndefined(c))return r(void 0,c,f,m)}else return r(c,u,f,m)}function s(c,u){if(!z.isUndefined(u))return r(void 0,u)}function i(c,u){if(z.isUndefined(u)){if(!z.isUndefined(c))return r(void 0,c)}else return r(void 0,u)}function l(c,u,f){if(f in t)return r(c,u);if(f in e)return r(void 0,c)}const a={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(c,u,f)=>o(Vh(c),Vh(u),f,!0)};return z.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=a[u]||o,m=f(e[u],t[u],u);z.isUndefined(m)&&f!==l||(n[u]=m)}),n}const Fx=e=>{const t=br({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:l}=t;t.headers=i=it.from(i),t.url=$x(Dx(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(z.isFormData(n)){if(Ve.hasStandardBrowserEnv||Ve.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[c,...u]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...u].join("; "))}}if(Ve.hasStandardBrowserEnv&&(r&&z.isFunction(r)&&(r=r(t)),r||r!==!1&&D4(t.url))){const c=o&&s&&F4.read(s);c&&i.set(o,c)}return t},H4=typeof XMLHttpRequest<"u",V4=H4&&function(e){return new Promise(function(n,r){const o=Fx(e);let s=o.data;const i=it.from(o.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:c}=o,u,f,m,g,x;function y(){g&&g(),x&&x(),o.cancelToken&&o.cancelToken.unsubscribe(u),o.signal&&o.signal.removeEventListener("abort",u)}let v=new XMLHttpRequest;v.open(o.method.toUpperCase(),o.url,!0),v.timeout=o.timeout;function h(){if(!v)return;const w=it.from("getAllResponseHeaders"in v&&v.getAllResponseHeaders()),N={data:!l||l==="text"||l==="json"?v.responseText:v.response,status:v.status,statusText:v.statusText,headers:w,config:e,request:v};Lx(function(_){n(_),y()},function(_){r(_),y()},N),v=null}"onloadend"in v?v.onloadend=h:v.onreadystatechange=function(){!v||v.readyState!==4||v.status===0&&!(v.responseURL&&v.responseURL.indexOf("file:")===0)||setTimeout(h)},v.onabort=function(){v&&(r(new Q("Request aborted",Q.ECONNABORTED,e,v)),v=null)},v.onerror=function(){r(new Q("Network Error",Q.ERR_NETWORK,e,v)),v=null},v.ontimeout=function(){let S=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const N=o.transitional||Ox;o.timeoutErrorMessage&&(S=o.timeoutErrorMessage),r(new Q(S,N.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,v)),v=null},s===void 0&&i.setContentType(null),"setRequestHeader"in v&&z.forEach(i.toJSON(),function(S,N){v.setRequestHeader(N,S)}),z.isUndefined(o.withCredentials)||(v.withCredentials=!!o.withCredentials),l&&l!=="json"&&(v.responseType=o.responseType),c&&([m,x]=Wl(c,!0),v.addEventListener("progress",m)),a&&v.upload&&([f,g]=Wl(a),v.upload.addEventListener("progress",f),v.upload.addEventListener("loadend",g)),(o.cancelToken||o.signal)&&(u=w=>{v&&(r(!w||w.type?new Ao(null,e,v):w),v.abort(),v=null)},o.cancelToken&&o.cancelToken.subscribe(u),o.signal&&(o.signal.aborted?u():o.signal.addEventListener("abort",u)));const p=I4(o.url);if(p&&Ve.protocols.indexOf(p)===-1){r(new Q("Unsupported protocol "+p+":",Q.ERR_BAD_REQUEST,e));return}v.send(s||null)})},W4=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(c){if(!o){o=!0,l();const u=c instanceof Error?c:this.reason;r.abort(u instanceof Q?u:new Ao(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,s(new Q(`timeout ${t} of ms exceeded`,Q.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),e=null)};e.forEach(c=>c.addEventListener("abort",s));const{signal:a}=r;return a.unsubscribe=()=>z.asap(l),a}},q4=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Y4=async function*(e,t){for await(const n of G4(e))yield*q4(n,t)},G4=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Wh=(e,t,n,r)=>{const o=Y4(e,t);let s=0,i,l=a=>{i||(i=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:c,value:u}=await o.next();if(c){l(),a.close();return}let f=u.byteLength;if(n){let m=s+=f;n(m)}a.enqueue(new Uint8Array(u))}catch(c){throw l(c),c}},cancel(a){return l(a),o.return()}},{highWaterMark:2})},Ca=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ux=Ca&&typeof ReadableStream=="function",X4=Ca&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Bx=(e,...t)=>{try{return!!e(...t)}catch{return!1}},K4=Ux&&Bx(()=>{let e=!1;const t=new Request(Ve.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),qh=64*1024,Xu=Ux&&Bx(()=>z.isReadableStream(new Response("").body)),ql={stream:Xu&&(e=>e.body)};Ca&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ql[t]&&(ql[t]=z.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new Q(`Response type '${t}' is not supported`,Q.ERR_NOT_SUPPORT,r)})})})(new Response);const Q4=async e=>{if(e==null)return 0;if(z.isBlob(e))return e.size;if(z.isSpecCompliantForm(e))return(await new Request(Ve.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(z.isArrayBufferView(e)||z.isArrayBuffer(e))return e.byteLength;if(z.isURLSearchParams(e)&&(e=e+""),z.isString(e))return(await X4(e)).byteLength},Z4=async(e,t)=>{const n=z.toFiniteNumber(e.getContentLength());return n??Q4(t)},J4=Ca&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:c,headers:u,withCredentials:f="same-origin",fetchOptions:m}=Fx(e);c=c?(c+"").toLowerCase():"text";let g=W4([o,s&&s.toAbortSignal()],i),x;const y=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let v;try{if(a&&K4&&n!=="get"&&n!=="head"&&(v=await Z4(u,r))!==0){let N=new Request(t,{method:"POST",body:r,duplex:"half"}),E;if(z.isFormData(r)&&(E=N.headers.get("content-type"))&&u.setContentType(E),N.body){const[_,C]=Bh(v,Wl(Hh(a)));r=Wh(N.body,qh,_,C)}}z.isString(f)||(f=f?"include":"omit");const h="credentials"in Request.prototype;x=new Request(t,{...m,signal:g,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:h?f:void 0});let p=await fetch(x);const w=Xu&&(c==="stream"||c==="response");if(Xu&&(l||w&&y)){const N={};["status","statusText","headers"].forEach(T=>{N[T]=p[T]});const E=z.toFiniteNumber(p.headers.get("content-length")),[_,C]=l&&Bh(E,Wl(Hh(l),!0))||[];p=new Response(Wh(p.body,qh,_,()=>{C&&C(),y&&y()}),N)}c=c||"text";let S=await ql[z.findKey(ql,c)||"text"](p,e);return!w&&y&&y(),await new Promise((N,E)=>{Lx(N,E,{data:S,headers:it.from(p.headers),status:p.status,statusText:p.statusText,config:e,request:x})})}catch(h){throw y&&y(),h&&h.name==="TypeError"&&/Load failed|fetch/i.test(h.message)?Object.assign(new Q("Network Error",Q.ERR_NETWORK,e,x),{cause:h.cause||h}):Q.from(h,h&&h.code,e,x)}}),Ku={http:h4,xhr:V4,fetch:J4};z.forEach(Ku,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Yh=e=>`- ${e}`,eR=e=>z.isFunction(e)||e===null||e===!1,Hx={getAdapter:e=>{e=z.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!eR(n)&&(r=Ku[(i=String(n)).toLowerCase()],r===void 0))throw new Q(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(Yh).join(`
`):" "+Yh(s[0]):"as no adapter specified";throw new Q("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Ku};function bc(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ao(null,e)}function Gh(e){return bc(e),e.headers=it.from(e.headers),e.data=Sc.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Hx.getAdapter(e.adapter||li.adapter)(e).then(function(r){return bc(e),r.data=Sc.call(e,e.transformResponse,r),r.headers=it.from(r.headers),r},function(r){return zx(r)||(bc(e),r&&r.response&&(r.response.data=Sc.call(e,e.transformResponse,r.response),r.response.headers=it.from(r.response.headers))),Promise.reject(r)})}const Vx="1.9.0",ja={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ja[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Xh={};ja.transitional=function(t,n,r){function o(s,i){return"[Axios v"+Vx+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,l)=>{if(t===!1)throw new Q(o(i," has been removed"+(n?" in "+n:"")),Q.ERR_DEPRECATED);return n&&!Xh[i]&&(Xh[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,l):!0}};ja.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function tR(e,t,n){if(typeof e!="object")throw new Q("options must be an object",Q.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const l=e[s],a=l===void 0||i(l,s,e);if(a!==!0)throw new Q("option "+s+" must be "+a,Q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Q("Unknown option "+s,Q.ERR_BAD_OPTION)}}const ll={assertOptions:tR,validators:ja},Dt=ll.validators;let hr=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Fh,response:new Fh}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=br(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&ll.assertOptions(r,{silentJSONParsing:Dt.transitional(Dt.boolean),forcedJSONParsing:Dt.transitional(Dt.boolean),clarifyTimeoutError:Dt.transitional(Dt.boolean)},!1),o!=null&&(z.isFunction(o)?n.paramsSerializer={serialize:o}:ll.assertOptions(o,{encode:Dt.function,serialize:Dt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ll.assertOptions(n,{baseUrl:Dt.spelling("baseURL"),withXsrfToken:Dt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&z.merge(s.common,s[n.method]);s&&z.forEach(["delete","get","head","post","put","patch","common"],x=>{delete s[x]}),n.headers=it.concat(i,s);const l=[];let a=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(a=a&&y.synchronous,l.unshift(y.fulfilled,y.rejected))});const c=[];this.interceptors.response.forEach(function(y){c.push(y.fulfilled,y.rejected)});let u,f=0,m;if(!a){const x=[Gh.bind(this),void 0];for(x.unshift.apply(x,l),x.push.apply(x,c),m=x.length,u=Promise.resolve(n);f<m;)u=u.then(x[f++],x[f++]);return u}m=l.length;let g=n;for(f=0;f<m;){const x=l[f++],y=l[f++];try{g=x(g)}catch(v){y.call(this,v);break}}try{u=Gh.call(this,g)}catch(x){return Promise.reject(x)}for(f=0,m=c.length;f<m;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=br(this.defaults,t);const n=Dx(t.baseURL,t.url,t.allowAbsoluteUrls);return $x(n,t.params,t.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(t){hr.prototype[t]=function(n,r){return this.request(br(r||{},{method:t,url:n,data:(r||{}).data}))}});z.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,l){return this.request(br(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}hr.prototype[t]=n(),hr.prototype[t+"Form"]=n(!0)});let nR=class Wx{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(l=>{r.subscribe(l),s=l}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,l){r.reason||(r.reason=new Ao(s,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Wx(function(o){t=o}),cancel:t}}};function rR(e){return function(n){return e.apply(null,n)}}function oR(e){return z.isObject(e)&&e.isAxiosError===!0}const Qu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qu).forEach(([e,t])=>{Qu[t]=e});function qx(e){const t=new hr(e),n=bx(hr.prototype.request,t);return z.extend(n,hr.prototype,t,{allOwnKeys:!0}),z.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return qx(br(e,o))},n}const be=qx(li);be.Axios=hr;be.CanceledError=Ao;be.CancelToken=nR;be.isCancel=zx;be.VERSION=Vx;be.toFormData=_a;be.AxiosError=Q;be.Cancel=be.CanceledError;be.all=function(t){return Promise.all(t)};be.spread=rR;be.isAxiosError=oR;be.mergeConfig=br;be.AxiosHeaders=it;be.formToJSON=e=>Ix(z.isHTMLForm(e)?new FormData(e):e);be.getAdapter=Hx.getAdapter;be.HttpStatusCode=Qu;be.default=be;const{Axios:AA,AxiosError:TA,CanceledError:PA,isCancel:MA,CancelToken:$A,VERSION:OA,all:IA,Cancel:zA,isAxiosError:LA,spread:DA,toFormData:FA,AxiosHeaders:UA,HttpStatusCode:BA,formToJSON:HA,getAdapter:VA,mergeConfig:WA}=be;class sR{constructor(){Nf(this,"client");this.client=be.create({baseURL:"http://localhost:3001/api",timeout:3e4,headers:{"Content-Type":"application/json"}}),this.client.interceptors.request.use(t=>{var n;return console.log(`API Request: ${(n=t.method)==null?void 0:n.toUpperCase()} ${t.url}`),t},t=>(console.error("API Request Error:",t),Promise.reject(t))),this.client.interceptors.response.use(t=>(console.log(`API Response: ${t.status} ${t.config.url}`),t),t=>{var n;return console.error("API Response Error:",((n=t.response)==null?void 0:n.data)||t.message),Promise.reject(t)})}async generateYaml(t){return(await this.client.post("/workflow/generate-yaml",t)).data}async validateWorkflow(t){return(await this.client.post("/workflow/validate",t)).data}async validateYaml(t){return(await this.client.post("/workflow/validate-yaml",{yaml:t})).data}async getBuiltinActions(){return(await this.client.get("/workflow/builtin-actions")).data}async getBuiltinAction(t){return(await this.client.get(`/workflow/builtin-actions/${encodeURIComponent(t)}`)).data}async getDSLFunctions(){return(await this.client.get("/workflow/dsl-functions")).data}async getActionSuggestions(t){return(await this.client.post("/workflow/action-suggestions",{context:t})).data}async checkHealth(){return(await this.client.get("/health")).data}}const os=new sR,{generateYaml:qA,validateWorkflow:YA,validateYaml:GA,getBuiltinActions:XA,getBuiltinAction:KA,getDSLFunctions:QA,getActionSuggestions:ZA,checkHealth:JA}=os,Wt=w3()(N3(j3((e,t)=>({currentWorkflow:null,workflows:[],selectedStepId:null,isGenerating:!1,showValidation:!1,sidebarCollapsed:!1,yamlPreviewVisible:!0,generatedYaml:null,validationResult:null,yamlMetadata:null,builtinActions:[],dslFunctions:[],createWorkflow:(n,r)=>{const o={id:Vl(),name:n,description:r,steps:[],created_at:new Date,updated_at:new Date};e(s=>({currentWorkflow:o,workflows:[...s.workflows,o],selectedStepId:null,generatedYaml:null,validationResult:null,yamlMetadata:null}))},loadWorkflow:n=>{const r=t().workflows.find(o=>o.id===n);r&&e({currentWorkflow:r,selectedStepId:null,generatedYaml:null,validationResult:null,yamlMetadata:null})},saveWorkflow:()=>{const{currentWorkflow:n}=t();if(!n)return;const r={...n,updated_at:new Date};e(o=>({currentWorkflow:r,workflows:o.workflows.map(s=>s.id===r.id?r:s)}))},updateWorkflow:n=>{const{currentWorkflow:r}=t();if(!r)return;const o={...r,...n,updated_at:new Date};e(s=>({currentWorkflow:o,workflows:s.workflows.map(i=>i.id===o.id?o:i)}))},deleteWorkflow:n=>{e(r=>{var o;return{workflows:r.workflows.filter(s=>s.id!==n),currentWorkflow:((o=r.currentWorkflow)==null?void 0:o.id)===n?null:r.currentWorkflow}})},addStep:(n,r)=>{const{currentWorkflow:o}=t();if(!o)return"";const s=Vl(),i={id:s,type:n,position:r,config:{},connections:[]},l={...o,steps:[...o.steps,i],updated_at:new Date};return e(a=>({currentWorkflow:l,workflows:a.workflows.map(c=>c.id===l.id?l:c),selectedStepId:s})),s},updateStep:(n,r)=>{const{currentWorkflow:o}=t();if(!o)return;const s={...o,steps:o.steps.map(i=>i.id===n?{...i,...r}:i),updated_at:new Date};e(i=>({currentWorkflow:s,workflows:i.workflows.map(l=>l.id===s.id?s:l)}))},deleteStep:n=>{const{currentWorkflow:r}=t();if(!r)return;const o={...r,steps:r.steps.filter(s=>s.id!==n),updated_at:new Date};e(s=>({currentWorkflow:o,workflows:s.workflows.map(i=>i.id===o.id?o:i),selectedStepId:s.selectedStepId===n?null:s.selectedStepId}))},selectStep:n=>{e({selectedStepId:n})},connectSteps:(n,r)=>{const{currentWorkflow:o}=t();if(!o)return;const s={...o,steps:o.steps.map(i=>i.id===n?{...i,connections:[...i.connections,r]}:i),updated_at:new Date};e(i=>({currentWorkflow:s,workflows:i.workflows.map(l=>l.id===s.id?s:l)}))},disconnectSteps:(n,r)=>{const{currentWorkflow:o}=t();if(!o)return;const s={...o,steps:o.steps.map(i=>i.id===n?{...i,connections:i.connections.filter(l=>l!==r)}:i),updated_at:new Date};e(i=>({currentWorkflow:s,workflows:i.workflows.map(l=>l.id===s.id?s:l)}))},generateYaml:async(n={})=>{const{currentWorkflow:r}=t();if(r){e({isGenerating:!0});try{const o=await os.generateYaml({workflow:r,options:{format:"yaml",validate:!0,include_comments:n.include_comments||!1}});e({generatedYaml:o.yaml,validationResult:o.validation,yamlMetadata:o.metadata,isGenerating:!1})}catch(o){throw console.error("Failed to generate YAML:",o),e({isGenerating:!1}),o}}},validateWorkflow:async()=>{const{currentWorkflow:n}=t();if(n)try{const r=await os.validateWorkflow(n);e({validationResult:r})}catch(r){throw console.error("Failed to validate workflow:",r),r}},loadBuiltinActions:async()=>{try{const n=await os.getBuiltinActions();e({builtinActions:n})}catch(n){throw console.error("Failed to load built-in actions:",n),n}},loadDSLFunctions:async()=>{try{const n=await os.getDSLFunctions();e({dslFunctions:n})}catch(n){throw console.error("Failed to load DSL functions:",n),n}},toggleSidebar:()=>{e(n=>({sidebarCollapsed:!n.sidebarCollapsed}))},toggleYamlPreview:()=>{e(n=>({yamlPreviewVisible:!n.yamlPreviewVisible}))},toggleValidation:()=>{e(n=>({showValidation:!n.showValidation}))}}),{name:"workflow-store",partialize:e=>({workflows:e.workflows,sidebarCollapsed:e.sidebarCollapsed,yamlPreviewVisible:e.yamlPreviewVisible})}),{name:"workflow-store"})),Nc=[{id:"user-lookup",name:"User Lookup",description:"Look up a user by email and send a notification",category:"Basic",icon:"👤",steps:[{type:"action",position:{x:100,y:100},config:{action_name:"mw.get_user_by_email",output_key:"user_result",input_args:{email:"data.user_email"}},connections:[]},{type:"action",position:{x:100,y:220},config:{action_name:"mw.send_plaintext_chat_notification",output_key:"notification_result",input_args:{user_id:"user_result.user_id",message:'$CONCAT("Hello ", user_result.display_name, "!")'}},connections:[]}]},{id:"approval-workflow",name:"Approval Workflow",description:"Create an approval request and check its status",category:"Approval",icon:"✅",steps:[{type:"action",position:{x:100,y:100},config:{action_name:"mw.create_generic_approval_request",output_key:"approval_request",input_args:{approver_email:"data.approver_email",request_title:"data.request_title",request_body:"data.request_body"}},connections:[]},{type:"action",position:{x:100,y:220},config:{action_name:"mw.get_approval_request_status",output_key:"approval_status",input_args:{request_id:"approval_request.request_id"}},connections:[]}]},{id:"conditional-processing",name:"Conditional Processing",description:"Process data differently based on conditions",category:"Control Flow",icon:"🔀",steps:[{type:"switch",position:{x:100,y:100},config:{output_key:"conditional_result",cases:[{condition:'data.priority == "high"',steps:[{type:"action",config:{action_name:"mw.send_email",output_key:"urgent_email",input_args:{to:"data.manager_email",subject:"Urgent: High Priority Request",body:"data.message"}}}]}],default:{steps:[{type:"script",config:{output_key:"normal_processing",code:'return {"status": "processed_normally"}'}}]}},connections:[]}]},{id:"batch-processing",name:"Batch Processing",description:"Process multiple items with error handling",category:"Advanced",icon:"🔄",steps:[{type:"for",position:{x:100,y:100},config:{output_key:"batch_results",each:"item",index:"index",in:"data.items",steps:[{type:"try_catch",config:{output_key:"item_result",try:{steps:[{type:"script",config:{output_key:"processed_item",code:'return {"item_id": item.id, "status": "success"}'}}]},catch:{steps:[{type:"script",config:{output_key:"error_result",code:'return {"item_id": item.id, "status": "error", "error": "Processing failed"}'}}]}}}]},connections:[]}]}];function iR({onClose:e}){const{createWorkflow:t}=Wt(),[n,r]=b.useState("All"),o=["All",...Array.from(new Set(Nc.map(l=>l.category)))],s=n==="All"?Nc:Nc.filter(l=>l.category===n),i=l=>{const a={id:Vl(),name:l.name,description:l.description,steps:l.steps.map(u=>({...u,id:Vl()}))};t(l.name,l.description);const c=Wt.getState();c.currentWorkflow&&c.updateWorkflow({steps:a.steps}),e()};return d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:[d.jsxs("div",{className:"p-6 border-b border-secondary-200",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h2",{className:"text-xl font-semibold text-secondary-900",children:"Workflow Templates"}),d.jsx("p",{className:"text-secondary-600 mt-1",children:"Start with a pre-built template or create from scratch"})]}),d.jsx("button",{onClick:e,className:"text-secondary-400 hover:text-secondary-600",children:"✕"})]}),d.jsx("div",{className:"flex space-x-2 mt-4",children:o.map(l=>d.jsx("button",{onClick:()=>r(l),className:Ee("px-3 py-1 rounded-full text-sm transition-colors",n===l?"bg-primary-100 text-primary-700":"bg-secondary-100 text-secondary-600 hover:bg-secondary-200"),children:l},l))})]}),d.jsx("div",{className:"p-6 overflow-y-auto max-h-96",children:d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map(l=>d.jsx("div",{className:"border border-secondary-200 rounded-lg p-4 hover:border-primary-300 transition-colors",children:d.jsxs("div",{className:"flex items-start space-x-3",children:[d.jsx("div",{className:"text-2xl",children:l.icon}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("h3",{className:"font-medium text-secondary-900",children:l.name}),d.jsx("p",{className:"text-sm text-secondary-600 mt-1",children:l.description}),d.jsxs("div",{className:"flex items-center justify-between mt-3",children:[d.jsx("span",{className:"text-xs bg-secondary-100 text-secondary-600 px-2 py-1 rounded",children:l.category}),d.jsxs("button",{onClick:()=>i(l),className:"btn btn-primary btn-sm",children:[d.jsx(Ws,{className:"h-3 w-3 mr-1"}),"Use Template"]})]})]})]})},l.id))})}),d.jsx("div",{className:"p-6 border-t border-secondary-200 bg-secondary-50",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("div",{className:"text-sm text-secondary-600",children:"Choose a template to get started quickly, or close to create from scratch."}),d.jsx("button",{onClick:e,className:"btn btn-secondary",children:"Create from Scratch"})]})})]})})}function lR(){const{workflows:e,createWorkflow:t,deleteWorkflow:n,loadBuiltinActions:r,loadDSLFunctions:o,builtinActions:s,dslFunctions:i}=Wt(),[l,a]=b.useState(!1),[c,u]=b.useState(!1),[f,m]=b.useState(""),[g,x]=b.useState(""),[y,v]=b.useState(!0);b.useEffect(()=>{(async()=>{try{await Promise.all([r(),o()])}catch(E){console.error("Failed to load dashboard data:",E),Oe.error("Failed to load some data. Please refresh the page.")}finally{v(!1)}})()},[r,o]);const h=()=>{if(!f.trim()){Oe.error("Workflow name is required");return}t(f.trim(),g.trim()||void 0),a(!1),m(""),x(""),Oe.success("Workflow created successfully")},p=(N,E)=>{confirm(`Are you sure you want to delete "${E}"?`)&&(n(N),Oe.success("Workflow deleted successfully"))},w=N=>{const E=JSON.stringify(N,null,2);mf(E,`${N.name}.json`,"application/json"),Oe.success("Workflow exported successfully")},S=[{name:"Total Workflows",value:e.length,icon:Lr,color:"text-primary-600 bg-primary-100"},{name:"Built-in Actions",value:s.length,icon:yj,color:"text-green-600 bg-green-100"},{name:"DSL Functions",value:i.length,icon:Vs,color:"text-blue-600 bg-blue-100"},{name:"Recent Activity",value:e.filter(N=>{const E=new Date(Date.now()-864e5);return new Date(N.updated_at)>E}).length,icon:Uu,color:"text-orange-600 bg-orange-100"}];return y?d.jsx("div",{className:"flex items-center justify-center min-h-screen",children:d.jsx("div",{className:"loading-spinner h-8 w-8"})}):d.jsxs("div",{className:"p-6 space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-secondary-900",children:"Dashboard"}),d.jsx("p",{className:"text-secondary-600 mt-1",children:"Manage your Moveworks Compound Action workflows"})]}),d.jsxs("div",{className:"flex space-x-3",children:[d.jsxs("button",{onClick:()=>u(!0),className:"btn btn-outline btn-md",children:[d.jsx(Lr,{className:"h-4 w-4 mr-2"}),"From Template"]}),d.jsxs("button",{onClick:()=>a(!0),className:"btn btn-primary btn-md",children:[d.jsx(Ws,{className:"h-4 w-4 mr-2"}),"New Workflow"]})]})]}),d.jsx("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:S.map(N=>d.jsx("div",{className:"card",children:d.jsx("div",{className:"card-content",children:d.jsxs("div",{className:"flex items-center",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("div",{className:Ee("p-3 rounded-lg",N.color),children:d.jsx(N.icon,{className:"h-6 w-6"})})}),d.jsx("div",{className:"ml-5 w-0 flex-1",children:d.jsxs("dl",{children:[d.jsx("dt",{className:"text-sm font-medium text-secondary-500 truncate",children:N.name}),d.jsx("dd",{className:"text-lg font-medium text-secondary-900",children:N.value})]})})]})})},N.name))}),d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("h2",{className:"card-title",children:"Recent Workflows"}),d.jsx(Ml,{to:"/builder",className:"btn btn-outline btn-sm",children:"View All"})]})}),d.jsx("div",{className:"card-content",children:e.length===0?d.jsxs("div",{className:"text-center py-12",children:[d.jsx(Lr,{className:"mx-auto h-12 w-12 text-secondary-400"}),d.jsx("h3",{className:"mt-2 text-sm font-medium text-secondary-900",children:"No workflows"}),d.jsx("p",{className:"mt-1 text-sm text-secondary-500",children:"Get started by creating a new workflow."}),d.jsxs("div",{className:"mt-6 flex space-x-3",children:[d.jsxs("button",{onClick:()=>u(!0),className:"btn btn-outline btn-md",children:[d.jsx(Lr,{className:"h-4 w-4 mr-2"}),"From Template"]}),d.jsxs("button",{onClick:()=>a(!0),className:"btn btn-primary btn-md",children:[d.jsx(Ws,{className:"h-4 w-4 mr-2"}),"New Workflow"]})]})]}):d.jsx("div",{className:"space-y-4",children:e.slice(0,5).map(N=>d.jsxs("div",{className:"flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors",children:[d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsx(Lr,{className:"h-5 w-5 text-secondary-400"}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-sm font-medium text-secondary-900",children:N.name}),N.description&&d.jsx("p",{className:"text-sm text-secondary-500",children:N.description})]})]}),d.jsxs("div",{className:"mt-2 flex items-center space-x-4 text-xs text-secondary-500",children:[d.jsxs("span",{children:[N.steps.length," steps"]}),d.jsxs("span",{children:["Updated ",f3(new Date(N.updated_at))]})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Ml,{to:`/builder/${N.id}`,className:"btn btn-ghost btn-sm",children:d.jsx(jj,{className:"h-4 w-4"})}),d.jsx("button",{onClick:()=>w(N),className:"btn btn-ghost btn-sm",title:"Export workflow",children:d.jsx(Sa,{className:"h-4 w-4"})}),d.jsx("button",{onClick:()=>p(N.id,N.name),className:"btn btn-ghost btn-sm text-error-600 hover:bg-error-50",children:d.jsx(pf,{className:"h-4 w-4"})})]})]},N.id))})})]}),l&&d.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:d.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25",onClick:()=>a(!1)}),d.jsx("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full",children:d.jsxs("div",{className:"p-6",children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-4",children:"Create New Workflow"}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-secondary-700 mb-1",children:"Name *"}),d.jsx("input",{type:"text",value:f,onChange:N=>m(N.target.value),className:"input w-full",placeholder:"Enter workflow name",autoFocus:!0})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-secondary-700 mb-1",children:"Description"}),d.jsx("textarea",{value:g,onChange:N=>x(N.target.value),className:"textarea w-full",placeholder:"Enter workflow description (optional)",rows:3})]})]}),d.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[d.jsx("button",{onClick:()=>a(!1),className:"btn btn-outline btn-md",children:"Cancel"}),d.jsx("button",{onClick:h,className:"btn btn-primary btn-md",children:"Create Workflow"})]})]})})]})}),c&&d.jsx(iR,{onClose:()=>u(!1)})]})}function aR(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Kh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Qh(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Kh(Object(n),!0).forEach(function(r){aR(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function cR(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function uR(e,t){if(e==null)return{};var n=cR(e,t),r,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)r=s[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function dR(e,t){return fR(e)||pR(e,t)||hR(e,t)||mR()}function fR(e){if(Array.isArray(e))return e}function pR(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],r=!0,o=!1,s=void 0;try{for(var i=e[Symbol.iterator](),l;!(r=(l=i.next()).done)&&(n.push(l.value),!(t&&n.length===t));r=!0);}catch(a){o=!0,s=a}finally{try{!r&&i.return!=null&&i.return()}finally{if(o)throw s}}return n}}function hR(e,t){if(e){if(typeof e=="string")return Zh(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Zh(e,t)}}function Zh(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function mR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gR(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Jh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function em(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Jh(Object(n),!0).forEach(function(r){gR(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function yR(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return t.reduceRight(function(o,s){return s(o)},r)}}function ss(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),s=0;s<r;s++)o[s]=arguments[s];return o.length>=e.length?e.apply(this,o):function(){for(var i=arguments.length,l=new Array(i),a=0;a<i;a++)l[a]=arguments[a];return t.apply(n,[].concat(o,l))}}}function Yl(e){return{}.toString.call(e).includes("Object")}function xR(e){return!Object.keys(e).length}function Gs(e){return typeof e=="function"}function vR(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function wR(e,t){return Yl(t)||Fn("changeType"),Object.keys(t).some(function(n){return!vR(e,n)})&&Fn("changeField"),t}function SR(e){Gs(e)||Fn("selectorType")}function bR(e){Gs(e)||Yl(e)||Fn("handlerType"),Yl(e)&&Object.values(e).some(function(t){return!Gs(t)})&&Fn("handlersType")}function NR(e){e||Fn("initialIsRequired"),Yl(e)||Fn("initialType"),xR(e)&&Fn("initialContent")}function ER(e,t){throw new Error(e[t]||e.default)}var kR={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},Fn=ss(ER)(kR),zi={changes:wR,selector:SR,handler:bR,initial:NR};function _R(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};zi.initial(e),zi.handler(t);var n={current:e},r=ss(RR)(n,t),o=ss(jR)(n),s=ss(zi.changes)(e),i=ss(CR)(n);function l(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(u){return u};return zi.selector(c),c(n.current)}function a(c){yR(r,o,s,i)(c)}return[l,a]}function CR(e,t){return Gs(t)?t(e.current):t}function jR(e,t){return e.current=em(em({},e.current),t),t}function RR(e,t,n){return Gs(t)?t(e.current):Object.keys(n).forEach(function(r){var o;return(o=t[r])===null||o===void 0?void 0:o.call(t,e.current[r])}),n}var AR={create:_R},TR={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};function PR(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),s=0;s<r;s++)o[s]=arguments[s];return o.length>=e.length?e.apply(this,o):function(){for(var i=arguments.length,l=new Array(i),a=0;a<i;a++)l[a]=arguments[a];return t.apply(n,[].concat(o,l))}}}function MR(e){return{}.toString.call(e).includes("Object")}function $R(e){return e||tm("configIsRequired"),MR(e)||tm("configType"),e.urls?(OR(),{paths:{vs:e.urls.monacoBase}}):e}function OR(){console.warn(Yx.deprecation)}function IR(e,t){throw new Error(e[t]||e.default)}var Yx={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:`Deprecation warning!
    You are using deprecated way of configuration.

    Instead of using
      monaco.config({ urls: { monacoBase: '...' } })
    use
      monaco.config({ paths: { vs: '...' } })

    For more please check the link https://github.com/suren-atoyan/monaco-loader#config
  `},tm=PR(IR)(Yx),zR={config:$R},LR=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(o){return n.reduceRight(function(s,i){return i(s)},o)}};function Gx(e,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&e[n]&&Object.assign(t[n],Gx(e[n],t[n]))}),Qh(Qh({},e),t)}var DR={type:"cancelation",msg:"operation is manually canceled"};function Ec(e){var t=!1,n=new Promise(function(r,o){e.then(function(s){return t?o(DR):r(s)}),e.catch(o)});return n.cancel=function(){return t=!0},n}var FR=AR.create({config:TR,isInitialized:!1,resolve:null,reject:null,monaco:null}),Xx=dR(FR,2),ai=Xx[0],Ra=Xx[1];function UR(e){var t=zR.config(e),n=t.monaco,r=uR(t,["monaco"]);Ra(function(o){return{config:Gx(o.config,r),monaco:n}})}function BR(){var e=ai(function(t){var n=t.monaco,r=t.isInitialized,o=t.resolve;return{monaco:n,isInitialized:r,resolve:o}});if(!e.isInitialized){if(Ra({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Ec(kc);if(window.monaco&&window.monaco.editor)return Kx(window.monaco),e.resolve(window.monaco),Ec(kc);LR(HR,WR)(qR)}return Ec(kc)}function HR(e){return document.body.appendChild(e)}function VR(e){var t=document.createElement("script");return e&&(t.src=e),t}function WR(e){var t=ai(function(r){var o=r.config,s=r.reject;return{config:o,reject:s}}),n=VR("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function qR(){var e=ai(function(n){var r=n.config,o=n.resolve,s=n.reject;return{config:r,resolve:o,reject:s}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(n){Kx(n),e.resolve(n)},function(n){e.reject(n)})}function Kx(e){ai().monaco||Ra({monaco:e})}function YR(){return ai(function(e){var t=e.monaco;return t})}var kc=new Promise(function(e,t){return Ra({resolve:e,reject:t})}),Qx={config:UR,init:BR,__getMonacoInstance:YR},GR={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},_c=GR,XR={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},KR=XR;function QR({children:e}){return I.createElement("div",{style:KR.container},e)}var ZR=QR,JR=ZR;function eA({width:e,height:t,isEditorReady:n,loading:r,_ref:o,className:s,wrapperProps:i}){return I.createElement("section",{style:{..._c.wrapper,width:e,height:t},...i},!n&&I.createElement(JR,null,r),I.createElement("div",{ref:o,style:{..._c.fullWidth,...!n&&_c.hide},className:s}))}var tA=eA,Zx=b.memo(tA);function nA(e){b.useEffect(e,[])}var Jx=nA;function rA(e,t,n=!0){let r=b.useRef(!0);b.useEffect(r.current||!n?()=>{r.current=!1}:e,t)}var ct=rA;function gs(){}function eo(e,t,n,r){return oA(e,r)||sA(e,t,n,r)}function oA(e,t){return e.editor.getModel(ev(e,t))}function sA(e,t,n,r){return e.editor.createModel(t,n,r?ev(e,r):void 0)}function ev(e,t){return e.Uri.parse(t)}function iA({original:e,modified:t,language:n,originalLanguage:r,modifiedLanguage:o,originalModelPath:s,modifiedModelPath:i,keepCurrentOriginalModel:l=!1,keepCurrentModifiedModel:a=!1,theme:c="light",loading:u="Loading...",options:f={},height:m="100%",width:g="100%",className:x,wrapperProps:y={},beforeMount:v=gs,onMount:h=gs}){let[p,w]=b.useState(!1),[S,N]=b.useState(!0),E=b.useRef(null),_=b.useRef(null),C=b.useRef(null),T=b.useRef(h),P=b.useRef(v),D=b.useRef(!1);Jx(()=>{let A=Qx.init();return A.then(R=>(_.current=R)&&N(!1)).catch(R=>(R==null?void 0:R.type)!=="cancelation"&&console.error("Monaco initialization: error:",R)),()=>E.current?k():A.cancel()}),ct(()=>{if(E.current&&_.current){let A=E.current.getOriginalEditor(),R=eo(_.current,e||"",r||n||"text",s||"");R!==A.getModel()&&A.setModel(R)}},[s],p),ct(()=>{if(E.current&&_.current){let A=E.current.getModifiedEditor(),R=eo(_.current,t||"",o||n||"text",i||"");R!==A.getModel()&&A.setModel(R)}},[i],p),ct(()=>{let A=E.current.getModifiedEditor();A.getOption(_.current.editor.EditorOption.readOnly)?A.setValue(t||""):t!==A.getValue()&&(A.executeEdits("",[{range:A.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),A.pushUndoStop())},[t],p),ct(()=>{var A,R;(R=(A=E.current)==null?void 0:A.getModel())==null||R.original.setValue(e||"")},[e],p),ct(()=>{let{original:A,modified:R}=E.current.getModel();_.current.editor.setModelLanguage(A,r||n||"text"),_.current.editor.setModelLanguage(R,o||n||"text")},[n,r,o],p),ct(()=>{var A;(A=_.current)==null||A.editor.setTheme(c)},[c],p),ct(()=>{var A;(A=E.current)==null||A.updateOptions(f)},[f],p);let U=b.useCallback(()=>{var F;if(!_.current)return;P.current(_.current);let A=eo(_.current,e||"",r||n||"text",s||""),R=eo(_.current,t||"",o||n||"text",i||"");(F=E.current)==null||F.setModel({original:A,modified:R})},[n,t,o,e,r,s,i]),B=b.useCallback(()=>{var A;!D.current&&C.current&&(E.current=_.current.editor.createDiffEditor(C.current,{automaticLayout:!0,...f}),U(),(A=_.current)==null||A.editor.setTheme(c),w(!0),D.current=!0)},[f,c,U]);b.useEffect(()=>{p&&T.current(E.current,_.current)},[p]),b.useEffect(()=>{!S&&!p&&B()},[S,p,B]);function k(){var R,F,M,j;let A=(R=E.current)==null?void 0:R.getModel();l||((F=A==null?void 0:A.original)==null||F.dispose()),a||((M=A==null?void 0:A.modified)==null||M.dispose()),(j=E.current)==null||j.dispose()}return I.createElement(Zx,{width:g,height:m,isEditorReady:p,loading:u,_ref:C,className:x,wrapperProps:y})}var lA=iA;b.memo(lA);function aA(e){let t=b.useRef();return b.useEffect(()=>{t.current=e},[e]),t.current}var cA=aA,Li=new Map;function uA({defaultValue:e,defaultLanguage:t,defaultPath:n,value:r,language:o,path:s,theme:i="light",line:l,loading:a="Loading...",options:c={},overrideServices:u={},saveViewState:f=!0,keepCurrentModel:m=!1,width:g="100%",height:x="100%",className:y,wrapperProps:v={},beforeMount:h=gs,onMount:p=gs,onChange:w,onValidate:S=gs}){let[N,E]=b.useState(!1),[_,C]=b.useState(!0),T=b.useRef(null),P=b.useRef(null),D=b.useRef(null),U=b.useRef(p),B=b.useRef(h),k=b.useRef(),A=b.useRef(r),R=cA(s),F=b.useRef(!1),M=b.useRef(!1);Jx(()=>{let $=Qx.init();return $.then(L=>(T.current=L)&&C(!1)).catch(L=>(L==null?void 0:L.type)!=="cancelation"&&console.error("Monaco initialization: error:",L)),()=>P.current?O():$.cancel()}),ct(()=>{var L,V,W,Y;let $=eo(T.current,e||r||"",t||o||"",s||n||"");$!==((L=P.current)==null?void 0:L.getModel())&&(f&&Li.set(R,(V=P.current)==null?void 0:V.saveViewState()),(W=P.current)==null||W.setModel($),f&&((Y=P.current)==null||Y.restoreViewState(Li.get(s))))},[s],N),ct(()=>{var $;($=P.current)==null||$.updateOptions(c)},[c],N),ct(()=>{!P.current||r===void 0||(P.current.getOption(T.current.editor.EditorOption.readOnly)?P.current.setValue(r):r!==P.current.getValue()&&(M.current=!0,P.current.executeEdits("",[{range:P.current.getModel().getFullModelRange(),text:r,forceMoveMarkers:!0}]),P.current.pushUndoStop(),M.current=!1))},[r],N),ct(()=>{var L,V;let $=(L=P.current)==null?void 0:L.getModel();$&&o&&((V=T.current)==null||V.editor.setModelLanguage($,o))},[o],N),ct(()=>{var $;l!==void 0&&(($=P.current)==null||$.revealLine(l))},[l],N),ct(()=>{var $;($=T.current)==null||$.editor.setTheme(i)},[i],N);let j=b.useCallback(()=>{var $;if(!(!D.current||!T.current)&&!F.current){B.current(T.current);let L=s||n,V=eo(T.current,r||e||"",t||o||"",L||"");P.current=($=T.current)==null?void 0:$.editor.create(D.current,{model:V,automaticLayout:!0,...c},u),f&&P.current.restoreViewState(Li.get(L)),T.current.editor.setTheme(i),l!==void 0&&P.current.revealLine(l),E(!0),F.current=!0}},[e,t,n,r,o,s,c,u,f,i,l]);b.useEffect(()=>{N&&U.current(P.current,T.current)},[N]),b.useEffect(()=>{!_&&!N&&j()},[_,N,j]),A.current=r,b.useEffect(()=>{var $,L;N&&w&&(($=k.current)==null||$.dispose(),k.current=(L=P.current)==null?void 0:L.onDidChangeModelContent(V=>{M.current||w(P.current.getValue(),V)}))},[N,w]),b.useEffect(()=>{if(N){let $=T.current.editor.onDidChangeMarkers(L=>{var W;let V=(W=P.current.getModel())==null?void 0:W.uri;if(V&&L.find(Y=>Y.path===V.path)){let Y=T.current.editor.getModelMarkers({resource:V});S==null||S(Y)}});return()=>{$==null||$.dispose()}}return()=>{}},[N,S]);function O(){var $,L;($=k.current)==null||$.dispose(),m?f&&Li.set(s,P.current.saveViewState()):(L=P.current.getModel())==null||L.dispose(),P.current.dispose()}return I.createElement(Zx,{width:g,height:x,isEditorReady:N,loading:a,_ref:D,className:y,wrapperProps:v})}var dA=uA,fA=b.memo(dA),pA=fA;const nm=[{name:"$CONCAT",description:"Concatenate strings or arrays",syntax:"$CONCAT(value1, value2, ...)",example:'$CONCAT("Hello ", user_result.display_name, "!")',category:"string"},{name:"$MAP",description:"Transform each element in an array",syntax:"$MAP(array, expression)",example:"$MAP(users, item.email)",category:"array"},{name:"$FILTER",description:"Filter array elements based on condition",syntax:"$FILTER(array, condition)",example:'$FILTER(tickets, item.priority == "high")',category:"array"},{name:"$GET",description:"Get value from object with optional default",syntax:"$GET(object, path, default)",example:'$GET(user_result, "profile.department", "Unknown")',category:"object"},{name:"$IF",description:"Conditional expression",syntax:"$IF(condition, true_value, false_value)",example:'$IF(data.amount > 1000, "high", "normal")',category:"conditional"},{name:"$LENGTH",description:"Get length of string or array",syntax:"$LENGTH(value)",example:"$LENGTH(approval_list)",category:"utility"}];function Dr({value:e,onChange:t,placeholder:n,label:r,description:o,dataContext:s={},expectedType:i,className:l}){const[a,c]=b.useState(!1),[u,f]=b.useState(0),[m,g]=b.useState({isValid:!0,type:"success"}),x=b.useRef(null),y=b.useRef(null),v=()=>{const _=h(),C=[];return(_.startsWith("$")||_==="")&&nm.forEach(T=>{T.name.toLowerCase().includes(_.toLowerCase().replace("$",""))&&C.push({type:"dsl",label:T.name,description:T.description,insertText:T.syntax})}),(_.includes(".")||Object.keys(s).some(T=>T.toLowerCase().includes(_.toLowerCase())))&&Object.keys(s).forEach(T=>{T.toLowerCase().includes(_.toLowerCase())&&C.push({type:"data",label:T,description:`Available data: ${typeof s[T]}`,insertText:T})}),i&&_===""&&p(i).forEach(P=>{C.push({type:"template",label:P.label,description:P.description,insertText:P.value})}),C.slice(0,8)},h=()=>{const C=e.substring(0,u).split(/[\s,()[\]{}]/);return C[C.length-1]||""},p=_=>({string:[{label:"Simple string",description:"Plain text value",value:'"example text"'},{label:"Data reference",description:"Reference to workflow data",value:"data.field_name"},{label:"Concatenation",description:"Combine multiple values",value:'$CONCAT("Hello ", data.name)'}],object:[{label:"Simple object",description:"Key-value pairs",value:`{
  "key": "value"
}`},{label:"Data mapping",description:"Map from workflow data",value:`{
  "field": data.source_field
}`}],array:[{label:"Simple array",description:"List of values",value:'["item1", "item2"]'},{label:"Data array",description:"Reference to array data",value:"data.items"},{label:"Mapped array",description:"Transform array elements",value:"$MAP(data.items, item.name)"}]})[_]||[];b.useEffect(()=>{const C=setTimeout(()=>{if(!e.trim()){g({isValid:!0,type:"success"});return}try{if(e.includes("$")){const T=/\$[A-Z_]+\(/g,P=e.match(T);if(P){const D=P.filter(U=>!nm.some(B=>U.startsWith(B.name+"(")));if(D.length>0){g({isValid:!1,type:"error",message:`Unknown DSL function: ${D[0].replace("(","")}`});return}}}if(e.includes("data.")){const P=(e.match(/data\.[a-zA-Z_][a-zA-Z0-9_.]*\b/g)||[]).filter(D=>{const U=D.replace("data.","");return!w(s,U)});if(P.length>0){g({isValid:!1,type:"warning",message:`Data reference may not exist: ${P[0]}`});return}}(e.startsWith("{")&&e.endsWith("}")||e.startsWith("[")&&e.endsWith("]"))&&JSON.parse(e),g({isValid:!0,type:"success"})}catch{g({isValid:!1,type:"error",message:"Invalid JSON syntax"})}},300);return()=>clearTimeout(C)},[e,s]);const w=(_,C)=>C.split(".").reduce((T,P)=>T&&T[P]!==void 0?T[P]:void 0,_)!==void 0,S=_=>{const C=_.target.value,T=_.target.selectionStart;t(C),f(T),c(C.length>0)},N=_=>{const C=e.substring(0,u),T=e.substring(u),P=h(),D=C.substring(0,C.length-P.length),U=D+_.insertText+T;t(U),c(!1),setTimeout(()=>{var k,A;(k=x.current)==null||k.focus();const B=D.length+_.insertText.length;(A=x.current)==null||A.setSelectionRange(B,B)},0)},E=a?v():[];return d.jsxs("div",{className:Ee("relative",l),children:[r&&d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[r,o&&d.jsxs("span",{className:"ml-2 text-xs text-gray-500",children:[d.jsx(kj,{className:"inline h-3 w-3 mr-1"}),o]})]}),d.jsxs("div",{className:"relative",children:[d.jsx("textarea",{ref:x,value:e,onChange:S,onFocus:()=>c(e.length>0),onBlur:()=>setTimeout(()=>c(!1),200),placeholder:n,className:Ee("w-full px-3 py-2 border rounded-md text-sm font-mono resize-none","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",m.isValid?"border-gray-300":m.type==="error"?"border-red-300":"border-yellow-300"),rows:e.split(`
`).length||1,style:{minHeight:"40px",maxHeight:"200px"}}),d.jsx("div",{className:"absolute right-2 top-2",children:m.isValid?d.jsx(Vs,{className:"h-4 w-4 text-green-500"}):d.jsx(wa,{className:Ee("h-4 w-4",m.type==="error"?"text-red-500":"text-yellow-500")})})]}),!m.isValid&&m.message&&d.jsx("div",{className:Ee("mt-1 text-xs",m.type==="error"?"text-red-600":"text-yellow-600"),children:m.message}),a&&E.length>0&&d.jsx("div",{ref:y,className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-y-auto",children:E.map((_,C)=>d.jsx("div",{onClick:()=>N(_),className:"px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:Ee("text-xs px-1.5 py-0.5 rounded text-white font-medium",_.type==="dsl"?"bg-blue-500":_.type==="data"?"bg-green-500":"bg-purple-500"),children:_.type.toUpperCase()}),d.jsx("span",{className:"font-medium text-sm",children:_.label})]}),d.jsx("div",{className:"text-xs text-gray-600 mt-1",children:_.description})]}),d.jsx(vj,{className:"h-4 w-4 text-gray-400 transform -rotate-90"})]})},C))})]})}function hA({step:e,onUpdate:t}){var h,p,w,S,N;const{currentWorkflow:n}=Wt(),[r,o]=b.useState([]),[s,i]=b.useState([]),[l,a]=b.useState(!0),[c,u]=b.useState(!0);b.useEffect(()=>{if(!n||!e)return;const E=f(e,n.steps),_=m(n.steps,e);o(E),i(_)},[e,n]);const f=(E,_)=>{var D;const C=[],T=_.findIndex(U=>U.id===E.id),P=_.slice(0,T);if(E.type==="action"&&((D=E.config)!=null&&D.action_name)){const U=E.config.action_name;if(U.includes("notification")||U.includes("message")){P.some(A=>{var R,F;return(F=(R=A.config)==null?void 0:R.action_name)==null?void 0:F.includes("get_user")})||C.push({action:"mw.get_user_by_email",reason:"User lookup is typically needed before sending notifications",priority:"high",category:"prerequisite"});const k=E.config.input_args||{};!k.user_id&&!k.user_record_id&&C.push({action:"Add user_id reference",reason:"Notification actions require a user_id parameter",priority:"high",category:"enhancement"})}U.includes("approval")&&C.push({action:"mw.get_approval_request_status",reason:"Consider checking approval status after creating request",priority:"medium",category:"follow-up"}),U.includes("email")&&(P.some(k=>{var A,R;return(R=(A=k.config)==null?void 0:A.action_name)==null?void 0:R.includes("get_user")})||C.push({action:"mw.get_user_by_email",reason:"User lookup can provide email address and user details",priority:"medium",category:"prerequisite"})),_.some(B=>B.type==="try_catch")||C.push({action:"Add error handling",reason:"Consider wrapping API calls in try/catch blocks",priority:"low",category:"enhancement"})}return C},m=(E,_)=>{const C=[],T=E.findIndex(D=>D.id===_.id),P=E.slice(0,T);return C.push({key:"data",type:"object",source:"Workflow Input",description:"Input data passed to the workflow",available:!0}),P.forEach(D=>{var U;if((U=D.config)!=null&&U.output_key){const B=D.config.output_key;let k=`Output from ${D.type} step`,A="object";if(D.type==="action"&&D.config.action_name){const R=D.config.action_name;R.includes("get_user")?(k="User object with id, email, display_name, etc.",A="User"):R.includes("approval")?(k="Approval request object with status, id, etc.",A="ApprovalRequest"):R.includes("ticket")&&(k="Ticket object with id, status, description, etc.",A="Ticket")}C.push({key:B,type:A,source:`Step: ${D.config.action_name||D.type}`,description:k,available:!0})}}),C},g=(E,_)=>{t({config:{...e.config,[E]:_}})},x=(E,_)=>{var T;const C=((T=e.config)==null?void 0:T.input_args)||{};g("input_args",{...C,[E]:_})},y=E=>{E.action.startsWith("mw.")?console.log("Would add new step:",E.action):E.action==="Add user_id reference"&&x("user_id","user_result.user_id")},v=()=>{const E={};return s.forEach(_=>{_.available&&(E[_.key]={type:_.type,description:_.description})}),E};return d.jsxs("div",{className:"space-y-6",children:[r.length>0&&d.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[d.jsxs("div",{className:"flex items-center justify-between mb-3",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(_j,{className:"h-4 w-4 text-blue-600"}),d.jsx("h4",{className:"font-medium text-blue-900",children:"Smart Suggestions"})]}),d.jsx("button",{onClick:()=>a(!l),className:"text-blue-600 hover:text-blue-800 text-sm",children:l?"Hide":"Show"})]}),l&&d.jsx("div",{className:"space-y-2",children:r.map((E,_)=>d.jsxs("div",{className:"flex items-start justify-between p-3 bg-white rounded border border-blue-100",children:[d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[d.jsx("span",{className:Ee("text-xs px-2 py-1 rounded font-medium",E.priority==="high"?"bg-red-100 text-red-700":E.priority==="medium"?"bg-yellow-100 text-yellow-700":"bg-gray-100 text-gray-700"),children:E.priority.toUpperCase()}),d.jsx("span",{className:Ee("text-xs px-2 py-1 rounded",E.category==="prerequisite"?"bg-orange-100 text-orange-700":E.category==="follow-up"?"bg-green-100 text-green-700":"bg-purple-100 text-purple-700"),children:E.category})]}),d.jsx("div",{className:"font-medium text-sm text-gray-900 mb-1",children:E.action}),d.jsx("div",{className:"text-xs text-gray-600",children:E.reason})]}),d.jsx("button",{onClick:()=>y(E),className:"ml-3 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700",children:"Apply"})]},_))})]}),d.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[d.jsxs("div",{className:"flex items-center justify-between mb-3",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Bu,{className:"h-4 w-4 text-green-600"}),d.jsx("h4",{className:"font-medium text-green-900",children:"Available Data Context"})]}),d.jsx("button",{onClick:()=>u(!c),className:"text-green-600 hover:text-green-800 text-sm",children:c?"Hide":"Show"})]}),c&&d.jsx("div",{className:"space-y-2",children:s.map((E,_)=>d.jsx("div",{className:"flex items-center justify-between p-2 bg-white rounded border border-green-100",children:d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("code",{className:"text-sm font-mono bg-gray-100 px-2 py-1 rounded",children:E.key}),d.jsx("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded",children:E.type})]}),d.jsxs("div",{className:"text-xs text-gray-600 mt-1",children:[E.description," • From: ",E.source]})]})},_))})]}),d.jsxs("div",{className:"space-y-4",children:[d.jsx("h4",{className:"font-medium text-gray-900",children:"Step Configuration"}),d.jsx(Dr,{label:"Output Key",description:"Variable name to store the result",value:((h=e.config)==null?void 0:h.output_key)||"",onChange:E=>g("output_key",E),placeholder:`${e.type}_result`,expectedType:"string",dataContext:v()}),e.type==="action"&&d.jsxs(d.Fragment,{children:[d.jsx(Dr,{label:"Action Name",description:"Moveworks built-in action to execute",value:((p=e.config)==null?void 0:p.action_name)||"",onChange:E=>g("action_name",E),placeholder:"mw.get_user_by_email",expectedType:"string",dataContext:v()}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Input Arguments"}),d.jsxs("div",{className:"space-y-3",children:[Object.entries(((w=e.config)==null?void 0:w.input_args)||{}).map(([E,_])=>d.jsxs("div",{className:"flex items-start space-x-2",children:[d.jsx("div",{className:"flex-1",children:d.jsx(Dr,{label:E,value:typeof _=="string"?_:JSON.stringify(_),onChange:C=>x(E,C),dataContext:v(),expectedType:"string"})}),d.jsx("button",{onClick:()=>{var T;const C={...(T=e.config)==null?void 0:T.input_args};delete C[E],g("input_args",C)},className:"mt-6 p-1 text-red-500 hover:text-red-700",children:"×"})]},E)),d.jsxs("button",{onClick:()=>{const E=prompt("Enter argument name:");E&&x(E,"")},className:"flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800",children:[d.jsx(Ws,{className:"h-4 w-4"}),d.jsx("span",{children:"Add Argument"})]})]})]})]}),e.type==="script"&&d.jsx(Dr,{label:"APIthon Code",description:"Python-like code to execute",value:((S=e.config)==null?void 0:S.code)||"",onChange:E=>g("code",E),placeholder:`# APIthon script
return {'result': 'success'}`,expectedType:"string",dataContext:v()}),e.type==="switch"&&d.jsx(Dr,{label:"Condition",description:"Boolean expression to evaluate",value:((N=e.config)==null?void 0:N.condition)||"",onChange:E=>g("condition",E),placeholder:"data.amount > 1000",expectedType:"string",dataContext:v()})]})]})}const Di=[{type:"action",label:"Action",icon:"⚡",description:"Execute a Moveworks built-in action"},{type:"script",label:"Script",icon:"📝",description:"Run custom APIthon code"},{type:"switch",label:"Switch",icon:"🔀",description:"Conditional branching logic"},{type:"for",label:"For Loop",icon:"🔄",description:"Iterate over a collection"},{type:"parallel",label:"Parallel",icon:"⚡",description:"Execute steps in parallel"},{type:"try_catch",label:"Try/Catch",icon:"🛡️",description:"Error handling"},{type:"raise",label:"Raise",icon:"⚠️",description:"Throw an error"},{type:"return",label:"Return",icon:"↩️",description:"Return a value"}];function mA({className:e,showVisualization:t,onToggleVisualization:n}){var y;const{currentWorkflow:r,selectedStepId:o,addStep:s,updateStep:i,deleteStep:l,selectStep:a}=Wt(),[c,u]=b.useState(!1),f=r==null?void 0:r.steps.find(v=>v.id===o),m=v=>{const h={x:100,y:100+((r==null?void 0:r.steps.length)||0)*120};s(v,h),u(!1)},g=v=>{confirm("Are you sure you want to delete this step?")&&l(v)},x=(v,h)=>{o&&i(o,{config:{...f==null?void 0:f.config,[v]:h}})};return d.jsxs("div",{className:Ee("bg-white border-r border-secondary-200 flex flex-col",e),children:[d.jsx("div",{className:"p-4 border-b border-secondary-200",children:d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsx("h3",{className:"font-medium text-secondary-900",children:"Workflow Steps"}),d.jsxs("div",{className:"flex items-center space-x-2",children:[n&&d.jsx("button",{onClick:n,className:"btn btn-ghost btn-sm",title:t?"Hide Visualization":"Show Visualization",children:t?d.jsx(dx,{className:"h-4 w-4"}):d.jsx(Hu,{className:"h-4 w-4"})}),d.jsxs("div",{className:"relative",children:[d.jsxs("button",{onClick:()=>u(!c),className:"btn btn-primary btn-sm",children:[d.jsx(Ws,{className:"h-4 w-4 mr-1"}),"Add Step"]}),c&&d.jsx("div",{className:"absolute right-0 top-full mt-2 w-64 bg-white border border-secondary-200 rounded-lg shadow-lg z-50",children:d.jsxs("div",{className:"p-2",children:[d.jsx("div",{className:"text-xs font-medium text-secondary-600 mb-2 px-2",children:"Choose Step Type"}),Di.map(v=>d.jsx("button",{onClick:()=>m(v.type),className:"w-full text-left p-2 rounded hover:bg-secondary-50 transition-colors",children:d.jsxs("div",{className:"flex items-start space-x-3",children:[d.jsx("span",{className:"text-lg",children:v.icon}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("div",{className:"font-medium text-secondary-900",children:v.label}),d.jsx("div",{className:"text-xs text-secondary-600",children:v.description})]})]})},v.type))]})})]})]})]})}),d.jsx("div",{className:"flex-1 overflow-y-auto",children:(r==null?void 0:r.steps.length)===0?d.jsxs("div",{className:"p-4 text-center text-secondary-500",children:[d.jsx("div",{className:"text-4xl mb-2",children:"📝"}),d.jsx("p",{className:"text-sm",children:"No steps yet"}),d.jsx("p",{className:"text-xs",children:'Click "Add Step" to get started'})]}):d.jsx("div",{className:"p-2 space-y-2",children:r==null?void 0:r.steps.map((v,h)=>{var p,w,S;return d.jsx("div",{className:Ee("p-3 border rounded-lg cursor-pointer transition-colors",o===v.id?"border-primary-300 bg-primary-50":"border-secondary-200 hover:border-secondary-300"),onClick:()=>a(v.id),children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:"text-xs font-medium text-secondary-500",children:h+1}),d.jsx("span",{className:"text-lg",children:((p=Di.find(N=>N.type===v.type))==null?void 0:p.icon)||"❓"}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("div",{className:"font-medium text-secondary-900 text-sm",children:((w=Di.find(N=>N.type===v.type))==null?void 0:w.label)||v.type}),((S=v.config)==null?void 0:S.output_key)&&d.jsxs("div",{className:"text-xs text-secondary-600",children:["→ ",v.config.output_key]})]})]}),d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("button",{onClick:N=>{N.stopPropagation(),a(v.id)},className:"p-1 text-secondary-400 hover:text-secondary-600",title:"Configure",children:d.jsx(px,{className:"h-3 w-3"})}),d.jsx("button",{onClick:N=>{N.stopPropagation(),g(v.id)},className:"p-1 text-secondary-400 hover:text-error-600",title:"Delete",children:d.jsx(pf,{className:"h-3 w-3"})})]})]})},v.id)})})}),f&&d.jsx("div",{className:"border-t border-secondary-200 bg-secondary-50 flex-1 overflow-y-auto",children:d.jsxs("div",{className:"p-4",children:[d.jsxs("h4",{className:"font-medium text-secondary-900 mb-4",children:["Configure ",(y=Di.find(v=>v.type===f.type))==null?void 0:y.label]}),d.jsx(hA,{step:f,onUpdate:v=>{Object.entries(v).forEach(([h,p])=>{h==="config"?Object.entries(p).forEach(([w,S])=>{x(w,S)}):i(f.id,{[h]:p})})}})]})}),c&&d.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>u(!1)})]})}const gA={"mw.get_user_by_email":{required:["email"],optional:[],returns:"User object with user_id, display_name, email, etc."},"mw.get_user_by_id":{required:["user_id"],optional:[],returns:"User object with user_id, display_name, email, etc."},"mw.send_plaintext_chat_notification":{required:["user_id","message"],optional:["thread_id"],returns:"Notification result with success status"},"mw.send_email":{required:["to","subject","body"],optional:["cc","bcc","attachments"],returns:"Email result with message_id"},"mw.create_generic_approval_request":{required:["approver_email","request_title","request_body"],optional:["due_date","priority"],returns:"Approval request with request_id"},"mw.get_approval_request_status":{required:["request_id"],optional:[],returns:"Approval status object"},"mw.create_ticket":{required:["title","description"],optional:["priority","assignee","labels"],returns:"Ticket object with ticket_id"},"mw.get_ticket_by_id":{required:["ticket_id"],optional:[],returns:"Ticket object with details"}},yA={$CONCAT:{minArgs:2,maxArgs:1/0,description:"Concatenate strings or arrays"},$MAP:{minArgs:2,maxArgs:2,description:"Transform each element in an array"},$FILTER:{minArgs:2,maxArgs:2,description:"Filter array elements based on condition"},$GET:{minArgs:2,maxArgs:3,description:"Get value from object with optional default"},$IF:{minArgs:3,maxArgs:3,description:"Conditional expression"},$LENGTH:{minArgs:1,maxArgs:1,description:"Get length of string or array"},$UPPER:{minArgs:1,maxArgs:1,description:"Convert string to uppercase"},$LOWER:{minArgs:1,maxArgs:1,description:"Convert string to lowercase"}};class xA{validateWorkflow(t){const n=[],r=[],o=[];return this.validateWorkflowStructure(t,n,r),t.steps.forEach(s=>{this.validateStep(s,t,n,r,o)}),this.validateDataFlow(t,n,r,o),this.checkBestPractices(t,r,o),{isValid:n.filter(s=>s.severity==="error").length===0,errors:n,warnings:r,suggestions:o}}validateWorkflowStructure(t,n,r){var o;(o=t.name)!=null&&o.trim()||n.push({id:"missing-name",message:"Workflow must have a name",severity:"error",fixable:!0}),(!t.steps||t.steps.length===0)&&n.push({id:"no-steps",message:"Workflow must contain at least one step",severity:"error",fixable:!1}),t.steps&&t.steps.length>50&&r.push({id:"too-many-steps",message:"Workflow has many steps, consider breaking into smaller workflows",impact:"maintainability"})}validateStep(t,n,r,o,s){var i;if(t.id||r.push({id:`step-no-id-${Math.random()}`,stepId:t.id,message:"Step must have an ID",severity:"error",fixable:!0}),!t.type){r.push({id:`step-no-type-${t.id}`,stepId:t.id,message:"Step must have a type",severity:"error",fixable:!1});return}switch(t.type){case"action":this.validateActionStep(t,r,o,s);break;case"script":this.validateScriptStep(t,r,o);break;case"switch":this.validateSwitchStep(t,n,r,o);break;case"for":this.validateForStep(t,r,o);break}(i=t.config)!=null&&i.output_key&&(/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(t.config.output_key)||r.push({id:`invalid-output-key-${t.id}`,stepId:t.id,field:"output_key",message:"Output key must be a valid variable name",severity:"error",fixable:!0}),n.steps.filter(a=>{var c;return a.id!==t.id&&((c=a.config)==null?void 0:c.output_key)===t.config.output_key}).length>0&&r.push({id:`duplicate-output-key-${t.id}`,stepId:t.id,field:"output_key",message:`Output key '${t.config.output_key}' is used by another step`,severity:"error",fixable:!0}))}validateActionStep(t,n,r,o){var c,u;const s=(c=t.config)==null?void 0:c.action_name;if(!s){n.push({id:`action-no-name-${t.id}`,stepId:t.id,field:"action_name",message:"Action step must specify an action name",severity:"error",fixable:!1});return}const i=gA[s];if(!i){r.push({id:`unknown-action-${t.id}`,stepId:t.id,message:`Unknown action '${s}'. This may be a custom action.`,impact:"best-practice"});return}const l=((u=t.config)==null?void 0:u.input_args)||{};i.required.filter(f=>!l[f]).forEach(f=>{n.push({id:`missing-param-${t.id}-${f}`,stepId:t.id,field:"input_args",message:`Required parameter '${f}' is missing for action '${s}'`,severity:"error",fixable:!0})}),Object.entries(l).forEach(([f,m])=>{this.validateParameterValue(t.id,f,m,n,r)}),s.includes("notification")&&!l.user_id&&!l.user_record_id&&o.push({id:`suggest-user-lookup-${t.id}`,stepId:t.id,message:"Consider adding a user lookup step before sending notifications",action:"Add user lookup step"})}validateParameterValue(t,n,r,o,s){typeof r=="string"&&(r.includes("$")&&this.validateDSLExpressions(t,n,r,o,s),r.includes("data.")&&this.validateDataReferences(t,n,r,s))}validateDSLExpressions(t,n,r,o,s){const i=/\$([A-Z_]+)\s*\(/g;let l;for(;(l=i.exec(r))!==null;){const a=`$${l[1]}`,c=yA[a];if(!c)o.push({id:`unknown-dsl-${t}-${n}`,stepId:t,field:n,message:`Unknown DSL function '${a}'`,severity:"error",fixable:!1});else{const u=r.match(new RegExp(`\\${a}\\s*\\(([^)]*)\\)`));if(u){const f=u[1].split(",").filter(m=>m.trim()).length;f<c.minArgs&&o.push({id:`dsl-too-few-args-${t}-${n}`,stepId:t,field:n,message:`${a} requires at least ${c.minArgs} arguments`,severity:"error",fixable:!1}),c.maxArgs!==1/0&&f>c.maxArgs&&o.push({id:`dsl-too-many-args-${t}-${n}`,stepId:t,field:n,message:`${a} accepts at most ${c.maxArgs} arguments`,severity:"error",fixable:!1})}}}}validateDataReferences(t,n,r,o){(r.match(/data\.[a-zA-Z_][a-zA-Z0-9_.]*\b/g)||[]).forEach(i=>{i.split(".").length>4&&o.push({id:`deep-data-ref-${t}-${n}`,stepId:t,message:`Deep data reference '${i}' may be fragile`,impact:"maintainability"})})}validateScriptStep(t,n,r){var s;const o=(s=t.config)==null?void 0:s.code;if(!(o!=null&&o.trim())){n.push({id:`script-no-code-${t.id}`,stepId:t.id,field:"code",message:"Script step must contain code",severity:"error",fixable:!1});return}o.includes("return")||r.push({id:`script-no-return-${t.id}`,stepId:t.id,message:"Script should typically return a value",impact:"best-practice"})}validateSwitchStep(t,n,r,o){var i;const s=(i=t.config)==null?void 0:i.condition;s!=null&&s.trim()||r.push({id:`switch-no-condition-${t.id}`,stepId:t.id,field:"condition",message:"Switch step must have a condition",severity:"error",fixable:!1})}validateForStep(t,n,r){const o=t.config;o!=null&&o.in||n.push({id:`for-no-array-${t.id}`,stepId:t.id,field:"in",message:"For loop must specify an array to iterate over",severity:"error",fixable:!1}),o!=null&&o.each}validateDataFlow(t,n,r,o){const s=new Set(["data"]);t.steps.forEach(i=>{var c;(JSON.stringify(i.config).match(/[a-zA-Z_][a-zA-Z0-9_]*(?=\.)/g)||[]).forEach(u=>{u!=="data"&&!s.has(u)&&n.push({id:`undefined-reference-${i.id}-${u}`,stepId:i.id,message:`Reference to undefined variable '${u}'`,severity:"error",fixable:!1})}),(c=i.config)!=null&&c.output_key&&s.add(i.config.output_key)})}checkBestPractices(t,n,r){!t.steps.some(l=>l.type==="try_catch")&&t.steps.some(l=>l.type==="action")&&r.push({id:"add-error-handling",message:"Consider adding error handling for API calls",action:"Add try/catch blocks"}),t.steps.length>20&&r.push({id:"break-up-workflow",message:"Consider breaking this workflow into smaller, reusable components",action:"Refactor workflow"});const s=new Set(t.steps.map(l=>{var a;return(a=l.config)==null?void 0:a.output_key}).filter(Boolean)),i=new Set;t.steps.forEach(l=>{const a=JSON.stringify(l.config);s.forEach(c=>{a.includes(c)&&i.add(c)})}),s.forEach(l=>{l&&!i.has(l)&&n.push({id:`unused-output-${l}`,message:`Output '${l}' is never used`,impact:"maintainability"})})}}const vA=new xA,wA=({data:e})=>{var a,c,u,f;const{step:t,isSelected:n,hasErrors:r,hasWarnings:o,onSelect:s}=e,i=m=>{switch(m){case"action":return d.jsx(rl,{className:"h-4 w-4"});case"script":return d.jsx(wj,{className:"h-4 w-4"});case"switch":return d.jsx(bj,{className:"h-4 w-4"});case"for":return d.jsx(Aj,{className:"h-4 w-4"});case"parallel":return d.jsx(Bu,{className:"h-4 w-4"});case"try_catch":return d.jsx(wa,{className:"h-4 w-4"});default:return d.jsx(Bu,{className:"h-4 w-4"})}},l=m=>{switch(m){case"action":return"bg-blue-50 border-blue-200 text-blue-900";case"script":return"bg-yellow-50 border-yellow-200 text-yellow-900";case"switch":return"bg-purple-50 border-purple-200 text-purple-900";case"for":return"bg-green-50 border-green-200 text-green-900";case"parallel":return"bg-indigo-50 border-indigo-200 text-indigo-900";case"try_catch":return"bg-red-50 border-red-200 text-red-900";default:return"bg-gray-50 border-gray-200 text-gray-900"}};return d.jsxs("div",{onClick:()=>s(t.id),className:Ee("px-4 py-3 rounded-lg border-2 cursor-pointer transition-all min-w-[200px]",l(t.type),n?"ring-2 ring-primary-500 ring-offset-2":"",r?"border-red-400":o?"border-yellow-400":""),children:[d.jsx(No,{type:"target",position:G.Top,className:"w-3 h-3"}),d.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[i(t.type),d.jsx("span",{className:"font-medium text-sm",children:t.type==="action"?(c=(a=t.config)==null?void 0:a.action_name)==null?void 0:c.split(".").pop():t.type}),r&&d.jsx(Th,{className:"h-4 w-4 text-red-500"}),o&&d.jsx(Th,{className:"h-4 w-4 text-yellow-500"})]}),((u=t.config)==null?void 0:u.output_key)&&d.jsxs("div",{className:"text-xs bg-white bg-opacity-50 px-2 py-1 rounded",children:["→ ",t.config.output_key]}),t.type==="action"&&((f=t.config)==null?void 0:f.action_name)&&d.jsx("div",{className:"text-xs text-gray-600 mt-1 truncate",children:t.config.action_name}),d.jsx(No,{type:"source",position:G.Bottom,className:"w-3 h-3"})]})},SA={stepNode:wA};function bA({workflow:e,onStepSelect:t,selectedStepId:n}){const[r,o,s]=VC([]),[i,l,a]=WC([]),[c,u]=b.useState(null),[f]=b.useState(null);b.useEffect(()=>{if(!(e!=null&&e.steps))return;const x=vA.validateWorkflow(e);u(x);const y=e.steps.map((p,w)=>{const S=x.errors.filter(E=>E.stepId===p.id),N=x.warnings.filter(E=>E.stepId===p.id);return{id:p.id,type:"stepNode",position:p.position||{x:100+w%3*250,y:100+Math.floor(w/3)*150},data:{step:p,isSelected:n===p.id,hasErrors:S.length>0,hasWarnings:N.length>0,onSelect:t||(()=>{})}}}),v=[],h=new Map;e.steps.forEach(p=>{var w;(w=p.config)!=null&&w.output_key&&h.set(p.config.output_key,p.id)}),e.steps.forEach((p,w)=>{const S=JSON.stringify(p.config||{});if(h.forEach((N,E)=>{S.includes(E)&&N!==p.id&&v.push({id:`${N}-${p.id}-${E}`,source:N,target:p.id,type:"smoothstep",animated:f===E,style:{stroke:f===E?"#3b82f6":"#6b7280",strokeWidth:f===E?3:2},label:E,labelStyle:{fontSize:12,fontWeight:500,fill:f===E?"#3b82f6":"#6b7280"}})}),w>0&&!v.some(N=>N.target===p.id)){const N=e.steps[w-1];v.push({id:`seq-${N.id}-${p.id}`,source:N.id,target:p.id,type:"smoothstep",style:{stroke:"#d1d5db",strokeWidth:1,strokeDasharray:"5,5"}})}}),o(y),l(v)},[e,n,f]);const m=b.useCallback(x=>l(y=>ky(x,y)),[l]),g=b.useCallback((x,y)=>{t&&t(y.id)},[t]);return d.jsxs("div",{className:"h-full w-full relative",children:[d.jsxs(sx,{nodes:r,edges:i,onNodesChange:s,onEdgesChange:a,onConnect:m,onNodeClick:g,nodeTypes:SA,fitView:!0,attributionPosition:"bottom-left",children:[d.jsx(aj,{}),d.jsx(tj,{nodeStrokeColor:"#374151",nodeColor:"#f3f4f6",nodeBorderRadius:8}),d.jsx(hj,{gap:20,size:1})]}),c&&(c.errors.length>0||c.warnings.length>0)&&d.jsxs("div",{className:"absolute top-4 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-h-96 overflow-y-auto",children:[d.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Validation Results"}),c.errors.length>0&&d.jsxs("div",{className:"mb-4",children:[d.jsx("h4",{className:"text-sm font-medium text-red-700 mb-2",children:"Errors"}),d.jsxs("div",{className:"space-y-2",children:[c.errors.slice(0,5).map(x=>d.jsxs("div",{className:"text-xs bg-red-50 border border-red-200 rounded p-2",children:[d.jsx("div",{className:"font-medium text-red-800",children:x.message}),x.stepId&&d.jsxs("div",{className:"text-red-600 mt-1",children:["Step: ",x.stepId]})]},x.id)),c.errors.length>5&&d.jsxs("div",{className:"text-xs text-gray-500",children:["+",c.errors.length-5," more errors"]})]})]}),c.warnings.length>0&&d.jsxs("div",{className:"mb-4",children:[d.jsx("h4",{className:"text-sm font-medium text-yellow-700 mb-2",children:"Warnings"}),d.jsxs("div",{className:"space-y-2",children:[c.warnings.slice(0,3).map(x=>d.jsxs("div",{className:"text-xs bg-yellow-50 border border-yellow-200 rounded p-2",children:[d.jsx("div",{className:"font-medium text-yellow-800",children:x.message}),d.jsxs("div",{className:"text-yellow-600 mt-1",children:["Impact: ",x.impact]})]},x.id)),c.warnings.length>3&&d.jsxs("div",{className:"text-xs text-gray-500",children:["+",c.warnings.length-3," more warnings"]})]})]}),c.suggestions.length>0&&d.jsxs("div",{children:[d.jsx("h4",{className:"text-sm font-medium text-blue-700 mb-2",children:"Suggestions"}),d.jsx("div",{className:"space-y-2",children:c.suggestions.slice(0,3).map(x=>d.jsxs("div",{className:"text-xs bg-blue-50 border border-blue-200 rounded p-2",children:[d.jsx("div",{className:"font-medium text-blue-800",children:x.message}),d.jsx("button",{className:"text-blue-600 hover:text-blue-800 mt-1 text-xs underline",children:x.action})]},x.id))})]})]}),d.jsxs("div",{className:"absolute bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3",children:[d.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Data Flow"}),d.jsxs("div",{className:"space-y-1 text-xs",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-4 h-0.5 bg-gray-400"}),d.jsx("span",{children:"Data dependency"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-4 h-0.5 bg-gray-300 border-dashed border-t"}),d.jsx("span",{children:"Sequential flow"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-4 h-0.5 bg-blue-500"}),d.jsx("span",{children:"Highlighted flow"})]})]})]})]})}function NA({workflow:e,onClose:t}){const[n,r]=b.useState([{id:"default",name:"Default Test",description:"Basic test scenario",inputData:{}}]),[o,s]=b.useState("default"),[i,l]=b.useState(!1),[a,c]=b.useState(!1),u=n.find(h=>h.id===o),f=h=>{if(u)try{const p=JSON.parse(h);r(w=>w.map(S=>S.id===o?{...S,inputData:p}:S))}catch(p){console.warn("Invalid JSON input:",p)}},m=async h=>{const p=Date.now();await new Promise(N=>setTimeout(N,1e3+Math.random()*2e3));const w=Date.now()-p;if(Math.random()>.2){const N={...h};return e.steps.forEach(E=>{var _,C,T,P;if((_=E.config)!=null&&_.output_key)switch(E.type){case"action":(C=E.config.action_name)!=null&&C.includes("get_user")?N[E.config.output_key]={user_id:"user_123",email:h.email||"<EMAIL>",display_name:"Test User",department:"Engineering"}:(T=E.config.action_name)!=null&&T.includes("notification")?N[E.config.output_key]={success:!0,message_id:"msg_"+Math.random().toString(36).substr(2,9)}:(P=E.config.action_name)!=null&&P.includes("approval")&&(N[E.config.output_key]={request_id:"req_"+Math.random().toString(36).substr(2,9),status:"pending",created_at:new Date().toISOString()});break;case"script":N[E.config.output_key]={result:"Script executed successfully",data:h};break;case"switch":N[E.config.output_key]={condition_result:Math.random()>.5,branch_taken:"true_branch"};break}}),{success:!0,output:N,executionTime:w,errors:[]}}else return{success:!1,output:null,executionTime:w,errors:["Simulated execution error","Network timeout"]}},g=async()=>{if(u){l(!0),c(!0);try{const h=await m(u.inputData);r(p=>p.map(w=>w.id===o?{...w,lastRun:{timestamp:new Date,...h}}:w))}catch(h){console.error("Test execution failed:",h)}finally{l(!1)}}},x=()=>{const h=prompt("Enter scenario name:");if(!h)return;const p={id:Date.now().toString(),name:h,description:"Custom test scenario",inputData:{}};r(w=>[...w,p]),s(p.id)},y=()=>{const h=JSON.stringify(n,null,2),p=new Blob([h],{type:"application/json"}),w=URL.createObjectURL(p),S=document.createElement("a");S.href=w,S.download=`${e.name}_test_scenarios.json`,S.click(),URL.revokeObjectURL(w)},v=h=>{var S;const p=(S=h.target.files)==null?void 0:S[0];if(!p)return;const w=new FileReader;w.onload=N=>{var E;try{const _=JSON.parse((E=N.target)==null?void 0:E.result);r(C=>[...C,..._])}catch{alert("Failed to import scenarios: Invalid JSON format")}},w.readAsText(p)};return d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden",children:[d.jsx("div",{className:"p-6 border-b border-gray-200",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsxs("h2",{className:"text-xl font-semibold text-gray-900",children:["Test Workflow: ",e.name]}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Simulate workflow execution with test data"})]}),d.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:"✕"})]})}),d.jsxs("div",{className:"flex h-[70vh]",children:[d.jsxs("div",{className:"w-1/2 p-6 border-r border-gray-200",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsx("h3",{className:"font-medium text-gray-900",children:"Test Scenarios"}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("button",{onClick:x,className:"btn btn-ghost btn-sm",title:"Save new scenario",children:d.jsx(fx,{className:"h-4 w-4"})}),d.jsxs("label",{className:"btn btn-ghost btn-sm cursor-pointer",title:"Import scenarios",children:[d.jsx(hx,{className:"h-4 w-4"}),d.jsx("input",{type:"file",accept:".json",onChange:v,className:"hidden"})]}),d.jsx("button",{onClick:y,className:"btn btn-ghost btn-sm",title:"Export scenarios",children:d.jsx(Sa,{className:"h-4 w-4"})})]})]}),d.jsx("div",{className:"mb-4",children:d.jsx("select",{value:o,onChange:h=>s(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:n.map(h=>d.jsx("option",{value:h.id,children:h.name},h.id))})}),u&&d.jsxs("div",{className:"space-y-4",children:[d.jsx(Dr,{label:"Input Data (JSON)",description:"Test data to pass to the workflow",value:JSON.stringify(u.inputData,null,2),onChange:f,expectedType:"object",className:"h-64"}),d.jsx("button",{onClick:g,disabled:i,className:"w-full btn btn-primary",children:i?d.jsxs(d.Fragment,{children:[d.jsx(Uu,{className:"h-4 w-4 mr-2 animate-spin"}),"Running Test..."]}):d.jsxs(d.Fragment,{children:[d.jsx(Jr,{className:"h-4 w-4 mr-2"}),"Run Test"]})})]})]}),d.jsxs("div",{className:"w-1/2 p-6",children:[d.jsx("h3",{className:"font-medium text-gray-900 mb-4",children:"Test Results"}),u!=null&&u.lastRun?d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:Ee("p-4 rounded-lg border",u.lastRun.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[u.lastRun.success?d.jsx(Vs,{className:"h-5 w-5 text-green-600"}):d.jsx(wa,{className:"h-5 w-5 text-red-600"}),d.jsx("span",{className:Ee("font-medium",u.lastRun.success?"text-green-900":"text-red-900"),children:u.lastRun.success?"Test Passed":"Test Failed"})]}),d.jsxs("div",{className:"text-sm text-gray-600",children:[d.jsxs("p",{children:["Executed: ",u.lastRun.timestamp.toLocaleString()]}),d.jsxs("p",{children:["Duration: ",u.lastRun.executionTime,"ms"]})]})]}),u.lastRun.errors&&u.lastRun.errors.length>0&&d.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[d.jsx("h4",{className:"font-medium text-red-900 mb-2",children:"Errors"}),d.jsx("ul",{className:"text-sm text-red-700 space-y-1",children:u.lastRun.errors.map((h,p)=>d.jsxs("li",{children:["• ",h]},p))})]}),u.lastRun.output&&d.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[d.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Output"}),d.jsx("pre",{className:"text-sm text-gray-700 overflow-auto max-h-64",children:JSON.stringify(u.lastRun.output,null,2)})]})]}):a&&i?d.jsx("div",{className:"flex items-center justify-center h-32",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Uu,{className:"h-8 w-8 mx-auto mb-2 animate-spin text-primary-600"}),d.jsx("p",{className:"text-gray-600",children:"Running test..."})]})}):d.jsx("div",{className:"flex items-center justify-center h-32 text-gray-500",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Jr,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),d.jsx("p",{children:"Run a test to see results"})]})})]})]}),d.jsx("div",{className:"p-6 border-t border-gray-200 bg-gray-50",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("div",{className:"text-sm text-gray-600",children:"Test your workflow with different input scenarios to ensure it works as expected."}),d.jsx("button",{onClick:t,className:"btn btn-secondary",children:"Close"})]})})]})})}function rm(){const{workflowId:e}=FS(),{currentWorkflow:t,loadWorkflow:n,createWorkflow:r,saveWorkflow:o,generateYaml:s,validateWorkflow:i,generatedYaml:l,validationResult:a,yamlMetadata:c,isGenerating:u,yamlPreviewVisible:f,toggleYamlPreview:m}=Wt(),[g,x]=b.useState(!1),[y,v]=b.useState(!0),[h,p]=b.useState(!1);b.useEffect(()=>{e?n(e):t||r("Untitled Workflow","A new Moveworks Compound Action workflow")},[e,t,n,r]);const w=()=>{o(),Oe.success("Workflow saved successfully")},S=async()=>{if(t)try{await s({include_comments:!0}),Oe.success("YAML generated successfully")}catch(C){Oe.error("Failed to generate YAML"),console.error("YAML generation error:",C)}},N=async()=>{if(t){x(!0);try{await i(),Oe.success("Workflow validated successfully")}catch(C){Oe.error("Failed to validate workflow"),console.error("Validation error:",C)}finally{x(!1)}}},E=async()=>{if(l)try{await p3(l),Oe.success("YAML copied to clipboard")}catch{Oe.error("Failed to copy YAML")}},_=()=>{if(!l||!t)return;const C=`${t.name.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.yaml`;mf(l,C,"text/yaml"),Oe.success("YAML downloaded successfully")};return t?d.jsxs("div",{className:"h-screen flex flex-col",children:[d.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-secondary-200 bg-white",children:[d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-xl font-semibold text-secondary-900",children:t.name}),t.description&&d.jsx("p",{className:"text-sm text-secondary-600",children:t.description})]}),a&&d.jsx("div",{className:"flex items-center space-x-2",children:a.isValid?d.jsxs("div",{className:"flex items-center text-success-600",children:[d.jsx(Vs,{className:"h-4 w-4 mr-1"}),d.jsx("span",{className:"text-sm",children:"Valid"})]}):d.jsxs("div",{className:"flex items-center text-error-600",children:[d.jsx(wa,{className:"h-4 w-4 mr-1"}),d.jsxs("span",{className:"text-sm",children:[a.errors.length," error(s)"]})]})})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsxs("button",{onClick:N,disabled:g,className:"btn btn-outline btn-sm",children:[g?d.jsx(yc,{className:"h-4 w-4 mr-2 animate-spin"}):d.jsx(Vs,{className:"h-4 w-4 mr-2"}),"Validate"]}),d.jsxs("button",{onClick:()=>p(!0),disabled:t.steps.length===0,className:"btn btn-outline btn-sm",title:"Test workflow",children:[d.jsx(Jr,{className:"h-4 w-4 mr-2"}),"Test"]}),d.jsxs("button",{onClick:S,disabled:u,className:"btn btn-primary btn-sm",children:[u?d.jsx(yc,{className:"h-4 w-4 mr-2 animate-spin"}):d.jsx(Jr,{className:"h-4 w-4 mr-2"}),"Generate YAML"]}),d.jsx("button",{onClick:m,className:"btn btn-ghost btn-sm",children:f?d.jsx(dx,{className:"h-4 w-4"}):d.jsx(Hu,{className:"h-4 w-4"})}),d.jsxs("button",{onClick:w,className:"btn btn-secondary btn-sm",children:[d.jsx(fx,{className:"h-4 w-4 mr-2"}),"Save"]})]})]}),d.jsxs("div",{className:"flex-1 flex",children:[d.jsx(mA,{className:"w-80",showVisualization:y,onToggleVisualization:()=>v(!y)}),d.jsx("div",{className:"flex-1 bg-secondary-50",children:y&&t.steps.length>0?d.jsx(bA,{workflow:t,onStepSelect:C=>{const{selectStep:T}=Wt.getState();T(C)},selectedStepId:Wt.getState().selectedStepId||void 0}):d.jsx("div",{className:"h-full p-6",children:d.jsx("div",{className:"h-full bg-white rounded-lg border border-secondary-200 flex items-center justify-center",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-6xl mb-4",children:"⚡"}),d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-2",children:"Workflow Builder"}),d.jsx("p",{className:"text-secondary-600 mb-4",children:t.steps.length===0?"Add steps using the sidebar to get started.":"Toggle visualization to see your workflow structure."}),d.jsxs("div",{className:"space-y-2 text-sm text-secondary-500",children:[d.jsxs("p",{children:["Current workflow has ",t.steps.length," steps"]}),c&&d.jsxs("p",{children:["Complexity score: ",c.complexity_score]})]}),t.steps.length>0&&d.jsxs("div",{className:"mt-6 space-y-3",children:[d.jsxs("button",{onClick:()=>v(!0),className:"btn btn-outline mr-3",children:[d.jsx(Hu,{className:"h-4 w-4 mr-2"}),"Show Visualization"]}),d.jsxs("button",{onClick:S,disabled:u,className:"btn btn-primary",children:[u?d.jsx(yc,{className:"h-4 w-4 mr-2 animate-spin"}):d.jsx(Jr,{className:"h-4 w-4 mr-2"}),"Generate YAML"]})]})]})})})}),f&&d.jsxs("div",{className:"w-1/2 border-l border-secondary-200 bg-white flex flex-col",children:[d.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-secondary-200",children:[d.jsx("h3",{className:"font-medium text-secondary-900",children:"Generated YAML"}),l&&d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("button",{onClick:E,className:"btn btn-ghost btn-sm",title:"Copy to clipboard",children:d.jsx(Sj,{className:"h-4 w-4"})}),d.jsx("button",{onClick:_,className:"btn btn-ghost btn-sm",title:"Download YAML",children:d.jsx(Sa,{className:"h-4 w-4"})})]})]}),d.jsx("div",{className:"flex-1 relative",children:l?d.jsx(pA,{height:"100%",defaultLanguage:"yaml",value:l,options:{readOnly:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,lineNumbers:"on",wordWrap:"on"},theme:"vs"}):d.jsx("div",{className:"flex items-center justify-center h-full text-secondary-500",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Jr,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),d.jsx("p",{children:'Click "Generate YAML" to see the output'})]})})}),a&&!a.isValid&&d.jsxs("div",{className:"border-t border-secondary-200 p-4 bg-error-50",children:[d.jsx("h4",{className:"font-medium text-error-900 mb-2",children:"Validation Errors"}),d.jsxs("div",{className:"space-y-1",children:[a.errors.slice(0,5).map((C,T)=>d.jsxs("div",{className:"text-sm text-error-700",children:[d.jsxs("span",{className:"font-medium",children:[C.type,":"]})," ",C.message,C.path&&d.jsxs("span",{className:"text-error-600",children:[" (",C.path,")"]})]},T)),a.errors.length>5&&d.jsxs("div",{className:"text-sm text-error-600",children:["... and ",a.errors.length-5," more errors"]})]})]}),c&&d.jsxs("div",{className:"border-t border-secondary-200 p-4 bg-secondary-50",children:[d.jsx("h4",{className:"font-medium text-secondary-900 mb-2",children:"Metadata"}),d.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[d.jsxs("div",{children:[d.jsx("span",{className:"text-secondary-600",children:"Steps:"}),d.jsx("span",{className:"ml-2 font-medium",children:c.step_count})]}),d.jsxs("div",{children:[d.jsx("span",{className:"text-secondary-600",children:"Complexity:"}),d.jsx("span",{className:"ml-2 font-medium",children:c.complexity_score})]}),c.estimated_execution_time&&d.jsxs("div",{className:"col-span-2",children:[d.jsx("span",{className:"text-secondary-600",children:"Est. execution time:"}),d.jsxs("span",{className:"ml-2 font-medium",children:[c.estimated_execution_time,"ms"]})]})]})]})]})]}),h&&d.jsx(NA,{workflow:t,onClose:()=>p(!1)})]}):d.jsx("div",{className:"flex items-center justify-center min-h-screen",children:d.jsx("div",{className:"loading-spinner h-8 w-8"})})}function EA(){const{builtinActions:e,dslFunctions:t,loadBuiltinActions:n,loadDSLFunctions:r}=Wt(),[o,s]=b.useState(""),[i,l]=b.useState("all"),[a,c]=b.useState("actions");b.useEffect(()=>{n(),r()},[n,r]);const u=e.filter(g=>g.name.toLowerCase().includes(o.toLowerCase())||g.description.toLowerCase().includes(o.toLowerCase())),f=t.filter(g=>g.name.toLowerCase().includes(o.toLowerCase())||g.description.toLowerCase().includes(o.toLowerCase())||i==="all"||g.category===i),m=["all",...new Set(t.map(g=>g.category))];return d.jsxs("div",{className:"p-6 max-w-6xl mx-auto",children:[d.jsxs("div",{className:"mb-8",children:[d.jsx("h1",{className:"text-3xl font-bold text-secondary-900 mb-2",children:"Documentation"}),d.jsx("p",{className:"text-secondary-600",children:"Comprehensive guide to Moveworks Compound Actions, built-in actions, and DSL functions."})]}),d.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[d.jsxs("div",{className:"relative flex-1",children:[d.jsx(Tj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400"}),d.jsx("input",{type:"text",placeholder:"Search documentation...",value:o,onChange:g=>s(g.target.value),className:"input pl-10 w-full"})]}),a==="dsl"&&d.jsx("select",{value:i,onChange:g=>l(g.target.value),className:"input w-auto",children:m.map(g=>d.jsx("option",{value:g,children:g==="all"?"All Categories":g.charAt(0).toUpperCase()+g.slice(1)},g))})]}),d.jsx("div",{className:"border-b border-secondary-200 mb-6",children:d.jsxs("nav",{className:"-mb-px flex space-x-8",children:[d.jsxs("button",{onClick:()=>c("actions"),className:Ee("py-2 px-1 border-b-2 font-medium text-sm",a==="actions"?"border-primary-500 text-primary-600":"border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"),children:["Built-in Actions (",e.length,")"]}),d.jsxs("button",{onClick:()=>c("dsl"),className:Ee("py-2 px-1 border-b-2 font-medium text-sm",a==="dsl"?"border-primary-500 text-primary-600":"border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"),children:["DSL Functions (",t.length,")"]}),d.jsx("button",{onClick:()=>c("guide"),className:Ee("py-2 px-1 border-b-2 font-medium text-sm",a==="guide"?"border-primary-500 text-primary-600":"border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"),children:"User Guide"})]})}),a==="actions"&&d.jsx("div",{className:"space-y-4",children:u.map(g=>d.jsx("div",{className:"card",children:d.jsxs("div",{className:"card-content",children:[d.jsxs("div",{className:"flex items-start justify-between mb-3",children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900",children:g.name}),d.jsx("span",{className:"badge badge-default",children:"mw"})]}),d.jsx("p",{className:"text-secondary-600 mb-4",children:g.description}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"font-medium text-secondary-900 mb-2",children:"Input Arguments"}),d.jsx("div",{className:"space-y-2",children:g.input_args.map(x=>d.jsxs("div",{className:"flex items-start space-x-3 text-sm",children:[d.jsx("code",{className:"bg-secondary-100 px-2 py-1 rounded text-secondary-800 font-mono",children:x.name}),d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:"text-secondary-600",children:x.type}),x.required&&d.jsx("span",{className:"badge badge-error text-xs",children:"required"})]}),d.jsx("p",{className:"text-secondary-600 mt-1",children:x.description})]})]},x.name))})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"font-medium text-secondary-900 mb-2",children:"Output Structure"}),d.jsx("pre",{className:"bg-secondary-50 p-3 rounded text-sm overflow-x-auto",children:d.jsx("code",{children:JSON.stringify(g.output_structure,null,2)})})]})]})]})},g.name))}),a==="dsl"&&d.jsx("div",{className:"space-y-4",children:f.map(g=>d.jsx("div",{className:"card",children:d.jsxs("div",{className:"card-content",children:[d.jsxs("div",{className:"flex items-start justify-between mb-3",children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900",children:g.name}),d.jsx("span",{className:"badge badge-default",children:g.category})]}),d.jsx("p",{className:"text-secondary-600 mb-4",children:g.description}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"font-medium text-secondary-900 mb-2",children:"Syntax"}),d.jsx("code",{className:"block bg-secondary-100 p-3 rounded text-sm font-mono",children:g.syntax})]}),d.jsxs("div",{children:[d.jsx("h4",{className:"font-medium text-secondary-900 mb-2",children:"Example"}),d.jsx("code",{className:"block bg-secondary-50 p-3 rounded text-sm font-mono",children:g.example})]})]})]})},g.name))}),a==="guide"&&d.jsx("div",{className:"prose max-w-none",children:d.jsx("div",{className:"card",children:d.jsxs("div",{className:"card-content",children:[d.jsx("h2",{className:"text-2xl font-bold text-secondary-900 mb-4",children:"Getting Started with Moveworks Compound Actions"}),d.jsxs("div",{className:"space-y-6",children:[d.jsxs("section",{children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-3",children:"What are Compound Actions?"}),d.jsx("p",{className:"text-secondary-600 mb-4",children:"Moveworks Compound Actions are powerful workflow automation tools that allow you to chain multiple actions together, handle complex data transformations, and implement sophisticated business logic."})]}),d.jsxs("section",{children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-3",children:"Key Concepts"}),d.jsxs("ul",{className:"list-disc list-inside space-y-2 text-secondary-600",children:[d.jsxs("li",{children:[d.jsx("strong",{children:"Steps:"})," Individual actions or operations in your workflow"]}),d.jsxs("li",{children:[d.jsx("strong",{children:"Data Object:"})," Shared memory that stores input variables and step outputs"]}),d.jsxs("li",{children:[d.jsx("strong",{children:"Output Keys:"})," Unique identifiers for storing step results"]}),d.jsxs("li",{children:[d.jsx("strong",{children:"Input Args:"})," Parameters passed to each action"]}),d.jsxs("li",{children:[d.jsx("strong",{children:"Data References:"})," Ways to access data from previous steps"]})]})]}),d.jsxs("section",{children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-3",children:"Basic Workflow Structure"}),d.jsx("pre",{className:"bg-secondary-50 p-4 rounded text-sm overflow-x-auto",children:d.jsx("code",{children:`steps:
  - action:
      action_name: mw.get_user_by_email
      output_key: user_info
      input_args:
        email: data.user_email

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_result
      input_args:
        user_record_id: data.user_info.user_id
        message: "Hello, welcome to the system!"`})})]}),d.jsxs("section",{children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-3",children:"Best Practices"}),d.jsxs("ul",{className:"list-disc list-inside space-y-2 text-secondary-600",children:[d.jsx("li",{children:"Use descriptive output_key names for better readability"}),d.jsx("li",{children:"Validate your workflows before deployment"}),d.jsx("li",{children:"Handle errors gracefully with try-catch blocks"}),d.jsx("li",{children:"Keep workflows modular and focused on specific tasks"}),d.jsx("li",{children:"Document your workflows with clear descriptions"})]})]}),d.jsxs("section",{children:[d.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-3",children:"External Resources"}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("a",{href:"https://docs.moveworks.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-primary-600 hover:text-primary-700",children:[d.jsx(Ph,{className:"h-4 w-4 mr-2"}),"Official Moveworks Documentation"]}),d.jsxs("a",{href:"https://community.moveworks.com",target:"_blank",rel:"noopener noreferrer",className:"flex items-center text-primary-600 hover:text-primary-700",children:[d.jsx(Ph,{className:"h-4 w-4 mr-2"}),"Moveworks Community"]})]})]})]})]})})})]})}function kA(){const{workflows:e,deleteWorkflow:t}=Wt(),[n,r]=b.useState("json"),o=()=>{const l={version:"1.0.0",exported_at:new Date().toISOString(),workflows:e},a=JSON.stringify(l,null,2),c=`moveworks_workflows_${new Date().toISOString().split("T")[0]}.json`;mf(a,c,"application/json"),Oe.success("All workflows exported successfully")},s=l=>{var u;const a=(u=l.target.files)==null?void 0:u[0];if(!a)return;const c=new FileReader;c.onload=f=>{var m;try{const g=(m=f.target)==null?void 0:m.result,x=JSON.parse(g);if(!x.workflows||!Array.isArray(x.workflows))throw new Error("Invalid import format");Oe.success(`Ready to import ${x.workflows.length} workflows`),console.log("Import data:",x)}catch(g){Oe.error("Failed to import workflows. Please check the file format."),console.error("Import error:",g)}},c.readAsText(a),l.target.value=""},i=()=>{confirm("Are you sure you want to delete all workflows? This action cannot be undone.")&&(e.forEach(l=>t(l.id)),Oe.success("All workflows deleted"))};return d.jsxs("div",{className:"p-6 max-w-4xl mx-auto",children:[d.jsxs("div",{className:"mb-8",children:[d.jsx("h1",{className:"text-3xl font-bold text-secondary-900 mb-2",children:"Settings"}),d.jsx("p",{className:"text-secondary-600",children:"Manage your application preferences and workflow data."})]}),d.jsxs("div",{className:"space-y-8",children:[d.jsxs("section",{className:"card",children:[d.jsxs("div",{className:"card-header",children:[d.jsx("h2",{className:"card-title",children:"Data Management"}),d.jsx("p",{className:"card-description",children:"Export, import, and manage your workflow data."})]}),d.jsxs("div",{className:"card-content space-y-6",children:[d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Export Workflows"}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("select",{value:n,onChange:l=>r(l.target.value),className:"input w-auto",children:[d.jsx("option",{value:"json",children:"JSON Format"}),d.jsx("option",{value:"yaml",children:"YAML Format"})]}),d.jsxs("button",{onClick:o,className:"btn btn-primary btn-md",disabled:e.length===0,children:[d.jsx(Sa,{className:"h-4 w-4 mr-2"}),"Export All (",e.length,")"]})]}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Export all your workflows to a file for backup or sharing."})]}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Import Workflows"}),d.jsx("div",{className:"flex items-center space-x-4",children:d.jsxs("label",{className:"btn btn-outline btn-md cursor-pointer",children:[d.jsx(hx,{className:"h-4 w-4 mr-2"}),"Choose File",d.jsx("input",{type:"file",accept:".json",onChange:s,className:"hidden"})]})}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Import workflows from a previously exported JSON file."})]}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Clear All Data"}),d.jsxs("button",{onClick:i,className:"btn btn-outline btn-md text-error-600 border-error-300 hover:bg-error-50",disabled:e.length===0,children:[d.jsx(pf,{className:"h-4 w-4 mr-2"}),"Delete All Workflows"]}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Permanently delete all workflows. This action cannot be undone."})]})]})]}),d.jsxs("section",{className:"card",children:[d.jsxs("div",{className:"card-header",children:[d.jsx("h2",{className:"card-title",children:"Application Settings"}),d.jsx("p",{className:"card-description",children:"Configure your application preferences."})]}),d.jsxs("div",{className:"card-content space-y-6",children:[d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Theme"}),d.jsxs("select",{className:"input w-auto",defaultValue:"light",children:[d.jsx("option",{value:"light",children:"Light"}),d.jsx("option",{value:"dark",children:"Dark"}),d.jsx("option",{value:"system",children:"System"})]}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Choose your preferred color theme."})]}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Auto-save"}),d.jsxs("label",{className:"flex items-center space-x-3",children:[d.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded border-secondary-300 text-primary-600 focus:ring-primary-500"}),d.jsx("span",{className:"text-secondary-700",children:"Automatically save workflows while editing"})]}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Enable automatic saving to prevent data loss."})]}),d.jsxs("div",{children:[d.jsx("h3",{className:"text-lg font-medium text-secondary-900 mb-3",children:"Validation"}),d.jsxs("label",{className:"flex items-center space-x-3",children:[d.jsx("input",{type:"checkbox",defaultChecked:!0,className:"rounded border-secondary-300 text-primary-600 focus:ring-primary-500"}),d.jsx("span",{className:"text-secondary-700",children:"Show real-time validation errors"})]}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Display validation errors as you build workflows."})]})]})]}),d.jsxs("section",{className:"card",children:[d.jsxs("div",{className:"card-header",children:[d.jsx("h2",{className:"card-title",children:"API Configuration"}),d.jsx("p",{className:"card-description",children:"Configure connection to the backend API."})]}),d.jsxs("div",{className:"card-content space-y-6",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-secondary-700 mb-2",children:"API Base URL"}),d.jsx("input",{type:"url",defaultValue:"/api",className:"input w-full",placeholder:"https://api.example.com"}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Base URL for the Moveworks Assistant API."})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-secondary-700 mb-2",children:"Request Timeout (seconds)"}),d.jsx("input",{type:"number",defaultValue:30,min:5,max:300,className:"input w-auto"}),d.jsx("p",{className:"text-sm text-secondary-600 mt-2",children:"Maximum time to wait for API responses."})]}),d.jsxs("button",{className:"btn btn-outline btn-md",children:[d.jsx(Rj,{className:"h-4 w-4 mr-2"}),"Test Connection"]})]})]}),d.jsxs("section",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h2",{className:"card-title",children:"About"})}),d.jsxs("div",{className:"card-content",children:[d.jsxs("div",{className:"space-y-2 text-sm",children:[d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-secondary-600",children:"Version:"}),d.jsx("span",{className:"font-medium",children:"1.0.0"})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-secondary-600",children:"Build:"}),d.jsx("span",{className:"font-medium",children:"Phase 1 - Core Engine"})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{className:"text-secondary-600",children:"Last Updated:"}),d.jsx("span",{className:"font-medium",children:new Date().toLocaleDateString()})]})]}),d.jsx("div",{className:"mt-4 pt-4 border-t border-secondary-200",children:d.jsx("p",{className:"text-sm text-secondary-600",children:"AI-Powered Moveworks Compound Action Assistant - Simplifying workflow creation and YAML generation for Moveworks developers."})})]})]})]})]})}function _A(){return d.jsx(ff,{children:d.jsx(h3,{children:d.jsxs(nb,{children:[d.jsx(zr,{path:"/",element:d.jsx(lR,{})}),d.jsx(zr,{path:"/builder",element:d.jsx(rm,{})}),d.jsx(zr,{path:"/builder/:workflowId",element:d.jsx(rm,{})}),d.jsx(zr,{path:"/docs",element:d.jsx(EA,{})}),d.jsx(zr,{path:"/settings",element:d.jsx(kA,{})})]})})})}Cc.createRoot(document.getElementById("root")).render(d.jsx(I.StrictMode,{children:d.jsxs(cb,{children:[d.jsx(_A,{}),d.jsx(n2,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#22c55e",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})}));
//# sourceMappingURL=index-DQiy91Jy.js.map
