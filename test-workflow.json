{"id": "test-workflow-001", "name": "Test User Lookup Workflow", "description": "A simple test workflow to verify the system is working", "steps": [{"id": "step1", "type": "action", "position": {"x": 100, "y": 100}, "config": {"action_name": "mw.get_user_by_email", "output_key": "user_result", "input_args": {"email": "data.user_email"}}, "connections": []}, {"id": "step2", "type": "action", "position": {"x": 100, "y": 220}, "config": {"action_name": "mw.send_plaintext_chat_notification", "output_key": "notification_result", "input_args": {"user_id": "user_result.user_id", "message": "$CONCAT(\"Hello \", user_result.display_name, \"!\")"}}, "connections": []}], "created_at": "2025-05-29T23:21:00.000Z", "updated_at": "2025-05-29T23:21:00.000Z"}