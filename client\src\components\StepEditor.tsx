/**
 * Step Editor Component
 * Allows users to add and configure workflow steps
 */

import { useState } from 'react'
import { Plus, Settings, Trash2 } from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { WorkflowStep } from '@/types'
import { cn } from '@/lib/utils'

const STEP_TYPES = [
  { type: 'action', label: 'Action', icon: '⚡', description: 'Execute a Moveworks built-in action' },
  { type: 'script', label: 'Script', icon: '📝', description: 'Run custom APIthon code' },
  { type: 'switch', label: 'Switch', icon: '🔀', description: 'Conditional branching logic' },
  { type: 'for', label: 'For Loop', icon: '🔄', description: 'Iterate over a collection' },
  { type: 'parallel', label: 'Parallel', icon: '⚡', description: 'Execute steps in parallel' },
  { type: 'try_catch', label: 'Try/Catch', icon: '🛡️', description: 'Error handling' },
  { type: 'raise', label: 'Raise', icon: '⚠️', description: 'Throw an error' },
  { type: 'return', label: 'Return', icon: '↩️', description: 'Return a value' },
] as const

interface StepEditorProps {
  className?: string
}

export default function StepEditor({ className }: StepEditorProps) {
  const {
    currentWorkflow,
    selectedStepId,
    addStep,
    updateStep,
    deleteStep,
    selectStep,
  } = useWorkflowStore()

  const [showAddMenu, setShowAddMenu] = useState(false)

  const selectedStep = currentWorkflow?.steps.find(step => step.id === selectedStepId)

  const handleAddStep = (type: WorkflowStep['type']) => {
    const position = { x: 100, y: 100 + (currentWorkflow?.steps.length || 0) * 120 }
    addStep(type, position)
    setShowAddMenu(false)
  }

  const handleDeleteStep = (stepId: string) => {
    if (confirm('Are you sure you want to delete this step?')) {
      deleteStep(stepId)
    }
  }

  const handleStepConfigChange = (field: string, value: any) => {
    if (!selectedStepId) return

    updateStep(selectedStepId, {
      config: {
        ...selectedStep?.config,
        [field]: value
      }
    })
  }

  return (
    <div className={cn('bg-white border-r border-secondary-200 flex flex-col', className)}>
      {/* Header */}
      <div className="p-4 border-b border-secondary-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-secondary-900">Workflow Steps</h3>
          <div className="relative">
            <button
              onClick={() => setShowAddMenu(!showAddMenu)}
              className="btn btn-primary btn-sm"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Step
            </button>

            {/* Add Step Menu */}
            {showAddMenu && (
              <div className="absolute right-0 top-full mt-2 w-64 bg-white border border-secondary-200 rounded-lg shadow-lg z-50">
                <div className="p-2">
                  <div className="text-xs font-medium text-secondary-600 mb-2 px-2">
                    Choose Step Type
                  </div>
                  {STEP_TYPES.map((stepType) => (
                    <button
                      key={stepType.type}
                      onClick={() => handleAddStep(stepType.type)}
                      className="w-full text-left p-2 rounded hover:bg-secondary-50 transition-colors"
                    >
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">{stepType.icon}</span>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-secondary-900">
                            {stepType.label}
                          </div>
                          <div className="text-xs text-secondary-600">
                            {stepType.description}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Step List */}
      <div className="flex-1 overflow-y-auto">
        {currentWorkflow?.steps.length === 0 ? (
          <div className="p-4 text-center text-secondary-500">
            <div className="text-4xl mb-2">📝</div>
            <p className="text-sm">No steps yet</p>
            <p className="text-xs">Click "Add Step" to get started</p>
          </div>
        ) : (
          <div className="p-2 space-y-2">
            {currentWorkflow?.steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  'p-3 border rounded-lg cursor-pointer transition-colors',
                  selectedStepId === step.id
                    ? 'border-primary-300 bg-primary-50'
                    : 'border-secondary-200 hover:border-secondary-300'
                )}
                onClick={() => selectStep(step.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs font-medium text-secondary-500">
                      {index + 1}
                    </span>
                    <span className="text-lg">
                      {STEP_TYPES.find(t => t.type === step.type)?.icon || '❓'}
                    </span>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-secondary-900 text-sm">
                        {STEP_TYPES.find(t => t.type === step.type)?.label || step.type}
                      </div>
                      {step.config?.output_key && (
                        <div className="text-xs text-secondary-600">
                          → {step.config.output_key}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        selectStep(step.id)
                      }}
                      className="p-1 text-secondary-400 hover:text-secondary-600"
                      title="Configure"
                    >
                      <Settings className="h-3 w-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteStep(step.id)
                      }}
                      className="p-1 text-secondary-400 hover:text-error-600"
                      title="Delete"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Step Configuration Panel */}
      {selectedStep && (
        <div className="border-t border-secondary-200 p-4 bg-secondary-50">
          <h4 className="font-medium text-secondary-900 mb-3">
            Configure {STEP_TYPES.find(t => t.type === selectedStep.type)?.label}
          </h4>

          <div className="space-y-3">
            {/* Output Key */}
            <div>
              <label className="block text-xs font-medium text-secondary-700 mb-1">
                Output Key
              </label>
              <input
                type="text"
                value={selectedStep.config?.output_key || ''}
                onChange={(e) => handleStepConfigChange('output_key', e.target.value)}
                placeholder={`${selectedStep.type}_result`}
                className="w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
            </div>

            {/* Type-specific configuration */}
            {selectedStep.type === 'action' && (
              <div>
                <label className="block text-xs font-medium text-secondary-700 mb-1">
                  Action Name
                </label>
                <input
                  type="text"
                  value={selectedStep.config?.action_name || ''}
                  onChange={(e) => handleStepConfigChange('action_name', e.target.value)}
                  placeholder="mw.get_user_by_email"
                  className="w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
            )}

            {selectedStep.type === 'script' && (
              <div>
                <label className="block text-xs font-medium text-secondary-700 mb-1">
                  APIthon Code
                </label>
                <textarea
                  value={selectedStep.config?.code || ''}
                  onChange={(e) => handleStepConfigChange('code', e.target.value)}
                  placeholder="# APIthon script&#10;return {'result': 'success'}"
                  rows={4}
                  className="w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 font-mono"
                />
              </div>
            )}

            {selectedStep.type === 'switch' && (
              <div>
                <label className="block text-xs font-medium text-secondary-700 mb-1">
                  Condition
                </label>
                <input
                  type="text"
                  value={selectedStep.config?.condition || ''}
                  onChange={(e) => handleStepConfigChange('condition', e.target.value)}
                  placeholder="data.amount > 1000"
                  className="w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
            )}

            <div className="text-xs text-secondary-500">
              More configuration options will be available in the full editor.
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close menu */}
      {showAddMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowAddMenu(false)}
        />
      )}
    </div>
  )
}
