Interactive Project Plan: AI-Powered Moveworks Compound Action Assistant
Project Start Date: May 29, 2025

Guiding Document: "Generating Verified Moveworks Compound Action YAML for AI Application Development.docx" (Source of Truth Document)

Architectural Leaning (Based on prior discussions): Prioritize Python-centric solutions. For UI, consider Python GUI toolkits (e.g., PySide6) or Server-Side Rendered web interfaces with minimal JavaScript to align with preferences for easier creation.

Phase 1: Core Engine & Basic YAML Generation (MVP Foundation)
Objective: Establish the foundational YAML generation and validation logic for simple, sequential action and script steps. Create the backend engine that forms the heart of the application.

Knowledge Base Focus (Source of Truth Document):

Sections 1.3 (Overview of Compound Actions) [cite: 11, 12, 13, 14, 15, 16, 17]

Section 2.1 (The steps Key) [cite: 18, 19]

Section 2.2 (Defining and Accessing Input Variables) [cite: 22]

Section 2.3 (The data Object) [cite: 23]

Section 4.2 (APIthon script Actions - basic structure) [cite: 35]

Section 8.1 (Critical Syntactical Rules) [cite: 77]

Section 11.1 (YAML Syntax for action, script) [cite: 93]

Section 11.3 (APIthon Constraints - basic awareness) [cite: 97]

Next Steps & Action Items:

Setup Project Environment:

Action: Initialize a Python project.

Decision: Choose a primary Python version (e.g., 3.10+).

Action: Set up version control (e.g., Git).

🤖 AI Execution Guidance:

Prompt 1.1: "AI, provide the sequence of shell commands a developer would use to:

Create a project directory named moveworks_yaml_assistant.

Navigate into this directory.

Initialize a Python virtual environment (e.g., using python -m venv .venv).

Activate the virtual environment (provide commands for both Unix-like systems and Windows).

Initialize a Git repository in this directory."

Stop: Await human execution of these commands.

Prompt 1.2: "AI, generate a standard Python .gitignore file content, including common patterns for virtual environments, IDE files, and Python cache files."

Stop: Await human creation of .gitignore with this content. Phase 1, Step 1 complete. Start next prompt for Phase 1, Step 2.

Develop Core Data Structures:

Action: Design Python classes/data structures to internally represent:

A Compound Action workflow.

An individual action step (attributes: action_name, output_key, input_args dictionary, progress_updates, delay_config).

An individual script step (attributes: code, output_key, input_args dictionary).

The data object context (to track input variables and output_keys).

Reference: Sections 2.1, 2.3, 11.1 of the Source of Truth Document[cite: 18, 19, 23, 93].

🤖 AI Execution Guidance:

Prompt 2.1: "AI, create a Python file named core_structures.py. Based on Sections 2.1, 2.3, and 11.1 (for action and script attributes) of the Source of Truth Document, define the following Python classes using dataclasses or Pydantic:

ActionStep: Attributes for action_name (str), output_key (str), input_args (dict, optional), progress_updates (dict, optional, with on_pending and on_complete keys), delay_config (dict, optional, with keys like milliseconds, seconds).

ScriptStep: Attributes for code (str), output_key (str), input_args (dict, optional).

Workflow: Attribute steps (a list that can contain ActionStep or ScriptStep instances or other future step types).

DataContext: Placeholder class for now (to be detailed in Phase 1, Step 4).
Ensure type hints are used. For optional fields, use Optional and provide default values (e.g., None or empty dicts)."

Stop: Await human review of core_structures.py.

Next Prompt (if changes needed): "AI, modify the ActionStep class in core_structures.py to ensure input_args defaults to an empty dictionary if not provided." (or similar refinement).

Next Prompt (if OK): "Proceed to Phase 1, Step 3: Implement Basic YAML Generation Module."

Implement Basic YAML Generation Module:

Action: Create a Python module responsible for taking the internal workflow representation (a sequence of action and script steps) and generating a syntactically correct YAML string.

Requirement: Must correctly implement the steps: list[cite: 18, 19].

Requirement: Handle basic action and script structures as defined in Section 11.1 of the Source of Truth Document[cite: 93].

Requirement: Ensure proper indentation and YAML formatting[cite: 77].

Tooling: Select a Python YAML library (e.g., PyYAML or ruamel.yaml for more control over formatting).

🤖 AI Execution Guidance:

Prompt 3.1: "AI, create a Python file named yaml_generator.py. This module will use the PyYAML library (add it to a requirements.txt file). Implement a function workflow_to_yaml_dict(workflow: Workflow) -> dict that converts a Workflow instance (from core_structures.py) into a Python dictionary suitable for PyYAML serialization. This dictionary should represent the steps list. For each ActionStep, include action_name, output_key, and input_args (if not empty). For each ScriptStep, include code, output_key, and input_args (if not empty). Ensure progress_updates and delay_config are included for ActionStep if present and not empty. Filter out optional fields that are None or empty from the final dictionary representation of each step to keep YAML clean."

Stop: Await human review of the workflow_to_yaml_dict function.

Prompt 3.2: "AI, in yaml_generator.py, add a function generate_yaml_string(workflow: Workflow) -> str that uses workflow_to_yaml_dict and PyYAML (yaml.dump) to produce a YAML string. Ensure proper indentation (2 spaces) and that block scalars are used for multi-line APIthon code if appropriate. Provide an example usage creating a simple Workflow with one ActionStep and one ScriptStep and printing its YAML string."

Stop: Await human review and testing of the YAML output. Phase 1, Step 3 complete. Start next prompt for Phase 1, Step 4.

Implement data Object Context Management:

Action: Develop logic to simulate the data object.

Functionality:

Allow registration of CA input variables (Section 2.2 [cite: 22]).

Track output_keys from generated steps and make them available for subsequent steps (Section 2.3 [cite: 23]).

Handle the _ for unused output_keys[cite: 23].

🤖 AI Execution Guidance:

Prompt 4.1: "AI, update the DataContext class in core_structures.py. Implement the following:

An __init__ method that can optionally accept a dictionary of initial CA input variables. Store these internally.

A method add_step_output(self, output_key: str, value: any): If output_key is not '_', store the value associated with output_key.

A method get_data_value(self, path_string: str) -> any: This method should parse path_string (e.g., 'input_var' or 'step_output.nested_key'). It should first check initial inputs, then step outputs. Implement basic dot notation access for nested dictionaries. Raise a custom DataPathNotFound exception if the path is invalid.

A method is_path_available(self, path_string: str) -> bool.
Include basic docstrings for these methods."

Stop: Await human review of the DataContext class.

Prompt 4.2 (Optional): "AI, write a few PyTest unit tests for the DataContext class, covering initialization with inputs, adding step outputs (including _), and retrieving values (valid paths, nested paths, invalid paths)."

Stop: Await human review of tests. Phase 1, Step 4 complete. Start next prompt for Phase 1, Step 5.

Develop Initial Validation Service (Basic):

Action: Create a validation module that checks the generated YAML or internal representation for:

Presence of mandatory keys for action (action_name, output_key) and script (code, output_key)[cite: 93].

Basic output_key uniqueness (Section 8.2 [cite: 79]).

Reference: Section 8.1, 11.1 of the Source of Truth Document[cite: 77, 93].

🤖 AI Execution Guidance:

Prompt 5.1: "AI, create a Python file named validator.py. Implement a function validate_step(step, existing_output_keys: set) -> list[str] where step is an ActionStep or ScriptStep instance.

If ActionStep, check for action_name and output_key.

If ScriptStep, check for code and output_key.

If output_key is not '_', check if it's in existing_output_keys. If so, add an error.

Return a list of error message strings. Add the output_key to existing_output_keys if it's valid and not '_'."

Stop: Await human review of validate_step.

Prompt 5.2: "AI, in validator.py, implement validate_workflow(workflow: Workflow, initial_data_context: DataContext) -> list[str]. This function should iterate through workflow.steps:

Maintain a set of seen_output_keys (initialized from initial_data_context's top-level keys if desired, or start empty).

For each step, call validate_step. Accumulate errors.

(Future enhancement placeholder: data reference validation using initial_data_context.is_path_available for input_args).
Return a list of all error messages."

Stop: Await human review. Phase 1, Step 5 complete. Start next prompt for Phase 1, Step 6.

Create Basic Input Mechanism & Output:

Decision: For this phase, will a Command Line Interface (CLI) or a very simple web/GUI form be used to define a sequence of actions? (A CLI is likely faster for Phase 1).

🤖 AI Execution Guidance (Decision Support): "AI, list pros and cons of using a CLI vs. a very simple web form (e.g., single HTML page with Flask backend) for the Phase 1 input mechanism, considering speed of implementation for this phase."

Stop: Await human decision on CLI/Web/GUI. (Assume CLI is chosen for next prompts).

Action (if CLI): Implement CLI commands to:

Add an action step (prompt for name, output key, simple key-value for input args).

Add a script step (prompt for output key, simple key-value for input args, path to code or direct input).

Generate and print YAML.

Run validation.

Action: Ensure the application can output the generated YAML to the console or a file.

🤖 AI Execution Guidance (CLI Implementation):

Prompt 6.1 (if CLI chosen): "AI, create main_cli.py. Using the click library for Python:

Implement a main command group.

Implement a command add_action that prompts the user for action_name, output_key. For input_args, prompt for key-value pairs iteratively until the user indicates completion. Store these steps in a global list (for simplicity in Phase 1).

Implement add_script that prompts for output_key, code (as multi-line input), and input_args (iterative key-value).

Implement show_yaml that uses yaml_generator.generate_yaml_string on the current workflow list and prints to console.

Implement validate that uses validator.validate_workflow (with an empty DataContext for now) and prints errors or 'Workflow is valid'."

Stop: Await human review and testing of the basic CLI functionality. Phase 1, Step 6 complete. Phase 1 complete. Start next prompt for Phase 2.

Verification/Checkpoints for Phase 1:

Can the system generate valid YAML for a sequence of 2-3 action and script steps?

Are output_keys correctly registered in the internal data context representation?

Does basic validation for mandatory keys and output_key uniqueness work?

Is the generated YAML compliant with basic Moveworks syntax for these simple steps? [cite: 77, 93]

Phase 2: UI/UX Development & Enhanced Data Mapping
Objective: Develop the primary user interface (desktop or web based on architectural decision) for visual/guided workflow creation and implement initial data handling and mapping capabilities.

Knowledge Base Focus (Source of Truth Document):

Section 2.2 (Input Variables) [cite: 22]

Section 2.3 (The data Object) [cite: 23]

Section 2.4 (requestor, mw Objects - basic awareness) [cite: 24]

Section 3 (Parsing and Referencing Retrieved JSON Data) [cite: 25, 26, 27, 28, 29, 30, 31]

UI/UX design principles for workflow tools.

Next Steps & Action Items:

Finalize UI Architecture Choice:

Decision: Based on prior discussions (preference against TypeScript, ease of creation):

Option A: Python Desktop Application: (e.g., PySide6). Proceed with designing the main window, panels.

Option B: Python SSR Web App + Minimal JS: (e.g., Flask/Django + Jinja2 + HTMX/Alpine.js). Design page layouts and basic interactivity.

🤖 AI Execution Guidance (Decision Support):

Prompt 2.1.1: "AI, given the preference for Python-centric solutions and easier creation, elaborate on the pros and cons of Option A (PySide6 Desktop App) vs. Option B (Python SSR Web App with Flask/Jinja2 and minimal JS like HTMX) for this project. Consider factors like UI complexity for a workflow builder, development effort for each phase, and cross-platform support."

Stop: Await human decision on UI architecture (e.g., "Proceed with Option A: PySide6").

Action: Set up the chosen UI framework and project structure.

🤖 AI Execution Guidance (Setup for chosen option):

Prompt 2.1.2 (if Option A - PySide6 chosen): "AI, outline the initial PySide6 project structure. Create a basic main_gui.py with a QMainWindow, a menu bar, and placeholders for the main areas (workflow construction, properties panel, YAML preview, JSON input panel). Add PySide6 to requirements.txt."

Prompt 2.1.2 (if Option B - Flask/Jinja2 chosen): "AI, set up a basic Flask application structure: app.py, a templates folder with a base.html and index.html. index.html should have placeholders for the main areas. Add Flask and Jinja2 to requirements.txt."

Stop: Await human review of the initial UI project setup.

Design & Implement Core UI Layout:

Action: Create the main application window/page with distinct areas for:

A workflow construction area (canvas or step-list).

A properties/configuration panel (to edit selected step details).

A YAML preview panel.

A data context/JSON input panel.

🤖 AI Execution Guidance (UI Layout - specific to chosen architecture):

Prompt 2.2.1 (PySide6): "AI, in main_gui.py, use PySide6 layout managers (e.g., QHBoxLayout, QVBoxLayout, QSplitter) to arrange placeholder widgets (e.g., QListWidget for step-list, QTextEdit for properties, YAML preview, JSON input) into the described layout. Ensure the YAML preview and JSON input panels are resizable."

Prompt 2.2.1 (Flask/Jinja2): "AI, update templates/index.html (extending base.html) using HTML and basic CSS (or utility classes if a CSS framework like Tailwind is added later) to create the four main layout areas. Use div elements with appropriate IDs. For now, these can be simple text areas or placeholders."

Stop: Await human review of the visual layout.

Implement Workflow Construction Interface (Initial Version):

Decision: Will this be drag-and-drop or a structured list/form-based input first? (A structured list might be simpler for initial UI implementation).

🤖 AI Execution Guidance (Decision Support): "AI, compare the implementation complexity of a basic drag-and-drop step interface versus a structured list-based interface (add, remove, reorder buttons) for managing workflow steps in Phase 2, using [Chosen UI Tech: PySide6 or Flask/HTML]."

Stop: Await human decision. (Assume structured list for next prompts).

Action: Allow users to add action and script steps to a workflow.

Action: Allow users to reorder steps.

Action: When a step is selected, its properties appear in the configuration panel.

🤖 AI Execution Guidance (Structured List UI - specific to chosen architecture):

Prompt 2.3.1 (PySide6): "AI, using a QListWidget for the workflow construction area in main_gui.py:

Add 'Add Action' and 'Add Script' buttons. Clicking these should add a new item (e.g., 'New Action Step') to the QListWidget and append a corresponding ActionStep or ScriptStep (from core_structures.py) to an internal workflow list.

Implement 'Remove Selected' and 'Move Up'/'Move Down' buttons for the selected item in QListWidget, updating the internal workflow list accordingly.

When an item in QListWidget is selected, emit a signal or call a method to populate the properties panel (details in next step)."

Prompt 2.3.1 (Flask/Jinja2 + HTMX): "AI, in app.py and templates/index.html:

The workflow construction area should display the current list of steps (from a Python list in the Flask session or a simple in-memory store).

Add 'Add Action' and 'Add Script' buttons/forms that POST to Flask routes. These routes append new ActionStep/ScriptStep objects to the server-side workflow list and re-render the step list partial (using HTMX for dynamic updates if preferred).

Implement functionality (e.g., links/buttons next to each step) for removing or reordering steps, making requests to Flask routes that update the server-side list and re-render."

Stop: Await human review of basic step addition, removal, and reordering.

Develop Action Configuration Panel:

Action: For a selected action or script step, display dynamic forms to edit its attributes:

action_name (text input).

output_key (text input, with _ handling).

input_args (key-value pair editor).

code (multi-line text area for script).

progress_updates (form for on_pending, on_complete).

delay_config (form for milliseconds, seconds, etc.).

Requirement: Changes in this panel should update the internal workflow representation and trigger YAML regeneration in the preview.

🤖 AI Execution Guidance (Properties Panel - specific to chosen architecture):

Prompt 2.4.1 (PySide6): "AI, create a new widget, PropertiesPanel(QWidget). When a step is selected in the workflow list, this panel should dynamically display input fields (e.g., QLineEdit for action_name, output_key; QTextEdit for code; a custom widget or QTableWidget for input_args key-value pairs; separate QLineEdits for progress_updates and delay_config fields). Changes in these fields should update the corresponding attributes of the selected step object in the internal workflow list. Trigger a signal when changes are made so the YAML preview can update."

Prompt 2.4.1 (Flask/Jinja2): "AI, when a step is 'selected' (e.g., by clicking an 'Edit' button next to it, which could load its details into a form in the properties panel area, possibly via a new route or HTMX):

Create a Flask route and template partial for rendering the properties form for a given step.

The form should have inputs for all relevant attributes (action_name, output_key, input_args as dynamic key-value pairs, code, etc.).

Submitting this form should update the step in the server-side workflow list and re-render relevant parts of the page."

Stop: Await human review of the properties panel interaction and data binding to the internal step objects.

Implement JSON Input & Basic Data Referencing:

Action: Create a UI section where users can paste an example JSON payload (simulating CA input or a previous step's output).

Action: Parse this JSON and display its structure (e.g., as a tree view).

Action: In the input_args editor, allow users to manually type data.path.to.json.field or data.input_variable_name[cite: 22, 23, 26].

Action: Implement basic requestor object access (e.g., allow typing requestor.email_addr)[cite: 24].

🤖 AI Execution Guidance (JSON Input - specific to chosen architecture):

Prompt 2.5.1 (PySide6): "AI, in the JSON input panel (e.g., a QTextEdit):

Allow users to paste JSON. Add a 'Parse JSON' button.

When clicked, parse the JSON. If valid, populate another widget (e.g., QTreeWidget) to display the JSON structure. If invalid, show an error message.

This parsed JSON should be stored in an instance of DataContext (from Phase 1) representing the initial context for the workflow.

For now, the input_args editor in the Properties Panel remains a manual text input for data. paths."

Prompt 2.5.1 (Flask/Jinja2): "AI, in the JSON input panel area of index.html:

Add a <textarea> for JSON input and a 'Parse JSON' button.

Clicking the button (or on text change with some JS) could submit this JSON to a Flask route. The route parses it, stores it (e.g., in the session-based DataContext), and re-renders a part of the page to display the JSON structure (e.g., as a nested list or using a simple JS tree view library if one is allowed for display only). Show errors if JSON is invalid.

The input_args editor remains manual for data. paths."

Stop: Await human review of JSON input, parsing, and display.

Integrate Real-time YAML Preview:

Action: Ensure the YAML preview panel updates automatically (or via a button) whenever the workflow or step configurations change.

Action: Connect this to the YAML generation module from Phase 1.

🤖 AI Execution Guidance (YAML Preview - specific to chosen architecture):

Prompt 2.6.1 (PySide6): "AI, connect the YAML preview panel (QTextEdit). Whenever the internal workflow list is modified (step added, removed, reordered, or properties of a selected step changed via PropertiesPanel), regenerate the YAML string using yaml_generator.generate_yaml_string and update the YAML preview panel's text."

Prompt 2.6.1 (Flask/Jinja2): "AI, whenever the server-side workflow list is modified (due to form submissions for adding/editing steps), ensure the Flask route re-renders the YAML preview panel area. The template for this area will call yaml_generator.generate_yaml_string with the current workflow data."

Stop: Await human review of YAML preview updating in response to UI changes.

Implement Workflow Save/Load (Local):

Action: Allow users to save their current workflow design to a local file (e.g., in a project-specific JSON or XML format, not the final YAML).

Action: Allow users to load a previously saved workflow design file.

🤖 AI Execution Guidance (Save/Load - specific to chosen architecture):

Prompt 2.7.1 (PySide6): "AI, add 'File' menu options for 'Save Workflow' and 'Load Workflow'.

'Save Workflow': Use QFileDialog.getSaveFileName. Serialize the internal workflow list (list of ActionStep/ScriptStep objects) to a JSON file (e.g., using their asdict representation if they are dataclasses).

'Load Workflow': Use QFileDialog.getOpenFileName. Read the JSON file, deserialize it back into ActionStep/ScriptStep objects, and repopulate the internal workflow list and the UI's step list."

Prompt 2.7.1 (Flask/Jinja2): "AI, implement 'Upload Workflow File' (HTML file input) and 'Download Workflow File' buttons/links.

'Download': A Flask route that serializes the current server-side workflow list to JSON and returns it as a file download.

'Upload': A Flask route that accepts a JSON file upload, deserializes it into ActionStep/ScriptStep objects, and replaces the server-side workflow list. Re-render the page."

Stop: Await human testing of save/load functionality. Phase 2 complete. Start next prompt for Phase 3.

Verification/Checkpoints for Phase 2:

Can users visually (or via forms) create a sequence of action and script steps?

Can users configure all basic attributes of these steps?

Does the YAML preview update correctly?

Can users input JSON and manually reference its fields in input_args?

Can workflows be saved and loaded?

Phase 3: Control Flow & Advanced Data Transformation
Objective: Implement full support for Moveworks control flow constructs (switch, for, parallel) and integrate assistance for DSL and APIthon transformations.

Knowledge Base Focus (Source of Truth Document):

Section 4.1 (DSL) & Table 1 [cite: 32, 33, 34]

Section 4.2 (APIthon - full details) [cite: 35, 36, 37, 38]

Section 5 (Workflow Logic - all subsections) [cite: 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]

Section 8.1 (DSL quoting and APIthon multi-line) [cite: 77]

Section 11.1 (YAML Syntax for switch, for, parallel, return) [cite: 93]

Section 11.3 (APIthon Constraints) [cite: 97]

Next Steps & Action Items:

Implement switch Expression UI & Logic:

Action: Add a "switch" element to the workflow builder.

Action: Create UI for defining cases (each with a condition expression and a nested list of steps) and an optional default block with steps[cite: 40, 41, 42, 43].

Requirement: Conditions should allow references to data object fields.

Action: Update YAML generation to produce valid switch syntax[cite: 93].

Action: Extend validation to check switch structure and boolean conditions.

🤖 AI Execution Guidance:

Prompt 3.1.1: "AI, in core_structures.py, define SwitchStep, SwitchCase, and DefaultCase classes. SwitchStep contains a list of SwitchCases and an optional DefaultCase. SwitchCase has condition (str) and steps (list of ActionStep/ScriptStep, etc.). DefaultCase has steps. Update Workflow.steps to allow these new types."

Stop: Review data structures.

Prompt 3.1.2 (UI - specific to architecture): "AI, update the UI to allow adding a 'Switch Step'. The Properties Panel for a Switch Step should allow:

Adding/removing cases. For each case: an input for condition string, and a way to define its nested steps (this might involve a sub-workflow editor or re-using the main step list UI component in a nested context).

Adding/removing a default case with its nested steps."

Stop: Review UI for switch configuration.

Prompt 3.1.3: "AI, update yaml_generator.workflow_to_yaml_dict to handle SwitchStep, correctly serializing cases (with condition and steps) and default (with steps). Refer to Section 11.1 for switch YAML structure."

Stop: Review YAML generation for switch.

Prompt 3.1.4: "AI, update validator.py to validate SwitchStep: ensure cases is a list, each case has condition and steps. (Condition expression validation is advanced, skip for now, but ensure data. paths in conditions can be checked later)."

Stop: Review validation for switch.

Implement for Loop Expression UI & Logic:

Action: Add a "for loop" element to the workflow builder.

Action: Create UI for configuring each, index, in (allowing selection of an array from the data context, e.g., data.my_json.my_array), and output_key[cite: 44, 45, 46].

Action: Allow users to define nested steps within the loop.

Requirement: Inside the loop's steps, the each variable's fields must be accessible for mapping (e.g., item.field_name if each is item).

Action: Update YAML generation for for loops[cite: 93].

Action: Extend validation for for loop structure and ensuring in references an iterable.

🤖 AI Execution Guidance:

Prompt 3.2.1: "AI, in core_structures.py, define ForLoopStep with attributes: each (str), index (str), in_source (str - representing data.path.to.list), output_key (str), and steps (list). Update Workflow.steps."

Stop: Review data structure.

Prompt 3.2.2 (UI): "AI, update UI to add 'For Loop Step'. Properties Panel should allow configuring each, index, in_source (text input for now), output_key, and its nested steps."

Stop: Review UI for for configuration.

Prompt 3.2.3: "AI, update yaml_generator.workflow_to_yaml_dict for ForLoopStep based on Section 11.1."

Stop: Review YAML for for.

Prompt 3.2.4: "AI, update validator.py for ForLoopStep: check mandatory fields. (Later, in_source will need validation against DataContext to ensure it's an iterable path)."

Stop: Review validation for for.

Implement parallel Expression UI & Logic:

Action: Add a "parallel" element to the workflow builder.

Action: Create UI for defining branches (each branch being a list of steps)[cite: 47, 48, 49, 50].

Action: (Advanced) UI for defining parallel for loops[cite: 48].

Action: Update YAML generation for parallel syntax[cite: 93].

Action: Ensure data context correctly reflects availability of outputs from parallel branches after the parallel block completes.

🤖 AI Execution Guidance:

Prompt 3.3.1: "AI, in core_structures.py, define ParallelStep with an attribute branches (list of lists of steps, where each inner list is a branch). Optional: consider parallel_for_config if doing parallel for. Update Workflow.steps."

Stop: Review data structure.

Prompt 3.3.2 (UI): "AI, update UI for 'Parallel Step'. Properties Panel should allow defining multiple branches, each branch containing its own sequence of steps."

Stop: Review UI for parallel.

Prompt 3.3.3: "AI, update yaml_generator.workflow_to_yaml_dict for ParallelStep (branches only for now) based on Section 11.1."

Stop: Review YAML for parallel.

Prompt 3.3.4: "AI, update validator.py for ParallelStep: check branches structure. (Data context after parallel completion needs careful handling in DataContext later)."

Stop: Review validation for parallel.

Implement return Expression UI & Logic:

Action: Add a "return" element.

Action: UI for defining the output_mapper dictionary[cite: 93].

Action: Update YAML generation.

🤖 AI Execution Guidance:

Prompt 3.4.1: "AI, define ReturnStep in core_structures.py with output_mapper (dict, optional). Update Workflow.steps."

Stop: Review.

Prompt 3.4.2 (UI): "AI, add 'Return Step' to UI. Properties Panel allows defining output_mapper as key-value pairs (values can be data. paths or DSL expressions)."

Stop: Review.

Prompt 3.4.3: "AI, update yaml_generator.workflow_to_yaml_dict for ReturnStep."

Stop: Review.

Prompt 3.4.4: "AI, update validator.py for ReturnStep (basic structure check)."

Stop: Review.

Develop DSL Assistance Features:

Action: In input_args and output_mapper fields, provide UI helpers for common DSL functions (from Table 1 of Source of Truth Document [cite: 33]). This could be a dropdown of functions that inserts a template.

Action: Implement correct quoting for DSL constants as per Section 8.1[cite: 77].

Action: Basic DSL syntax validation if possible.

🤖 AI Execution Guidance:

Prompt 3.5.1 (UI): "AI, for text input fields in the Properties Panel that accept DSL (e.g., values in input_args, output_mapper, condition):

Suggest adding a small button next to these fields.

When clicked, this button could show a popup/dropdown with common DSL functions from Table 1 (e.g., $CONCAT, $MAP, $LOWERCASE). Selecting one inserts a template string (e.g., $CONCAT([], "", TRUE))."

Stop: Review UI concept for DSL helpers.

Prompt 3.5.2: "AI, in yaml_generator.py, when serializing string values that are intended as DSL expressions (this might need a flag in the internal data structure, e.g., is_dsl_expression=True, or infer based on starting with '$'), ensure they are output as plain strings in YAML, not quoted as YAML strings, so Moveworks evaluates them. For DSL constants (literal strings, numbers, booleans within a DSL expression itself), ensure the generator can produce the correct quoting as per Section 8.1 (e.g., '"string_literal"', '10', 'true'). This is complex; focus on getting $CONCAT example right first."

Stop: Review DSL output formatting. This is a tricky part and may need several iterations.

Enhance APIthon Script Editor:

Action: Ensure the code editor handles multi-line input correctly (e.g., by ensuring the YAML generator uses | or >)[cite: 37].

Action: Display APIthon constraints (Section 11.3 [cite: 97]) near the editor.

Action: Remind user that the last executed line is the return value[cite: 35].

🤖 AI Execution Guidance:

Prompt 3.6.1: "AI, in yaml_generator.py, ensure that the code attribute of ScriptStep is serialized using the YAML literal block scalar | to preserve newlines."

Stop: Review APIthon code block formatting in YAML.

Prompt 3.6.2 (UI): "AI, in the Properties Panel for a ScriptStep, next to the code input area (QTextEdit or <textarea>), display a static text block listing key APIthon constraints from Section 11.3 (No imports, Last line return, No classes, Size limits)."

Stop: Review UI for APIthon constraints display. Phase 3 complete.

Verification/Checkpoints for Phase 3:

Can switch, for, and parallel blocks be visually configured and generate correct YAML?

Are data contexts correctly handled within for loops (access to item) and after parallel blocks?

Can users utilize DSL helper functions to construct input_args?

Is APIthon script input and generation robust?

Does validation cover these new control flow structures?

Phase 4: Built-in Actions & Comprehensive Error Handling
Objective: Integrate knowledge of Moveworks Built-in Actions and enable users to easily configure robust error handling.

Knowledge Base Focus (Source of Truth Document):

Section 6 (Error Handling - all subsections) & 11.2 (error_data) [cite: 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 95]

Section 7 (Built-in Actions - all subsections) & Table 2 [cite: 67, 68, 69, 70, 71, 72, 73, 74, 75]

Section 2.4 (mw object) [cite: 24]

Section 11.1 (YAML Syntax for raise, try_catch) [cite: 93]

Next Steps & Action Items:

Develop Built-in Action (mw.) Catalog & Configuration:

Action: Create a searchable/browseable catalog of mw. actions within the UI, populated from Table 2 and Section 7 of the Source of Truth Document[cite: 75, 67, 68, 69, 70, 71].

Action: When a mw. action is selected, dynamically generate a configuration form for its specific input_args (name, type, required status)[cite: 75].

Requirement: Ensure action_name is correctly set to mw.action_name.

🤖 AI Execution Guidance:

Prompt 4.1.1: "AI, create a Python dictionary or list of objects in a new file mw_actions_catalog.py to store the details of mw. actions from Table 2 (Section 7.2) of the Source of Truth Document. For each action, store its name (e.g., get_user_by_email), a brief description, and a list of its input_args (each with name, type hint like 'string' or 'List[User]', and if it's required)."

Stop: Review catalog data structure.

Prompt 4.1.2 (UI): "AI, when adding an ActionStep in the UI, allow the user to select from this mw_actions_catalog. When a mw. action is chosen, its action_name should be pre-filled (e.g., mw.get_user_by_email). The Properties Panel should then dynamically display input fields specifically for the chosen mw. action's input_args based on the catalog."

Stop: Review UI for mw. action selection and dynamic form generation.

Implement User Object Dependency Logic:

Action: For mw. actions requiring User objects or user_record_ids (e.g., mw.send_plaintext_chat_notification, mw.create_generic_approval_request [cite: 69, 75]), the UI should guide the user.

Suggestion: If a required User object/ID is not available in the current data context, the app could suggest inserting a mw.get_user_by_email step[cite: 72, 73, 74].

🤖 AI Execution Guidance (UI/Logic):

Prompt 4.2.1: "AI, for mw. actions in the catalog that require user_record_id or User objects as input (e.g., mw.send_plaintext_chat_notification, mw.create_generic_approval_request):

When such an action is configured, the UI should check if a suitable data path (e.g., data.some_user_lookup.user.id or data.some_user_lookup.user) is provided for these arguments.

(Advanced Suggestion - for later iteration, just note it now): If not, the UI could display a suggestion: 'This input typically comes from an mw.get_user_by_email step. Would you like to add one?'"

Stop: Review the conceptual logic for handling User object dependencies. Actual suggestion implementation is for later.

Implement try_catch Block UI & Logic:

Action: Allow users to easily wrap existing steps (or add new steps) within a try_catch block from the UI.

Action: Create UI for defining try steps.

Action: Create UI for defining catch steps, including an input for on_status_code (allowing single or list of codes)[cite: 54].

Action: Update YAML generation to produce valid try_catch syntax[cite: 93].

🤖 AI Execution Guidance:

Prompt 4.3.1: "AI, define TryCatchStep, TryBlock, CatchBlock classes in core_structures.py. TryCatchStep contains one TryBlock and one CatchBlock. TryBlock has steps. CatchBlock has steps and an optional on_status_code (str or list). Update Workflow.steps."

Stop: Review data structures.

Prompt 4.3.2 (UI): "AI, update UI to add 'Try-Catch Step'. The Properties Panel should allow defining steps for the try block and steps for the catch block, plus an input for on_status_code for the catch block."

Stop: Review UI for try_catch.

Prompt 4.3.3: "AI, update yaml_generator.workflow_to_yaml_dict for TryCatchStep."

Stop: Review YAML for try_catch.

Prompt 4.3.4: "AI, update validator.py for TryCatchStep (structure, presence of try and catch)."

Stop: Review validation for try_catch.

Implement raise Expression UI & Logic (within catch or standalone):

Action: Allow users to add a raise step.

Action: UI for configuring output_key and message[cite: 58, 59].

Action: Update YAML generation for raise[cite: 93].

🤖 AI Execution Guidance:

Prompt 4.4.1: "AI, define RaiseStep in core_structures.py with output_key (str) and message (str, optional). Update Workflow.steps."

Stop: Review.

Prompt 4.4.2 (UI): "AI, add 'Raise Step' to UI. Properties Panel for output_key and message."

Stop: Review.

Prompt 4.4.3: "AI, update yaml_generator.workflow_to_yaml_dict for RaiseStep."

Stop: Review.

Prompt 4.4.4: "AI, update validator.py for RaiseStep (mandatory output_key)."

Stop: Review.

Expose error_data in catch Blocks:

Action: When configuring steps within a catch block, the data mapping UI should make fields from the error_data object accessible (e.g., error_data.failed_step_output_key.error.message, error_data.failed_step_output_key.error.code)[cite: 60, 95].

Requirement: The failed_step_output_key part needs to be dynamically determined based on the output_key of the step that might have failed in the try block.

🤖 AI Execution Guidance (UI/Logic):

Prompt 4.5.1: "AI, when the UI is rendering the Properties Panel for a step inside a `CatchBlock*:

The DataContext available for data mapping suggestions should be augmented. It needs to know the output_keys of the steps in the corresponding TryBlock.

For data mapping (e.g., in input_args), if the user types error_data., the UI should ideally suggest completions like error_data.<output_key_from_try_block>.error.message or error_data.<output_key_from_try_block>.error.code. This is complex for full auto-completion now; for Phase 4, ensure the user can manually type these paths and they are passed through to YAML. The validator will later check if these paths are plausible."

Stop: Review the conceptual approach. Full dynamic suggestion is hard; focus on allowing manual input.

Offer Error Handling Pattern Templates:

Action: Provide pre-defined templates for common error handling scenarios (e.g., "Log error and continue," "Log, notify admin, and re-raise") based on Section 6.4[cite: 66].

🤖 AI Execution Guidance (UI):

Prompt 4.6.1: "AI, in the UI, when a user is configuring a TryCatchStep (perhaps via a button 'Apply Error Pattern'):

Offer a choice of pre-defined error handling patterns (e.g., 'Log and Raise', 'Log and Notify').

Selecting a pattern would auto-populate the catch block's steps with placeholder ActionSteps (e.g., a script for logging, an mw.send_plaintext_chat_notification placeholder, a RaiseStep). The user would then configure these placeholders."

Stop: Review UI concept for error pattern templates. Phase 4 complete.

Verification/Checkpoints for Phase 4:

Can users find and configure common mw. actions, with correct input_args?

Does the app assist with User object dependencies for mw. actions?

Can try_catch blocks be added and configured, including on_status_code?

Can raise expressions be configured?

Is error_data correctly accessible and usable within catch steps?

Is the generated YAML for error handling valid?

Phase 5: Advanced Validation, Polish & Documentation
Objective: Implement the full suite of Moveworks validation checks, refine the user experience, and prepare comprehensive documentation. (AI-powered assistance features are stretch goals for a later iteration unless development is ahead of schedule).

Knowledge Base Focus (Source of Truth Document):

Section 8 (Ensuring YAML Validity - all subsections) & Table 3 [cite: 76, 77, 78, 79, 80, 81, 82]

Section 1.1, 1.2 (Purpose, Target Audience - for app documentation) [cite: 2, 3, 4, 5, 6, 7, 8, 9, 10]

All Appendix sections (11.1, 11.2, 11.3, 11.4) for final checks and documentation [cite: 92, 93, 94, 95, 96, 97, 98, 99]

Next Steps & Action Items:

Implement Comprehensive Validation Engine:

Action: Extend the validation service to cover all checks detailed in Section 8.2 and Table 3 of the Source of Truth Document[cite: 79, 80, 81, 82], including:

Full Data Reference Integrity (all data.X.Y paths are valid).

Structural Integrity of all control flow blocks.

Completeness of action/script definitions.

Basic Type Safety.

Plausibility checks for DSL syntax.

Verification of HTTP Action variable escaping if this level of detail is feasible[cite: 78].

Action: Provide clear, user-friendly error messages for validation failures, ideally highlighting the problematic part of the workflow/YAML.

🤖 AI Execution Guidance:

Prompt 5.1.1: "AI, enhance validator.validate_workflow. For each step, before validating the step itself, update a running DataContext instance with the output_key of the previous successfully validated step (if any). When validating input_args, condition expressions, or in_source for loops, use data_context.is_path_available(path) to check if data. references are valid. Collect errors if paths are not found. Refer to Section 8.2 and Table 3."

Stop: Review data reference validation logic. This is a key step.

Prompt 5.1.2: "AI, add more structural integrity checks to validator.py for SwitchStep, ForLoopStep, ParallelStep, TryCatchStep based on their definitions in core_structures.py and Section 11.1. For example, ensure cases in SwitchStep is a list of SwitchCase objects."

Stop: Review structural validation.

Prompt 5.1.3 (UI): "AI, ensure that validation errors returned by validator.validate_workflow are displayed clearly to the user in the UI, perhaps in a dedicated validation messages panel, or by highlighting the problematic step in the workflow view."

Stop: Review error display in UI.

UI/UX Polish:

Action: Conduct thorough usability testing.

Action: Refine UI layouts, tooltips, and interactive elements based on feedback.

Action: Optimize application performance (responsiveness of UI, speed of YAML generation and validation).

Action: Ensure consistent terminology aligned with the Source of Truth Document.

🤖 AI Execution Guidance:

Prompt 5.2.1: "AI, review the current UI code ([PySide6 or Flask/HTML]). Suggest 3-5 specific areas for UI/UX improvement focusing on clarity, ease of use for defining complex steps (like nested steps in switch or for), and responsiveness. For example, improving the input_args editor, or visual feedback during validation."

Stop: Await human review of suggestions and selection of items to implement.

Prompt 5.2.2 (Iterative): "AI, implement [selected UI improvement from previous prompt, e.g., 'Improve the input_args editor to better handle multi-line string values']."

Stop: Iterate on UI polish based on human feedback.

Develop In-App Help & Documentation:

Action: Create context-sensitive help for different UI elements and configuration options.

Action: Integrate links or summaries from relevant sections of the Source of Truth Document (e.g., for DSL functions, APIthon constraints, mw. action details).

Action: Prepare a user manual for the application.

🤖 AI Execution Guidance:

Prompt 5.3.1 (UI): "AI, for key UI elements like the Properties Panel for an ActionStep, ScriptStep, ForLoopStep, etc., suggest adding a small 'Help' icon or button. When clicked, it should display a brief explanation of the purpose of that step type and its main configuration fields, drawing information from Sections 11.1, 11.2, 11.3 of the Source of Truth Document."

Stop: Review help integration points.

Prompt 5.3.2: "AI, draft an outline for a user manual for this application. The sections should cover: Introduction, Setup (if any), Building a Basic Workflow (Actions, Scripts), Using Control Flow (Switch, For, Parallel), Data Mapping (JSON, DSL, APIthon), Error Handling, Using Built-in Actions, Validation, Saving/Loading."

Stop: Await human review of the manual outline. Content generation can be iterative per section.

Final Testing & Quality Assurance:

Action: Test against a wide range of workflow scenarios, including edge cases and complex examples (like those in Section 9 of the Source of Truth Document [cite: 83, 84, 85, 86, 87, 88]).

Action: Test YAML output by deploying to a Moveworks environment (if possible) or by having Moveworks SMEs review it.

Action: Perform cross-platform testing if a desktop application was chosen and multiple OS are supported.

🤖 AI Execution Guidance:

Prompt 5.4.1: "AI, based on the illustrative examples in Section 9 of the Source of Truth Document (Examples 1-4), generate a list of test cases for the application. Each test case should specify:

User goal.

Input JSON (if any).

Conceptual steps the user would take in the AI app.

Expected generated YAML structure (key elements).

Key validation checks that should pass."

Stop: Await human review of test cases. Human will perform manual testing using these cases. AI can be used to generate parts of the expected YAML for comparison during testing.

(Stretch Goal) Implement Basic AI-Powered Assistance (If time/resources permit):

Action: Explore simple AI suggestions, e.g., suggesting mw.get_user_by_email if a user tries to use user_record_id without it being available.

Note: Full NLP-driven workflow generation is a significant extension beyond this phase.

🤖 AI Execution Guidance:

Prompt 5.5.1 (Conceptual): "AI, for the feature 'suggest mw.get_user_by_email':

Outline the logic: When an mw. action requiring user_record_id (like mw.send_plaintext_chat_notification) is added or its input_args are being edited.

And the DataContext does not show an obvious path to a user.id (e.g., from a previous mw.get_user_by_email step's output).

How would the UI present this suggestion non-intrusively?

If the user accepts, how would the application insert a new mw.get_user_by_email step before the current one and help map its output?"

Stop: Review the conceptual design for this specific AI assistance feature. Implementation would require further prompts.

Verification/Checkpoints for Phase 5:

Does the validation engine catch all specified error types from the Source of Truth Document?

Are validation messages clear and actionable?

Is the UI intuitive and responsive?

Is in-app help comprehensive and useful?

Are a variety of complex YAML scenarios generated correctly and validated?

Is the application stable and performant?

This interactive project plan provides a structured path to developing the "AI-Powered Moveworks Compound Action Assistant." Each phase builds upon the last, ensuring that the core logic is sound before adding more complex features and UI elements. Constant reference to the "Generating Verified Moveworks Compound Action YAML for AI Application Development.docx" will be paramount for accuracy.