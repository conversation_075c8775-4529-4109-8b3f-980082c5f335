import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import {
  Play,
  Save,
  Download,
  Co<PERSON>,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { copyToClipboard, downloadFile } from '@/lib/utils'
import toast from 'react-hot-toast'
import Editor from '@monaco-editor/react'
import StepEditor from '@/components/StepEditor'
import WorkflowVisualization from '@/components/WorkflowVisualization'
import WorkflowTester from '@/components/WorkflowTester'

export default function WorkflowBuilder() {
  const { workflowId } = useParams()

  const {
    currentWorkflow,
    loadWorkflow,
    createWorkflow,
    saveWorkflow,
    generateYaml,
    validateWorkflow,
    generatedYaml,
    validationResult,
    yamlMetadata,
    isGenerating,
    yamlPreviewVisible,
    toggleYamlPreview,
  } = useWorkflowStore()

  const [isValidating, setIsValidating] = useState(false)
  const [showVisualization, setShowVisualization] = useState(true)
  const [showTester, setShowTester] = useState(false)

  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId)
    } else if (!currentWorkflow) {
      // Create a new workflow if none exists
      createWorkflow('Untitled Workflow', 'A new Moveworks Compound Action workflow')
    }
  }, [workflowId, currentWorkflow, loadWorkflow, createWorkflow])

  const handleSave = () => {
    saveWorkflow()
    toast.success('Workflow saved successfully')
  }

  const handleGenerateYaml = async () => {
    if (!currentWorkflow) return

    try {
      await generateYaml({ include_comments: true })
      toast.success('YAML generated successfully')
    } catch (error) {
      toast.error('Failed to generate YAML')
      console.error('YAML generation error:', error)
    }
  }

  const handleValidate = async () => {
    if (!currentWorkflow) return

    setIsValidating(true)
    try {
      await validateWorkflow()
      toast.success('Workflow validated successfully')
    } catch (error) {
      toast.error('Failed to validate workflow')
      console.error('Validation error:', error)
    } finally {
      setIsValidating(false)
    }
  }

  const handleCopyYaml = async () => {
    if (!generatedYaml) return

    try {
      await copyToClipboard(generatedYaml)
      toast.success('YAML copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy YAML')
    }
  }

  const handleDownloadYaml = () => {
    if (!generatedYaml || !currentWorkflow) return

    const filename = `${currentWorkflow.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.yaml`
    downloadFile(generatedYaml, filename, 'text/yaml')
    toast.success('YAML downloaded successfully')
  }

  if (!currentWorkflow) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading-spinner h-8 w-8"></div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-secondary-200 bg-white">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-xl font-semibold text-secondary-900">
              {currentWorkflow.name}
            </h1>
            {currentWorkflow.description && (
              <p className="text-sm text-secondary-600">
                {currentWorkflow.description}
              </p>
            )}
          </div>
          {validationResult && (
            <div className="flex items-center space-x-2">
              {validationResult.isValid ? (
                <div className="flex items-center text-success-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm">Valid</span>
                </div>
              ) : (
                <div className="flex items-center text-error-600">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm">
                    {validationResult.errors.length} error(s)
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleValidate}
            disabled={isValidating}
            className="btn btn-outline btn-sm"
          >
            {isValidating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Validate
          </button>

          <button
            onClick={() => setShowTester(true)}
            disabled={currentWorkflow.steps.length === 0}
            className="btn btn-outline btn-sm"
            title="Test workflow"
          >
            <Play className="h-4 w-4 mr-2" />
            Test
          </button>

          <button
            onClick={handleGenerateYaml}
            disabled={isGenerating}
            className="btn btn-primary btn-sm"
          >
            {isGenerating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Generate YAML
          </button>

          <button
            onClick={toggleYamlPreview}
            className="btn btn-ghost btn-sm"
          >
            {yamlPreviewVisible ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>

          <button
            onClick={handleSave}
            className="btn btn-secondary btn-sm"
          >
            <Save className="h-4 w-4 mr-2" />
            Save
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Step Editor Sidebar */}
        <StepEditor
          className="w-80"
          showVisualization={showVisualization}
          onToggleVisualization={() => setShowVisualization(!showVisualization)}
        />

        {/* Workflow Canvas */}
        <div className="flex-1 bg-secondary-50">
          {showVisualization && currentWorkflow.steps.length > 0 ? (
            <WorkflowVisualization
              workflow={currentWorkflow}
              onStepSelect={(stepId) => {
                const { selectStep } = useWorkflowStore.getState()
                selectStep(stepId)
              }}
              selectedStepId={useWorkflowStore.getState().selectedStepId || undefined}
            />
          ) : (
            <div className="h-full p-6">
              <div className="h-full bg-white rounded-lg border border-secondary-200 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">⚡</div>
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">
                    Workflow Builder
                  </h3>
                  <p className="text-secondary-600 mb-4">
                    {currentWorkflow.steps.length === 0
                      ? "Add steps using the sidebar to get started."
                      : "Toggle visualization to see your workflow structure."
                    }
                  </p>
                  <div className="space-y-2 text-sm text-secondary-500">
                    <p>Current workflow has {currentWorkflow.steps.length} steps</p>
                    {yamlMetadata && (
                      <p>Complexity score: {yamlMetadata.complexity_score}</p>
                    )}
                  </div>
                  {currentWorkflow.steps.length > 0 && (
                    <div className="mt-6 space-y-3">
                      <button
                        onClick={() => setShowVisualization(true)}
                        className="btn btn-outline mr-3"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Show Visualization
                      </button>
                      <button
                        onClick={handleGenerateYaml}
                        disabled={isGenerating}
                        className="btn btn-primary"
                      >
                        {isGenerating ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Play className="h-4 w-4 mr-2" />
                        )}
                        Generate YAML
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* YAML Preview Panel */}
        {yamlPreviewVisible && (
          <div className="w-1/2 border-l border-secondary-200 bg-white flex flex-col">
            <div className="flex items-center justify-between p-4 border-b border-secondary-200">
              <h3 className="font-medium text-secondary-900">Generated YAML</h3>
              {generatedYaml && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleCopyYaml}
                    className="btn btn-ghost btn-sm"
                    title="Copy to clipboard"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    onClick={handleDownloadYaml}
                    className="btn btn-ghost btn-sm"
                    title="Download YAML"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>

            <div className="flex-1 relative">
              {generatedYaml ? (
                <Editor
                  height="100%"
                  defaultLanguage="yaml"
                  value={generatedYaml}
                  options={{
                    readOnly: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: 'on',
                    wordWrap: 'on',
                  }}
                  theme="vs"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-secondary-500">
                  <div className="text-center">
                    <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Click "Generate YAML" to see the output</p>
                  </div>
                </div>
              )}
            </div>

            {/* Validation Results */}
            {validationResult && !validationResult.isValid && (
              <div className="border-t border-secondary-200 p-4 bg-error-50">
                <h4 className="font-medium text-error-900 mb-2">Validation Errors</h4>
                <div className="space-y-1">
                  {validationResult.errors.slice(0, 5).map((error, index) => (
                    <div key={index} className="text-sm text-error-700">
                      <span className="font-medium">{error.type}:</span> {error.message}
                      {error.path && <span className="text-error-600"> ({error.path})</span>}
                    </div>
                  ))}
                  {validationResult.errors.length > 5 && (
                    <div className="text-sm text-error-600">
                      ... and {validationResult.errors.length - 5} more errors
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metadata */}
            {yamlMetadata && (
              <div className="border-t border-secondary-200 p-4 bg-secondary-50">
                <h4 className="font-medium text-secondary-900 mb-2">Metadata</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-secondary-600">Steps:</span>
                    <span className="ml-2 font-medium">{yamlMetadata.step_count}</span>
                  </div>
                  <div>
                    <span className="text-secondary-600">Complexity:</span>
                    <span className="ml-2 font-medium">{yamlMetadata.complexity_score}</span>
                  </div>
                  {yamlMetadata.estimated_execution_time && (
                    <div className="col-span-2">
                      <span className="text-secondary-600">Est. execution time:</span>
                      <span className="ml-2 font-medium">
                        {yamlMetadata.estimated_execution_time}ms
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Workflow Tester Modal */}
      {showTester && (
        <WorkflowTester
          workflow={currentWorkflow}
          onClose={() => setShowTester(false)}
        />
      )}
    </div>
  )
}
