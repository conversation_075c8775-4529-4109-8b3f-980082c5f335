/**
 * Shared types for the frontend application
 * Mirrors the backend types for consistency
 */

// Base interfaces for all expression types
export interface BaseExpression {
  output_key?: string;
}

// Action expression
export interface ActionExpression extends BaseExpression {
  action: {
    action_name: string;
    output_key?: string;
    input_args: Record<string, any>;
    progress_updates?: boolean;
    delay_config?: {
      delay_seconds: number;
      max_retries?: number;
    };
  };
}

// Script expression
export interface ScriptExpression extends BaseExpression {
  script: {
    output_key?: string;
    input_args: Record<string, any>;
    code: string;
  };
}

// Switch expression
export interface SwitchExpression extends BaseExpression {
  switch: {
    output_key?: string;
    cases: Array<{
      condition: string;
      steps: Expression[];
    }>;
    default?: {
      steps: Expression[];
    };
  };
}

// For loop expression
export interface ForExpression extends BaseExpression {
  for: {
    output_key?: string;
    each: string;
    index?: string;
    in: string;
    steps: Expression[];
  };
}

// Parallel expression
export interface ParallelExpression extends BaseExpression {
  parallel: {
    output_key?: string;
    branches: Expression[];
  } | {
    output_key?: string;
    for: {
      each: string;
      index?: string;
      in: string;
      steps: Expression[];
    };
  };
}

// Try-catch expression
export interface TryCatchExpression extends BaseExpression {
  try_catch: {
    output_key?: string;
    try: {
      steps: Expression[];
    };
    catch: {
      on_status_code?: number[];
      steps: Expression[];
    };
  };
}

// Raise expression
export interface RaiseExpression extends BaseExpression {
  raise: {
    output_key?: string;
    message: string;
  };
}

// Return expression
export interface ReturnExpression extends BaseExpression {
  return: {
    output_key?: string;
    value?: any;
  };
}

// Union type for all expressions
export type Expression = 
  | ActionExpression 
  | ScriptExpression 
  | SwitchExpression 
  | ForExpression 
  | ParallelExpression 
  | TryCatchExpression 
  | RaiseExpression 
  | ReturnExpression;

// Root compound action structure
export interface CompoundAction {
  steps: Expression[];
}

// Workflow definition for the visual builder
export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  input_schema?: Record<string, any>;
  steps: WorkflowStep[];
  created_at: Date;
  updated_at: Date;
}

export interface WorkflowStep {
  id: string;
  type: 'action' | 'script' | 'switch' | 'for' | 'parallel' | 'try_catch' | 'raise' | 'return';
  position: { x: number; y: number };
  config: any;
  connections: string[];
}

// Built-in Moveworks actions
export interface MoveworksBuiltinAction {
  name: string;
  description: string;
  input_args: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  output_structure: Record<string, any>;
}

// Validation types
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  type: 'syntax' | 'structure' | 'reference' | 'type';
  message: string;
  path?: string;
  line?: number;
  column?: number;
}

export interface ValidationWarning {
  type: 'best_practice' | 'performance' | 'maintainability';
  message: string;
  path?: string;
  suggestion?: string;
}

// API request/response types
export interface GenerateYamlRequest {
  workflow: WorkflowDefinition;
  options?: {
    format?: 'yaml' | 'json';
    validate?: boolean;
    include_comments?: boolean;
  };
}

export interface GenerateYamlResponse {
  yaml: string;
  validation: ValidationResult;
  metadata: {
    step_count: number;
    complexity_score: number;
    estimated_execution_time?: number;
  };
}

// DSL Function type
export interface DSLFunction {
  name: string;
  description: string;
  syntax: string;
  example: string;
  category: string;
}

// UI State types
export interface UIState {
  selectedStep: string | null;
  isGenerating: boolean;
  showValidation: boolean;
  sidebarCollapsed: boolean;
  yamlPreviewVisible: boolean;
}

// Node types for React Flow
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    config: any;
    isValid: boolean;
    errors: ValidationError[];
  };
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
}

// Form types for step configuration
export interface ActionStepConfig {
  action_name: string;
  output_key: string;
  input_args: Record<string, any>;
  progress_updates?: boolean;
  delay_config?: {
    delay_seconds: number;
    max_retries?: number;
  };
}

export interface ScriptStepConfig {
  output_key: string;
  input_args: Record<string, any>;
  code: string;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Export utility type for step configurations
export type StepConfig<T extends WorkflowStep['type']> = 
  T extends 'action' ? ActionStepConfig :
  T extends 'script' ? ScriptStepConfig :
  any;
