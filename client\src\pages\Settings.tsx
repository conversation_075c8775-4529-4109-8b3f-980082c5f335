import { useState } from 'react'
import { Save, Download, Upload, Trash2, RefreshCw } from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { downloadFile } from '@/lib/utils'
import toast from 'react-hot-toast'

export default function Settings() {
  const { workflows, deleteWorkflow } = useWorkflowStore()
  const [exportFormat, setExportFormat] = useState<'json' | 'yaml'>('json')

  const handleExportAll = () => {
    const data = {
      version: '1.0.0',
      exported_at: new Date().toISOString(),
      workflows: workflows,
    }
    
    const content = JSON.stringify(data, null, 2)
    const filename = `moveworks_workflows_${new Date().toISOString().split('T')[0]}.json`
    
    downloadFile(content, filename, 'application/json')
    toast.success('All workflows exported successfully')
  }

  const handleImportWorkflows = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const data = JSON.parse(content)
        
        // Validate the import format
        if (!data.workflows || !Array.isArray(data.workflows)) {
          throw new Error('Invalid import format')
        }
        
        // TODO: Implement import logic
        toast.success(`Ready to import ${data.workflows.length} workflows`)
        console.log('Import data:', data)
      } catch (error) {
        toast.error('Failed to import workflows. Please check the file format.')
        console.error('Import error:', error)
      }
    }
    
    reader.readAsText(file)
    event.target.value = '' // Reset input
  }

  const handleClearAll = () => {
    if (confirm('Are you sure you want to delete all workflows? This action cannot be undone.')) {
      workflows.forEach(workflow => deleteWorkflow(workflow.id))
      toast.success('All workflows deleted')
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">Settings</h1>
        <p className="text-secondary-600">
          Manage your application preferences and workflow data.
        </p>
      </div>

      <div className="space-y-8">
        {/* Data Management */}
        <section className="card">
          <div className="card-header">
            <h2 className="card-title">Data Management</h2>
            <p className="card-description">
              Export, import, and manage your workflow data.
            </p>
          </div>
          <div className="card-content space-y-6">
            {/* Export */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Export Workflows</h3>
              <div className="flex items-center space-x-4">
                <select
                  value={exportFormat}
                  onChange={(e) => setExportFormat(e.target.value as 'json' | 'yaml')}
                  className="input w-auto"
                >
                  <option value="json">JSON Format</option>
                  <option value="yaml">YAML Format</option>
                </select>
                <button
                  onClick={handleExportAll}
                  className="btn btn-primary btn-md"
                  disabled={workflows.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export All ({workflows.length})
                </button>
              </div>
              <p className="text-sm text-secondary-600 mt-2">
                Export all your workflows to a file for backup or sharing.
              </p>
            </div>

            {/* Import */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Import Workflows</h3>
              <div className="flex items-center space-x-4">
                <label className="btn btn-outline btn-md cursor-pointer">
                  <Upload className="h-4 w-4 mr-2" />
                  Choose File
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportWorkflows}
                    className="hidden"
                  />
                </label>
              </div>
              <p className="text-sm text-secondary-600 mt-2">
                Import workflows from a previously exported JSON file.
              </p>
            </div>

            {/* Clear Data */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Clear All Data</h3>
              <button
                onClick={handleClearAll}
                className="btn btn-outline btn-md text-error-600 border-error-300 hover:bg-error-50"
                disabled={workflows.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete All Workflows
              </button>
              <p className="text-sm text-secondary-600 mt-2">
                Permanently delete all workflows. This action cannot be undone.
              </p>
            </div>
          </div>
        </section>

        {/* Application Settings */}
        <section className="card">
          <div className="card-header">
            <h2 className="card-title">Application Settings</h2>
            <p className="card-description">
              Configure your application preferences.
            </p>
          </div>
          <div className="card-content space-y-6">
            {/* Theme */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Theme</h3>
              <select className="input w-auto" defaultValue="light">
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System</option>
              </select>
              <p className="text-sm text-secondary-600 mt-2">
                Choose your preferred color theme.
              </p>
            </div>

            {/* Auto-save */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Auto-save</h3>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  defaultChecked
                  className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-secondary-700">
                  Automatically save workflows while editing
                </span>
              </label>
              <p className="text-sm text-secondary-600 mt-2">
                Enable automatic saving to prevent data loss.
              </p>
            </div>

            {/* Validation */}
            <div>
              <h3 className="text-lg font-medium text-secondary-900 mb-3">Validation</h3>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  defaultChecked
                  className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-secondary-700">
                  Show real-time validation errors
                </span>
              </label>
              <p className="text-sm text-secondary-600 mt-2">
                Display validation errors as you build workflows.
              </p>
            </div>
          </div>
        </section>

        {/* API Settings */}
        <section className="card">
          <div className="card-header">
            <h2 className="card-title">API Configuration</h2>
            <p className="card-description">
              Configure connection to the backend API.
            </p>
          </div>
          <div className="card-content space-y-6">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                API Base URL
              </label>
              <input
                type="url"
                defaultValue="/api"
                className="input w-full"
                placeholder="https://api.example.com"
              />
              <p className="text-sm text-secondary-600 mt-2">
                Base URL for the Moveworks Assistant API.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                Request Timeout (seconds)
              </label>
              <input
                type="number"
                defaultValue={30}
                min={5}
                max={300}
                className="input w-auto"
              />
              <p className="text-sm text-secondary-600 mt-2">
                Maximum time to wait for API responses.
              </p>
            </div>

            <button className="btn btn-outline btn-md">
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Connection
            </button>
          </div>
        </section>

        {/* About */}
        <section className="card">
          <div className="card-header">
            <h2 className="card-title">About</h2>
          </div>
          <div className="card-content">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary-600">Version:</span>
                <span className="font-medium">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Build:</span>
                <span className="font-medium">Phase 1 - Core Engine</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Last Updated:</span>
                <span className="font-medium">{new Date().toLocaleDateString()}</span>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <p className="text-sm text-secondary-600">
                AI-Powered Moveworks Compound Action Assistant - Simplifying workflow creation 
                and YAML generation for Moveworks developers.
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
