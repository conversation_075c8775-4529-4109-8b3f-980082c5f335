import { WorkflowDefinition } from '../types/moveworks';
export declare class YamlGeneratorService {
    generateYaml(workflow: WorkflowDefinition, includeComments?: boolean): string;
    private convertWorkflowToCompoundAction;
    private orderStepsByDependencies;
    private convertStepToExpression;
    private createActionExpression;
    private createScriptExpression;
    private createSwitchExpression;
    private createForExpression;
    private createParallelExpression;
    private createTryCatchExpression;
    private createRaiseExpression;
    private createReturnExpression;
    private cleanInputArgs;
    private generateYamlWithComments;
    calculateComplexityScore(workflow: WorkflowDefinition): number;
    estimateExecutionTime(workflow: WorkflowDefinition): number;
}
//# sourceMappingURL=yamlGenerator.d.ts.map