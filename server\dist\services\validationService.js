"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationService = void 0;
const yaml = __importStar(require("js-yaml"));
class ValidationService {
    MOVEWORKS_BUILTIN_ACTIONS = [
        'mw.get_user_by_email',
        'mw.batch_get_users_by_email',
        'mw.send_plaintext_chat_notification',
        'mw.create_generic_approval_request',
        'mw.get_approval_request_status',
        'mw.send_email',
        'mw.create_ticket',
        'mw.update_ticket',
        'mw.get_ticket_status',
        'mw.generate_structured_value_action',
        'mw.generate_text_action'
    ];
    DSL_FUNCTIONS = [
        '$CONCAT',
        '$MAP',
        '$LOWERCASE',
        '$UPPERCASE',
        '$TITLECASE',
        '$TRIM',
        '$SPLIT',
        '$JOIN',
        '$REPLACE',
        '$SUBSTRING',
        '$LENGTH',
        '$CONTAINS',
        '$STARTSWITH',
        '$ENDSWITH'
    ];
    validateWorkflow(workflow) {
        const errors = [];
        const warnings = [];
        this.validateWorkflowStructure(workflow, errors);
        this.validateSteps(workflow.steps, errors, warnings);
        this.validateOutputKeyUniqueness(workflow.steps, errors);
        this.validateDataReferences(workflow.steps, errors, warnings);
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    validateYamlSyntax(yamlContent) {
        const errors = [];
        const warnings = [];
        try {
            const parsed = yaml.load(yamlContent);
            this.validateCompoundActionStructure(parsed, errors);
        }
        catch (error) {
            errors.push({
                type: 'syntax',
                message: `YAML syntax error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                line: this.extractLineNumber(error)
            });
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    validateWorkflowStructure(workflow, errors) {
        if (!workflow.id) {
            errors.push({
                type: 'structure',
                message: 'Workflow must have an ID',
                path: 'id'
            });
        }
        if (!workflow.name || workflow.name.trim().length === 0) {
            errors.push({
                type: 'structure',
                message: 'Workflow must have a name',
                path: 'name'
            });
        }
        if (!workflow.steps || !Array.isArray(workflow.steps)) {
            errors.push({
                type: 'structure',
                message: 'Workflow must have a steps array',
                path: 'steps'
            });
        }
        if (workflow.steps && workflow.steps.length === 0) {
            errors.push({
                type: 'structure',
                message: 'Workflow must have at least one step',
                path: 'steps'
            });
        }
    }
    validateSteps(steps, errors, warnings) {
        steps.forEach((step, index) => {
            const stepPath = `steps[${index}]`;
            if (!step.id) {
                errors.push({
                    type: 'structure',
                    message: 'Step must have an ID',
                    path: `${stepPath}.id`
                });
            }
            if (!step.type) {
                errors.push({
                    type: 'structure',
                    message: 'Step must have a type',
                    path: `${stepPath}.type`
                });
            }
            this.validateStepByType(step, stepPath, errors, warnings);
        });
    }
    validateStepByType(step, stepPath, errors, warnings) {
        switch (step.type) {
            case 'action':
                this.validateActionStep(step, stepPath, errors, warnings);
                break;
            case 'script':
                this.validateScriptStep(step, stepPath, errors, warnings);
                break;
            case 'switch':
                this.validateSwitchStep(step, stepPath, errors, warnings);
                break;
            case 'for':
                this.validateForStep(step, stepPath, errors, warnings);
                break;
            case 'parallel':
                this.validateParallelStep(step, stepPath, errors, warnings);
                break;
            case 'try_catch':
                this.validateTryCatchStep(step, stepPath, errors, warnings);
                break;
            case 'raise':
                this.validateRaiseStep(step, stepPath, errors, warnings);
                break;
            case 'return':
                this.validateReturnStep(step, stepPath, errors, warnings);
                break;
            default:
                errors.push({
                    type: 'structure',
                    message: `Unknown step type: ${step.type}`,
                    path: `${stepPath}.type`
                });
        }
    }
    validateActionStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        if (!config.action_name) {
            errors.push({
                type: 'structure',
                message: 'Action step must have action_name',
                path: `${stepPath}.config.action_name`
            });
        }
        if (config.action_name && config.action_name.startsWith('mw.')) {
            if (!this.MOVEWORKS_BUILTIN_ACTIONS.includes(config.action_name)) {
                warnings.push({
                    type: 'best_practice',
                    message: `Unknown Moveworks built-in action: ${config.action_name}`,
                    path: `${stepPath}.config.action_name`,
                    suggestion: 'Verify the action name against the Moveworks documentation'
                });
            }
        }
        if (config.input_args && typeof config.input_args !== 'object') {
            errors.push({
                type: 'type',
                message: 'input_args must be an object',
                path: `${stepPath}.config.input_args`
            });
        }
        if (config.input_args) {
            this.validateDSLExpressions(config.input_args, `${stepPath}.config.input_args`, errors, warnings);
        }
    }
    validateScriptStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        if (!config.code) {
            errors.push({
                type: 'structure',
                message: 'Script step must have code',
                path: `${stepPath}.config.code`
            });
        }
        if (config.code) {
            this.validateAPIthonCode(config.code, stepPath, errors, warnings);
        }
    }
    validateAPIthonCode(code, stepPath, errors, warnings) {
        if (Buffer.byteLength(code, 'utf8') > 4096) {
            errors.push({
                type: 'structure',
                message: 'APIthon code exceeds 4096 byte limit',
                path: `${stepPath}.config.code`
            });
        }
        if (code.includes('import ') || code.includes('from ')) {
            errors.push({
                type: 'structure',
                message: 'APIthon code cannot contain import statements',
                path: `${stepPath}.config.code`
            });
        }
        if (code.includes('class ')) {
            errors.push({
                type: 'structure',
                message: 'APIthon code cannot contain class definitions',
                path: `${stepPath}.config.code`
            });
        }
        if (!code.includes('return ') && !code.trim().endsWith('}')) {
            warnings.push({
                type: 'best_practice',
                message: 'APIthon code should end with a return statement or expression',
                path: `${stepPath}.config.code`,
                suggestion: 'Add a return statement or ensure the last line is an expression'
            });
        }
    }
    validateSwitchStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        if (!config.cases || !Array.isArray(config.cases)) {
            errors.push({
                type: 'structure',
                message: 'Switch step must have cases array',
                path: `${stepPath}.config.cases`
            });
            return;
        }
        if (config.cases.length === 0) {
            warnings.push({
                type: 'best_practice',
                message: 'Switch step should have at least one case',
                path: `${stepPath}.config.cases`,
                suggestion: 'Add at least one case or consider using a different step type'
            });
        }
        config.cases.forEach((caseConfig, index) => {
            const casePath = `${stepPath}.config.cases[${index}]`;
            if (!caseConfig.condition) {
                errors.push({
                    type: 'structure',
                    message: 'Switch case must have a condition',
                    path: `${casePath}.condition`
                });
            }
            if (!caseConfig.steps || !Array.isArray(caseConfig.steps)) {
                errors.push({
                    type: 'structure',
                    message: 'Switch case must have steps array',
                    path: `${casePath}.steps`
                });
            }
        });
        if (config.default) {
            if (!config.default.steps || !Array.isArray(config.default.steps)) {
                errors.push({
                    type: 'structure',
                    message: 'Switch default must have steps array',
                    path: `${stepPath}.config.default.steps`
                });
            }
        }
    }
    validateForStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        if (!config.each) {
            errors.push({
                type: 'structure',
                message: 'For loop must have "each" variable name',
                path: `${stepPath}.config.each`
            });
        }
        if (!config.in) {
            errors.push({
                type: 'structure',
                message: 'For loop must have "in" data reference',
                path: `${stepPath}.config.in`
            });
        }
        if (config.in && typeof config.in === 'string' && !config.in.includes('[]') && !config.in.includes('items')) {
            warnings.push({
                type: 'best_practice',
                message: 'For loop "in" should reference an array or list',
                path: `${stepPath}.config.in`,
                suggestion: 'Ensure the referenced data is an array or list'
            });
        }
        if (!config.steps || !Array.isArray(config.steps)) {
            errors.push({
                type: 'structure',
                message: 'For loop must have steps array',
                path: `${stepPath}.config.steps`
            });
        }
    }
    validateParallelStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        const hasBranches = config.branches && Array.isArray(config.branches);
        const hasFor = config.for && typeof config.for === 'object';
        if (!hasBranches && !hasFor) {
            errors.push({
                type: 'structure',
                message: 'Parallel step must have either branches or for configuration',
                path: `${stepPath}.config`
            });
            return;
        }
        if (hasBranches && hasFor) {
            errors.push({
                type: 'structure',
                message: 'Parallel step cannot have both branches and for configuration',
                path: `${stepPath}.config`
            });
            return;
        }
        if (hasBranches && config.branches.length < 2) {
            warnings.push({
                type: 'performance',
                message: 'Parallel branches should have at least 2 branches for meaningful parallelism',
                path: `${stepPath}.config.branches`,
                suggestion: 'Add more branches or consider using sequential steps'
            });
        }
        if (hasFor) {
            if (!config.for.each) {
                errors.push({
                    type: 'structure',
                    message: 'Parallel for must have "each" variable name',
                    path: `${stepPath}.config.for.each`
                });
            }
            if (!config.for.in) {
                errors.push({
                    type: 'structure',
                    message: 'Parallel for must have "in" data reference',
                    path: `${stepPath}.config.for.in`
                });
            }
        }
    }
    validateTryCatchStep(step, stepPath, errors, _warnings) {
        const config = step.config || {};
        if (!config.try || !config.try.steps || !Array.isArray(config.try.steps)) {
            errors.push({
                type: 'structure',
                message: 'Try-catch must have try block with steps array',
                path: `${stepPath}.config.try.steps`
            });
        }
        if (!config.catch || !config.catch.steps || !Array.isArray(config.catch.steps)) {
            errors.push({
                type: 'structure',
                message: 'Try-catch must have catch block with steps array',
                path: `${stepPath}.config.catch.steps`
            });
        }
        if (config.catch?.on_status_code) {
            const statusCodes = Array.isArray(config.catch.on_status_code)
                ? config.catch.on_status_code
                : [config.catch.on_status_code];
            statusCodes.forEach((code, index) => {
                if (typeof code !== 'string' && typeof code !== 'number') {
                    errors.push({
                        type: 'type',
                        message: 'Status codes must be strings or numbers',
                        path: `${stepPath}.config.catch.on_status_code[${index}]`
                    });
                }
            });
        }
    }
    validateRaiseStep(step, stepPath, _errors, warnings) {
        const config = step.config || {};
        if (!config.message) {
            warnings.push({
                type: 'best_practice',
                message: 'Raise step should have a descriptive message',
                path: `${stepPath}.config.message`,
                suggestion: 'Add a message to help with debugging'
            });
        }
    }
    validateReturnStep(step, stepPath, errors, warnings) {
        const config = step.config || {};
        if (config.value && typeof config.value === 'object') {
            this.validateDSLExpressions(config.value, `${stepPath}.config.value`, errors, warnings);
        }
    }
    validateDSLExpressions(obj, path, errors, warnings) {
        if (typeof obj === 'string' && obj.startsWith('$')) {
            const functionName = obj.split('(')[0];
            if (!this.DSL_FUNCTIONS.includes(functionName)) {
                warnings.push({
                    type: 'best_practice',
                    message: `Unknown DSL function: ${functionName}`,
                    path,
                    suggestion: 'Verify the DSL function name against Moveworks documentation'
                });
            }
            if (!obj.includes('(') || !obj.includes(')')) {
                errors.push({
                    type: 'syntax',
                    message: 'DSL function must have parentheses',
                    path
                });
            }
        }
        else if (typeof obj === 'object' && obj !== null) {
            Object.entries(obj).forEach(([key, value]) => {
                this.validateDSLExpressions(value, `${path}.${key}`, errors, warnings);
            });
        }
    }
    validateOutputKeyUniqueness(steps, errors) {
        const outputKeys = new Set();
        steps.forEach((step, index) => {
            const outputKey = step.config?.output_key || step.id;
            if (outputKey && outputKey !== '_') {
                if (outputKeys.has(outputKey)) {
                    errors.push({
                        type: 'structure',
                        message: `Duplicate output_key: ${outputKey}`,
                        path: `steps[${index}].config.output_key`
                    });
                }
                else {
                    outputKeys.add(outputKey);
                }
            }
        });
    }
    validateDataReferences(steps, errors, warnings) {
        const availableKeys = new Set(['requestor', 'mw']);
        steps.forEach((step, index) => {
            const stepPath = `steps[${index}]`;
            const config = step.config || {};
            if (config.input_args) {
                this.validateDataReferencesInObject(config.input_args, availableKeys, `${stepPath}.config.input_args`, errors, warnings);
            }
            const outputKey = config.output_key || step.id;
            if (outputKey && outputKey !== '_') {
                availableKeys.add(outputKey);
            }
        });
    }
    validateDataReferencesInObject(obj, availableKeys, path, errors, warnings) {
        if (typeof obj === 'string' && obj.startsWith('data.')) {
            const keyPath = obj.substring(5);
            const rootKey = keyPath.split('.')[0];
            if (rootKey && !availableKeys.has(rootKey)) {
                errors.push({
                    type: 'reference',
                    message: `Reference to undefined data key: ${obj}`,
                    path
                });
            }
        }
        else if (typeof obj === 'object' && obj !== null) {
            Object.entries(obj).forEach(([key, value]) => {
                this.validateDataReferencesInObject(value, availableKeys, `${path}.${key}`, errors, warnings);
            });
        }
    }
    validateCompoundActionStructure(compoundAction, errors) {
        if (!compoundAction || typeof compoundAction !== 'object') {
            errors.push({
                type: 'structure',
                message: 'Root must be an object',
                path: 'root'
            });
            return;
        }
        if (!compoundAction.steps || !Array.isArray(compoundAction.steps)) {
            errors.push({
                type: 'structure',
                message: 'Root must have a steps array',
                path: 'steps'
            });
        }
    }
    extractLineNumber(error) {
        if (error && error.mark && typeof error.mark.line === 'number') {
            return error.mark.line + 1;
        }
        return undefined;
    }
}
exports.ValidationService = ValidationService;
//# sourceMappingURL=validationService.js.map