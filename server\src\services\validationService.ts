/**
 * Validation Service
 * Validates Moveworks Compound Action YAML and workflow definitions
 */

import * as yaml from 'js-yaml';
import {
  WorkflowDefinition,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  CompoundAction
} from '../types/moveworks';

export class ValidationService {
  private readonly MOVEWORKS_BUILTIN_ACTIONS = [
    'mw.get_user_by_email',
    'mw.send_plaintext_chat_notification',
    'mw.create_generic_approval_request',
    'mw.get_approval_request_status',
    'mw.send_email',
    'mw.create_ticket',
    'mw.update_ticket',
    'mw.get_ticket_status'
  ];

  /**
   * Validate a workflow definition
   */
  public validateWorkflow(workflow: WorkflowDefinition): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Basic structure validation
    this.validateWorkflowStructure(workflow, errors);

    // Step validation
    this.validateSteps(workflow.steps, errors, warnings);

    // Output key uniqueness
    this.validateOutputKeyUniqueness(workflow.steps, errors);

    // Data reference validation
    this.validateDataReferences(workflow.steps, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate generated YAML syntax
   */
  public validateYamlSyntax(yamlContent: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      const parsed = yaml.load(yamlContent) as CompoundAction;

      // Validate the parsed structure
      this.validateCompoundActionStructure(parsed, errors);

    } catch (error) {
      errors.push({
        type: 'syntax',
        message: `YAML syntax error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        line: this.extractLineNumber(error)
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate workflow basic structure
   */
  private validateWorkflowStructure(workflow: WorkflowDefinition, errors: ValidationError[]): void {
    if (!workflow.id) {
      errors.push({
        type: 'structure',
        message: 'Workflow must have an ID',
        path: 'id'
      });
    }

    if (!workflow.name || workflow.name.trim().length === 0) {
      errors.push({
        type: 'structure',
        message: 'Workflow must have a name',
        path: 'name'
      });
    }

    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      errors.push({
        type: 'structure',
        message: 'Workflow must have a steps array',
        path: 'steps'
      });
    }

    if (workflow.steps && workflow.steps.length === 0) {
      errors.push({
        type: 'structure',
        message: 'Workflow must have at least one step',
        path: 'steps'
      });
    }
  }

  /**
   * Validate individual workflow steps
   */
  private validateSteps(steps: any[], errors: ValidationError[], warnings: ValidationWarning[]): void {
    steps.forEach((step, index) => {
      const stepPath = `steps[${index}]`;

      // Required fields
      if (!step.id) {
        errors.push({
          type: 'structure',
          message: 'Step must have an ID',
          path: `${stepPath}.id`
        });
      }

      if (!step.type) {
        errors.push({
          type: 'structure',
          message: 'Step must have a type',
          path: `${stepPath}.type`
        });
      }

      // Type-specific validation
      this.validateStepByType(step, stepPath, errors, warnings);
    });
  }

  /**
   * Validate step based on its type
   */
  private validateStepByType(
    step: any,
    stepPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    switch (step.type) {
      case 'action':
        this.validateActionStep(step, stepPath, errors, warnings);
        break;
      case 'script':
        this.validateScriptStep(step, stepPath, errors, warnings);
        break;
      default:
        if (!['switch', 'for', 'parallel', 'try_catch', 'raise', 'return'].includes(step.type)) {
          errors.push({
            type: 'structure',
            message: `Unknown step type: ${step.type}`,
            path: `${stepPath}.type`
          });
        }
    }
  }

  /**
   * Validate action step configuration
   */
  private validateActionStep(
    step: any,
    stepPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    const config = step.config || {};

    if (!config.action_name) {
      errors.push({
        type: 'structure',
        message: 'Action step must have action_name',
        path: `${stepPath}.config.action_name`
      });
    }

    // Validate Moveworks built-in actions
    if (config.action_name && config.action_name.startsWith('mw.')) {
      if (!this.MOVEWORKS_BUILTIN_ACTIONS.includes(config.action_name)) {
        warnings.push({
          type: 'best_practice',
          message: `Unknown Moveworks built-in action: ${config.action_name}`,
          path: `${stepPath}.config.action_name`,
          suggestion: 'Verify the action name against the Moveworks documentation'
        });
      }
    }

    // Validate input_args structure
    if (config.input_args && typeof config.input_args !== 'object') {
      errors.push({
        type: 'type',
        message: 'input_args must be an object',
        path: `${stepPath}.config.input_args`
      });
    }
  }

  /**
   * Validate script step configuration
   */
  private validateScriptStep(
    step: any,
    stepPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    const config = step.config || {};

    if (!config.code) {
      errors.push({
        type: 'structure',
        message: 'Script step must have code',
        path: `${stepPath}.config.code`
      });
    }

    // APIthon constraints validation
    if (config.code) {
      this.validateAPIthonCode(config.code, stepPath, errors, warnings);
    }
  }

  /**
   * Validate APIthon code constraints
   */
  private validateAPIthonCode(
    code: string,
    stepPath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    // Size limit check (4096 bytes)
    if (Buffer.byteLength(code, 'utf8') > 4096) {
      errors.push({
        type: 'structure',
        message: 'APIthon code exceeds 4096 byte limit',
        path: `${stepPath}.config.code`
      });
    }

    // Check for prohibited imports
    if (code.includes('import ') || code.includes('from ')) {
      errors.push({
        type: 'structure',
        message: 'APIthon code cannot contain import statements',
        path: `${stepPath}.config.code`
      });
    }

    // Check for class definitions
    if (code.includes('class ')) {
      errors.push({
        type: 'structure',
        message: 'APIthon code cannot contain class definitions',
        path: `${stepPath}.config.code`
      });
    }

    // Warn about return statement
    if (!code.includes('return ') && !code.trim().endsWith('}')) {
      warnings.push({
        type: 'best_practice',
        message: 'APIthon code should end with a return statement or expression',
        path: `${stepPath}.config.code`,
        suggestion: 'Add a return statement or ensure the last line is an expression'
      });
    }
  }

  /**
   * Validate output key uniqueness
   */
  private validateOutputKeyUniqueness(steps: any[], errors: ValidationError[]): void {
    const outputKeys = new Set<string>();

    steps.forEach((step, index) => {
      const outputKey = step.config?.output_key || step.id;

      if (outputKey && outputKey !== '_') {
        if (outputKeys.has(outputKey)) {
          errors.push({
            type: 'structure',
            message: `Duplicate output_key: ${outputKey}`,
            path: `steps[${index}].config.output_key`
          });
        } else {
          outputKeys.add(outputKey);
        }
      }
    });
  }

  /**
   * Validate data references in input_args
   */
  private validateDataReferences(
    steps: any[],
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    const availableKeys = new Set<string>(['requestor', 'mw']);

    steps.forEach((step, index) => {
      const stepPath = `steps[${index}]`;
      const config = step.config || {};

      // Check input_args for data references
      if (config.input_args) {
        this.validateDataReferencesInObject(
          config.input_args,
          availableKeys,
          `${stepPath}.config.input_args`,
          errors,
          warnings
        );
      }

      // Add this step's output_key to available keys for subsequent steps
      const outputKey = config.output_key || step.id;
      if (outputKey && outputKey !== '_') {
        availableKeys.add(outputKey);
      }
    });
  }

  /**
   * Recursively validate data references in an object
   */
  private validateDataReferencesInObject(
    obj: any,
    availableKeys: Set<string>,
    path: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (typeof obj === 'string' && obj.startsWith('data.')) {
      const keyPath = obj.substring(5); // Remove 'data.' prefix
      const rootKey = keyPath.split('.')[0];

      if (rootKey && !availableKeys.has(rootKey)) {
        errors.push({
          type: 'reference',
          message: `Reference to undefined data key: ${obj}`,
          path
        });
      }
    } else if (typeof obj === 'object' && obj !== null) {
      Object.entries(obj).forEach(([key, value]) => {
        this.validateDataReferencesInObject(
          value,
          availableKeys,
          `${path}.${key}`,
          errors,
          warnings
        );
      });
    }
  }

  /**
   * Validate compound action structure
   */
  private validateCompoundActionStructure(compoundAction: any, errors: ValidationError[]): void {
    if (!compoundAction || typeof compoundAction !== 'object') {
      errors.push({
        type: 'structure',
        message: 'Root must be an object',
        path: 'root'
      });
      return;
    }

    if (!compoundAction.steps || !Array.isArray(compoundAction.steps)) {
      errors.push({
        type: 'structure',
        message: 'Root must have a steps array',
        path: 'steps'
      });
    }
  }

  /**
   * Extract line number from YAML parsing error
   */
  private extractLineNumber(error: any): number | undefined {
    if (error && error.mark && typeof error.mark.line === 'number') {
      return error.mark.line + 1; // Convert to 1-based line numbers
    }
    return undefined;
  }
}
