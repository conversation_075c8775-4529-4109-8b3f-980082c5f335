import { Routes, Route } from 'react-router-dom'
import { ReactFlowProvider } from 'reactflow'
import Layout from './components/Layout'
import WorkflowBuilder from './pages/WorkflowBuilder'
import Dashboard from './pages/Dashboard'
import Documentation from './pages/Documentation'
import Settings from './pages/Settings'
import 'reactflow/dist/style.css'

function App() {
  return (
    <ReactFlowProvider>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/builder" element={<WorkflowBuilder />} />
          <Route path="/builder/:workflowId" element={<WorkflowBuilder />} />
          <Route path="/docs" element={<Documentation />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </ReactFlowProvider>
  )
}

export default App
