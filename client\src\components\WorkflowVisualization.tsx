/**
 * Workflow Visualization Component
 * Shows visual representation of workflow with data flow
 */

import { useCallback, useEffect, useState } from 'react'
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  NodeTypes,
  Handle,
  Position
} from 'reactflow'
import { AlertTriangle, Database, Zap, Code, GitBranch, RotateCcw, AlertCircle } from 'lucide-react'
import { WorkflowDefinition } from '@/types'
import { enhancedValidator, ValidationResult } from '@/services/enhancedValidation'
import { cn } from '@/lib/utils'

interface WorkflowVisualizationProps {
  workflow: WorkflowDefinition
  onStepSelect?: (stepId: string) => void
  selectedStepId?: string
}

// Custom node component for workflow steps
const StepNode = ({ data }: { data: any }) => {
  const { step, isSelected, hasErrors, hasWarnings, onSelect } = data

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'action': return <Zap className="h-4 w-4" />
      case 'script': return <Code className="h-4 w-4" />
      case 'switch': return <GitBranch className="h-4 w-4" />
      case 'for': return <RotateCcw className="h-4 w-4" />
      case 'parallel': return <Database className="h-4 w-4" />
      case 'try_catch': return <AlertCircle className="h-4 w-4" />
      default: return <Database className="h-4 w-4" />
    }
  }

  const getStepColor = (type: string) => {
    switch (type) {
      case 'action': return 'bg-blue-50 border-blue-200 text-blue-900'
      case 'script': return 'bg-yellow-50 border-yellow-200 text-yellow-900'
      case 'switch': return 'bg-purple-50 border-purple-200 text-purple-900'
      case 'for': return 'bg-green-50 border-green-200 text-green-900'
      case 'parallel': return 'bg-indigo-50 border-indigo-200 text-indigo-900'
      case 'try_catch': return 'bg-red-50 border-red-200 text-red-900'
      default: return 'bg-gray-50 border-gray-200 text-gray-900'
    }
  }

  return (
    <div
      onClick={() => onSelect(step.id)}
      className={cn(
        'px-4 py-3 rounded-lg border-2 cursor-pointer transition-all min-w-[200px]',
        getStepColor(step.type),
        isSelected ? 'ring-2 ring-primary-500 ring-offset-2' : '',
        hasErrors ? 'border-red-400' : hasWarnings ? 'border-yellow-400' : ''
      )}
    >
      <Handle type="target" position={Position.Top} className="w-3 h-3" />

      <div className="flex items-center space-x-2 mb-2">
        {getStepIcon(step.type)}
        <span className="font-medium text-sm">
          {step.type === 'action' ? step.config?.action_name?.split('.').pop() : step.type}
        </span>
        {hasErrors && <AlertTriangle className="h-4 w-4 text-red-500" />}
        {hasWarnings && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
      </div>

      {step.config?.output_key && (
        <div className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
          → {step.config.output_key}
        </div>
      )}

      {step.type === 'action' && step.config?.action_name && (
        <div className="text-xs text-gray-600 mt-1 truncate">
          {step.config.action_name}
        </div>
      )}

      <Handle type="source" position={Position.Bottom} className="w-3 h-3" />
    </div>
  )
}

// Data flow edge component (currently unused but kept for future use)
// const DataFlowEdge = ({ data }: { data: any }) => {
//   const { dataKey, isHighlighted } = data
//
//   return (
//     <div className={cn(
//       'text-xs px-2 py-1 rounded bg-white border shadow-sm',
//       isHighlighted ? 'bg-blue-50 border-blue-200 text-blue-900' : 'bg-gray-50 border-gray-200'
//     )}>
//       {dataKey}
//     </div>
//   )
// }

const nodeTypes: NodeTypes = {
  stepNode: StepNode
}

export default function WorkflowVisualization({
  workflow,
  onStepSelect,
  selectedStepId
}: WorkflowVisualizationProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [highlightedDataFlow] = useState<string | null>(null)

  // Generate nodes and edges from workflow
  useEffect(() => {
    if (!workflow?.steps) return

    // Validate workflow
    const validation = enhancedValidator.validateWorkflow(workflow)
    setValidationResult(validation)

    // Create nodes
    const newNodes: Node[] = workflow.steps.map((step, index) => {
      const stepErrors = validation.errors.filter(e => e.stepId === step.id)
      const stepWarnings = validation.warnings.filter(w => w.stepId === step.id)

      return {
        id: step.id,
        type: 'stepNode',
        position: step.position || { x: 100 + (index % 3) * 250, y: 100 + Math.floor(index / 3) * 150 },
        data: {
          step,
          isSelected: selectedStepId === step.id,
          hasErrors: stepErrors.length > 0,
          hasWarnings: stepWarnings.length > 0,
          onSelect: onStepSelect || (() => {})
        }
      }
    })

    // Create edges based on data flow
    const newEdges: Edge[] = []
    const dataOutputs = new Map<string, string>() // outputKey -> stepId

    // First pass: collect all data outputs
    workflow.steps.forEach(step => {
      if (step.config?.output_key) {
        dataOutputs.set(step.config.output_key, step.id)
      }
    })

    // Second pass: create edges for data dependencies
    workflow.steps.forEach((step, index) => {
      const stepConfig = JSON.stringify(step.config || {})

      // Find data references in this step
      dataOutputs.forEach((sourceStepId, outputKey) => {
        if (stepConfig.includes(outputKey) && sourceStepId !== step.id) {
          newEdges.push({
            id: `${sourceStepId}-${step.id}-${outputKey}`,
            source: sourceStepId,
            target: step.id,
            type: 'smoothstep',
            animated: highlightedDataFlow === outputKey,
            style: {
              stroke: highlightedDataFlow === outputKey ? '#3b82f6' : '#6b7280',
              strokeWidth: highlightedDataFlow === outputKey ? 3 : 2
            },
            label: outputKey,
            labelStyle: {
              fontSize: 12,
              fontWeight: 500,
              fill: highlightedDataFlow === outputKey ? '#3b82f6' : '#6b7280'
            }
          })
        }
      })

      // Add sequential edges for steps without explicit data dependencies
      if (index > 0 && !newEdges.some(edge => edge.target === step.id)) {
        const previousStep = workflow.steps[index - 1]
        newEdges.push({
          id: `seq-${previousStep.id}-${step.id}`,
          source: previousStep.id,
          target: step.id,
          type: 'smoothstep',
          style: {
            stroke: '#d1d5db',
            strokeWidth: 1,
            strokeDasharray: '5,5'
          }
        })
      }
    })

    setNodes(newNodes)
    setEdges(newEdges)
  }, [workflow, selectedStepId, highlightedDataFlow])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const handleNodeClick = useCallback((_event: React.MouseEvent, node: Node) => {
    if (onStepSelect) {
      onStepSelect(node.id)
    }
  }, [onStepSelect])

  // const handleDataFlowHighlight = (outputKey: string | null) => {
  //   setHighlightedDataFlow(outputKey)
  // }

  return (
    <div className="h-full w-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={handleNodeClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <MiniMap
          nodeStrokeColor="#374151"
          nodeColor="#f3f4f6"
          nodeBorderRadius={8}
        />
        <Background gap={20} size={1} />
      </ReactFlow>

      {/* Validation Panel */}
      {validationResult && (validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
        <div className="absolute top-4 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-h-96 overflow-y-auto">
          <h3 className="font-medium text-gray-900 mb-3">Validation Results</h3>

          {validationResult.errors.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-red-700 mb-2">Errors</h4>
              <div className="space-y-2">
                {validationResult.errors.slice(0, 5).map(error => (
                  <div key={error.id} className="text-xs bg-red-50 border border-red-200 rounded p-2">
                    <div className="font-medium text-red-800">{error.message}</div>
                    {error.stepId && (
                      <div className="text-red-600 mt-1">Step: {error.stepId}</div>
                    )}
                  </div>
                ))}
                {validationResult.errors.length > 5 && (
                  <div className="text-xs text-gray-500">
                    +{validationResult.errors.length - 5} more errors
                  </div>
                )}
              </div>
            </div>
          )}

          {validationResult.warnings.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-yellow-700 mb-2">Warnings</h4>
              <div className="space-y-2">
                {validationResult.warnings.slice(0, 3).map(warning => (
                  <div key={warning.id} className="text-xs bg-yellow-50 border border-yellow-200 rounded p-2">
                    <div className="font-medium text-yellow-800">{warning.message}</div>
                    <div className="text-yellow-600 mt-1">Impact: {warning.impact}</div>
                  </div>
                ))}
                {validationResult.warnings.length > 3 && (
                  <div className="text-xs text-gray-500">
                    +{validationResult.warnings.length - 3} more warnings
                  </div>
                )}
              </div>
            </div>
          )}

          {validationResult.suggestions.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-blue-700 mb-2">Suggestions</h4>
              <div className="space-y-2">
                {validationResult.suggestions.slice(0, 3).map(suggestion => (
                  <div key={suggestion.id} className="text-xs bg-blue-50 border border-blue-200 rounded p-2">
                    <div className="font-medium text-blue-800">{suggestion.message}</div>
                    <button className="text-blue-600 hover:text-blue-800 mt-1 text-xs underline">
                      {suggestion.action}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Data Flow Legend */}
      <div className="absolute bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Data Flow</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-gray-400"></div>
            <span>Data dependency</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-gray-300 border-dashed border-t"></div>
            <span>Sequential flow</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-0.5 bg-blue-500"></div>
            <span>Highlighted flow</span>
          </div>
        </div>
      </div>
    </div>
  )
}
