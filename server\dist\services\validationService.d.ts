import { WorkflowDefinition, ValidationResult } from '../types/moveworks';
export declare class ValidationService {
    private readonly MOVEWORKS_BUILTIN_ACTIONS;
    private readonly DSL_FUNCTIONS;
    validateWorkflow(workflow: WorkflowDefinition): ValidationResult;
    validateYamlSyntax(yamlContent: string): ValidationResult;
    private validateWorkflowStructure;
    private validateSteps;
    private validateStepByType;
    private validateActionStep;
    private validateScriptStep;
    private validateAPIthonCode;
    private validateSwitchStep;
    private validateForStep;
    private validateParallelStep;
    private validateTryCatchStep;
    private validateRaiseStep;
    private validateReturnStep;
    private validateDSLExpressions;
    private validateOutputKeyUniqueness;
    private validateDataReferences;
    private validateDataReferencesInObject;
    private validateCompoundActionStructure;
    private extractLineNumber;
}
//# sourceMappingURL=validationService.d.ts.map