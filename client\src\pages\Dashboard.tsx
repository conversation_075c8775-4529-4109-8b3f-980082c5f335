import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
  Plus,
  Workflow,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash2,
  Edit,
  Copy,
  Download,
  Activity
} from 'lucide-react'
import { useWorkflowStore } from '@/store/workflowStore'
import { formatDate, copyToClipboard, downloadFile } from '@/lib/utils'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
import WorkflowTemplates from '@/components/WorkflowTemplates'

export default function Dashboard() {
  const {
    workflows,
    createWorkflow,
    deleteWorkflow,
    loadBuiltinActions,
    loadDSLFunctions,
    builtinActions,
    dslFunctions
  } = useWorkflowStore()

  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [newWorkflowName, setNewWorkflowName] = useState('')
  const [newWorkflowDescription, setNewWorkflowDescription] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([
          loadBuiltinActions(),
          loadDSLFunctions()
        ])
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
        toast.error('Failed to load some data. Please refresh the page.')
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [loadBuiltinActions, loadDSLFunctions])

  const handleCreateWorkflow = () => {
    if (!newWorkflowName.trim()) {
      toast.error('Workflow name is required')
      return
    }

    createWorkflow(newWorkflowName.trim(), newWorkflowDescription.trim() || undefined)
    setShowCreateModal(false)
    setNewWorkflowName('')
    setNewWorkflowDescription('')
    toast.success('Workflow created successfully')
  }

  const handleDeleteWorkflow = (id: string, name: string) => {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
      deleteWorkflow(id)
      toast.success('Workflow deleted successfully')
    }
  }

  const handleExportWorkflow = (workflow: any) => {
    const content = JSON.stringify(workflow, null, 2)
    downloadFile(content, `${workflow.name}.json`, 'application/json')
    toast.success('Workflow exported successfully')
  }

  const stats = [
    {
      name: 'Total Workflows',
      value: workflows.length,
      icon: Workflow,
      color: 'text-primary-600 bg-primary-100',
    },
    {
      name: 'Built-in Actions',
      value: builtinActions.length,
      icon: Activity,
      color: 'text-green-600 bg-green-100',
    },
    {
      name: 'DSL Functions',
      value: dslFunctions.length,
      icon: CheckCircle,
      color: 'text-blue-600 bg-blue-100',
    },
    {
      name: 'Recent Activity',
      value: workflows.filter(w => {
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
        return new Date(w.updated_at) > dayAgo
      }).length,
      icon: Clock,
      color: 'text-orange-600 bg-orange-100',
    },
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading-spinner h-8 w-8"></div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Dashboard</h1>
          <p className="text-secondary-600 mt-1">
            Manage your Moveworks Compound Action workflows
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowTemplates(true)}
            className="btn btn-outline btn-md"
          >
            <Workflow className="h-4 w-4 mr-2" />
            From Template
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary btn-md"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Workflow
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={cn("p-3 rounded-lg", stat.color)}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-secondary-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Workflows */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h2 className="card-title">Recent Workflows</h2>
            <Link to="/builder" className="btn btn-outline btn-sm">
              View All
            </Link>
          </div>
        </div>
        <div className="card-content">
          {workflows.length === 0 ? (
            <div className="text-center py-12">
              <Workflow className="mx-auto h-12 w-12 text-secondary-400" />
              <h3 className="mt-2 text-sm font-medium text-secondary-900">No workflows</h3>
              <p className="mt-1 text-sm text-secondary-500">
                Get started by creating a new workflow.
              </p>
              <div className="mt-6 flex space-x-3">
                <button
                  onClick={() => setShowTemplates(true)}
                  className="btn btn-outline btn-md"
                >
                  <Workflow className="h-4 w-4 mr-2" />
                  From Template
                </button>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="btn btn-primary btn-md"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Workflow
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {workflows.slice(0, 5).map((workflow) => (
                <div
                  key={workflow.id}
                  className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Workflow className="h-5 w-5 text-secondary-400" />
                      <div>
                        <h3 className="text-sm font-medium text-secondary-900">
                          {workflow.name}
                        </h3>
                        {workflow.description && (
                          <p className="text-sm text-secondary-500">
                            {workflow.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="mt-2 flex items-center space-x-4 text-xs text-secondary-500">
                      <span>{workflow.steps.length} steps</span>
                      <span>Updated {formatDate(new Date(workflow.updated_at))}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Link
                      to={`/builder/${workflow.id}`}
                      className="btn btn-ghost btn-sm"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <button
                      onClick={() => handleExportWorkflow(workflow)}
                      className="btn btn-ghost btn-sm"
                      title="Export workflow"
                    >
                      <Download className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteWorkflow(workflow.id, workflow.name)}
                      className="btn btn-ghost btn-sm text-error-600 hover:bg-error-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Workflow Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setShowCreateModal(false)} />
            <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="p-6">
                <h3 className="text-lg font-medium text-secondary-900 mb-4">
                  Create New Workflow
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">
                      Name *
                    </label>
                    <input
                      type="text"
                      value={newWorkflowName}
                      onChange={(e) => setNewWorkflowName(e.target.value)}
                      className="input w-full"
                      placeholder="Enter workflow name"
                      autoFocus
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={newWorkflowDescription}
                      onChange={(e) => setNewWorkflowDescription(e.target.value)}
                      className="textarea w-full"
                      placeholder="Enter workflow description (optional)"
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="btn btn-outline btn-md"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateWorkflow}
                    className="btn btn-primary btn-md"
                  >
                    Create Workflow
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Workflow Templates Modal */}
      {showTemplates && (
        <WorkflowTemplates onClose={() => setShowTemplates(false)} />
      )}
    </div>
  )
}
