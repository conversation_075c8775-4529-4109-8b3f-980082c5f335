{"ALL_COMPILER_OPTIONS_6917": "VŠECHNY MOŽNOSTI KOMPILÁTORU", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Modifikátor {0} nejde použít s deklarací import.", "A_0_parameter_must_be_the_first_parameter_2680": "Parametr {0} musí být prvním parametrem.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "<PERSON><PERSON><PERSON><PERSON> „@template“ jazyka JSDoc nemůže následovat po značce „@typedef“, „@callback“ nebo „@overload“.", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "<PERSON><PERSON><PERSON><PERSON> @typedef nemůže obsahovat více než jednu znač<PERSON> @type.", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "Liter<PERSON>l „bigint“ nelze použít jako název vlastnosti.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Literál typu bigint nemůže používat exponenciální notaci.", "A_bigint_literal_must_be_an_integer_1353": "Literál typu bigint musí být celé č<PERSON>lo.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Parametr vzoru vazby nemůže být u podpisu implementace nepovinný.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Příkaz break se dá použít jenom uvnitř nadřazené iterace nebo příkazu switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Příkaz break může skočit jenom na popisek nadřazeného příkazu.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "Třída znaků nesmí obsahovat vyhrazené dvojité interpunkční znaménko. Nechtěli jste ho uvést zpětným lomítkem?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "Rozsah tří<PERSON> znaků nesmí být ohraničen jinou třídou znaků.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "<PERSON><PERSON><PERSON>da může implementovat jenom identifikátor nebo kvalifikovaný název s volitelnými argumenty typu.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "<PERSON><PERSON><PERSON><PERSON> mů<PERSON>e implementovat jen typ objektu nebo průsečík typů objektů se <PERSON>ky známými členy.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "<PERSON><PERSON><PERSON><PERSON> nemů<PERSON>e rozšiřovat primitivní typ, jako je napřík<PERSON> „{0}“. <PERSON><PERSON><PERSON><PERSON> mů<PERSON> rozšiřovat pouze konstruovatelné hodnoty.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "<PERSON><PERSON><PERSON><PERSON> nemůže implementovat primitivní typ, jako je například „{0}“. M<PERSON>že implementovat pouze jiné typy pojmenovan<PERSON>ch objektů.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Deklarace tří<PERSON> bez modifikátoru default musí mít název.", "A_class_member_cannot_have_the_0_keyword_1248": "<PERSON><PERSON> třídy nemůže mít klíčové slovo {0}.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Výraz s čárkou není v názvu počítané vlastnosti povolený.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Název počítané vlastnosti nemůže odkazovat na parametr typu z jeho nadřazeného typu.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Název počítané vlastnosti v deklaraci vlastnosti třídy musí mít jednoduchý typ literálu nebo typ unique symbol.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Název počítané vlastnosti v přetížené metodě musí odkazovat na výraz, jehož typ je literál nebo unique symbol.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Název počítané vlastnosti v literálu typu musí odkazovat na výraz, jehož typ je literál nebo unique symbol.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Název počítané vlastnosti v ambientním kontextu musí odkazovat na výraz, jehož typ je literál nebo unique symbol.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Název počítané vlastnosti v rozhraní musí odkazovat na výraz, jehož typ je literál nebo unique symbol.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Název počí<PERSON>é vlastnosti musí být typu string, number, symbol nebo any.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Kontrolní výrazy const se dají p<PERSON>t jen pro odkazy na členy výčtu, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hodnot<PERSON>, pole nebo literá<PERSON> ob<PERSON>.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Ke členu konstantního výčtu se dá získat přístup jenom pomocí řetězcového literálu.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Inicializátor const v ambientním kontextu musí být řetězec, číselný literál nebo odkaz na výčet literálů.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Konstruktor nemůže obsahovat volání super, pokud jeho třída rozši<PERSON><PERSON><PERSON> null.", "A_constructor_cannot_have_a_this_parameter_2681": "Konstruktor nemůže mít parametr this.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Příkaz continue se dá použít jenom uvnitř příkazu nadřazené iterace.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Příkaz continue může přejít jenom na popisek příkazu nadřazené iterace.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "Soubor deklarace nelze importovat bez hodnoty „import type“. Nechtěli jste místo toho importovat soubor implementace „{0}“?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Modifikátor declare se nedá použít v kontextu, který už je ambientní.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "<PERSON><PERSON><PERSON><PERSON> mů<PERSON><PERSON> dekorovat jenom implementaci metody, ne přetížení.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Klauzule default nemůže být v příkazu switch víc než jednou.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "V modulu ve stylu ECMAScriptu se dá použít jenom výchozí export.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Výchozí export musí být na nejvyšší úrovni deklarace souboru nebo modulu.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Určitý kontrolní výraz přiřazení '!' není v tomto kontextu povolený.", "A_destructuring_declaration_must_have_an_initializer_1182": "Destrukturační deklarace musí obsahovat inicializátor.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "Volání dynamického importu v ES5 vyžaduje konstruktor „Promise“.  Ujistěte se, že máte deklaraci konstruktoru „Promise“, nebo do možnosti „--lib“ přidejte „ES2015“.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Volání <PERSON> importu vrací hodnotu ‚Promise‘. <PERSON>ji<PERSON><PERSON><PERSON>, že pro ni máte <PERSON>raci, nebo do možnosti ‚--lib‘ přidejte ‚ES2015‘.", "A_file_cannot_have_a_reference_to_itself_1006": "Soubor nemůže odkazovat sám na sebe.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "<PERSON><PERSON>, k<PERSON><PERSON> vrací hodnotu never, nem<PERSON><PERSON><PERSON> mít dosažitelný koncový bod.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Funkce volaná klíčovým slovem new nemůže mít typ this, k<PERSON><PERSON> je void.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> typ není „undefined“, „void“ ani „any“, musí vracet hodnotu.", "A_generator_cannot_have_a_void_type_annotation_2505": "<PERSON><PERSON><PERSON><PERSON> mít anotaci typu void.", "A_get_accessor_cannot_have_parameters_1054": "Přístupový objekt get nemůže obsahovat parametry.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Přístupový objekt get musí být alespoň tak přístupný jako metoda setter.", "A_get_accessor_must_return_a_value_2378": "Přístupový objekt get musí vracet hodnotu.", "A_label_is_not_allowed_here_1344": "Popisek se tady nepovoluje.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Element popsané řazené kolekce členů se deklaroval jako nepovinný pomocí otazníku za názvem a před dvojteč<PERSON>u, nikoli za typem.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Element popsané řazené kolekce členů se deklaroval jako zbytek s třemi teč<PERSON> (...) před názvem, nikoli před <PERSON>m.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Mapovaný typ nemůže deklarovat vlastnosti nebo metody.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Inicializátor členu v deklaraci výčtu nemůže odkazovat na členy deklarované až po výčtu, a to ani členy definované v jiných výčtech.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "T<PERSON><PERSON>da mixin musí mít konstruktor s jediným parametrem rest typu any[].", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "<PERSON><PERSON><PERSON><PERSON> mix<PERSON>, <PERSON><PERSON><PERSON> r<PERSON>ř<PERSON>je proměnnou typu obsahující signaturu abstraktn<PERSON><PERSON> kons<PERSON>, mus<PERSON> být tak<PERSON>á jako abstract.", "A_module_cannot_have_multiple_default_exports_2528": "Modul nemůže mít víc výchozích exportů.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Deklarace oboru názvů nemůže být v jiném souboru než třída nebo funkce, se kterou se slučuje.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "<PERSON><PERSON><PERSON> oboru n<PERSON>zvů nemůže být umístěná před třídou nebo funkcí, se kterou se slučuje.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Deklarace oboru názvů je povolená pouze na nejvyšší úrovni oboru názvů nebo v modulu.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "Deklarace namespace by se ne<PERSON><PERSON><PERSON> deklarovat pomocí klíčového slova module. Místo toho prosím použijte klíčové slovo namespace.", "A_non_dry_build_would_build_project_0_6357": "Build bez příznaku -dry by v<PERSON><PERSON><PERSON><PERSON> proje<PERSON> {0}.", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Build bez příznaku -dry by odstranil následující soubory: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Build bez příznaku -dry by akt<PERSON><PERSON><PERSON><PERSON> razítka pro výstup projektu {0}.", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Inicializátor parametru je povolený jenom v implementaci funkce nebo konstruktoru.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Vlastnost parametru se nedá deklarovat pomocí parametru rest.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Vlastnost parametru je povolená jenom v implementaci konstruktoru.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Vlastnost parametru se nedá deklarovat pomocí vzoru vazby.", "A_promise_must_have_a_then_method_1059": "Příslib musí mít metodu then.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Vlastnost třídy, <PERSON><PERSON><PERSON><PERSON> typ je unique symbol, mus<PERSON> být static a readonly.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Vlastnost rozhraní nebo literálu typu, jeh<PERSON><PERSON> typ je unique symbol, mus<PERSON> být readonly.", "A_required_element_cannot_follow_an_optional_element_1257": "Povinný element nemůže následovat po nepovinném elementu.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Povinný parametr nemůže následovat po nepovinném parametru.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Element rest nemůže obsahovat vzor vazby.", "A_rest_element_cannot_follow_another_rest_element_1265": "Element rest nemůže následovat za jiným elementem rest.", "A_rest_element_cannot_have_a_property_name_2566": "Element rest nemůže mít název vlastnosti.", "A_rest_element_cannot_have_an_initializer_1186": "Element rest nemůže obsahovat inicializátor.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Element rest musí být ve vzoru destrukturalizace poslední.", "A_rest_element_type_must_be_an_array_type_2574": "Typ elementu rest mus<PERSON> b<PERSON>t typu pole.", "A_rest_parameter_cannot_be_optional_1047": "Parametr rest nemůže být <PERSON>.", "A_rest_parameter_cannot_have_an_initializer_1048": "Parametr rest nemůže obsahovat inicializátor.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Parametr rest musí být posledním v seznamu parametrů.", "A_rest_parameter_must_be_of_an_array_type_2370": "Parametr rest mus<PERSON> b<PERSON><PERSON> typu pole.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Parametr rest nebo vzor vazby nesmí mít na konci čárku.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Příkaz return se dá použít jenom v těle funkce.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Příkaz return nejde použít uvnitř statického bloku třídy.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Řada z<PERSON>ů, k<PERSON><PERSON> mění mapování importů do umístění vyhledávání relativních vůči baseUrl.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Přístupový objekt set nemůže obsahovat anotaci návratového typu.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Přístupový objekt set nemůže obsahovat nepovinný parametr.", "A_set_accessor_cannot_have_rest_parameter_1053": "Přístupový objekt get nemůže obsahovat parametr rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Přístupov<PERSON> objekt set musí obsahovat přesně jeden parametr.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Parametr přístupového objektu set nemůže obsahovat inicializátor.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Argument rozprostření musí mít buď typ řazené kolekce členů, nebo musí být předán do parametru rest.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Volání super musí být příkaz na kořenové úrovni v konstruktoru odvozené třídy, který obsahuje inicializované vlastnosti, vlastnosti parametrů nebo privátní identifikátory.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Volání super musí být prvním příkazem v konstruktoru, který odkazuje na super nebo toto, k<PERSON>ž odvozená třída obsahuje inicializované vlastnosti, vlastnosti parametrů nebo soukromé identifikátory.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Ochrana typu this není kompatibilní s ochranou typu založeného na parametru.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Typ this je k dispozici jenom v nestatických členech třídy nebo rozhraní.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "Modifikátor „export“ nejvyšší úrovně nelze použít pro deklarace hodnot v modulu CommonJS, když je povolený modifikátor „verbatimModuleSyntax“.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Soubor tsconfig.json je už v {0} def<PERSON><PERSON><PERSON>.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Člen řazené kolekce členů nemůže být volitelný a zbytek.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Typ řazené kolekce členů není možné indexovat zápornou hodnotou.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Výraz potvrzení typu se na levé straně výrazu umocnění nepovoluje. Zvažte možnost uzavření výrazu do závorek.", "A_type_literal_property_cannot_have_an_initializer_1247": "Vlastnost literálu typu nemůže mít inicializátor.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "<PERSON><PERSON><PERSON>, p<PERSON><PERSON> k<PERSON> se importu<PERSON><PERSON> jen t<PERSON>, m<PERSON><PERSON><PERSON> ur<PERSON>t výchozí import nebo pojmenované vazby, ale ne obojí.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Predikát typu nemůže odkazovat na parametr rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Predikát typu nemůže odkazovat na element {0} ve vzoru vazby.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Predikát typu je povolený jenom na pozici návratového typu funkcí a metod.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Typ predikátu typu musí být přiřaditelný k typu jeho parametru.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Typ odkazovaný v dekorovaném podpisu musí být importován pomocí import type nebo importu oboru názvů, pokud jsou povoleny elementy isolatedModules a emitDecoratorMetadata.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> typ je unique symbol, musí být const.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Výraz yield je povolený jenom v těle generátoru.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "K abstraktní metodě {0} ve třídě {1} nejde získat přístup prostřednictvím výrazu super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Abstraktní metody se můžou vyskytovat jenom v abstraktní třídě.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "Abstraktní vlastnosti se můžou vyskytovat jenom v abstraktní třídě.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "K abstraktní vlastnosti {0} ve třídě {1} nelze získat přístup v konstruktoru.", "Accessibility_modifier_already_seen_1028": "Modifiká<PERSON> se už jednou vyskytl.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Přístupové objekty jsou dos<PERSON>, j<PERSON><PERSON> k<PERSON> je cílem ECMAScript 5 a vyšší verze.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Přistupující objekty musí být abstraktní nebo neabstraktní.", "Add_0_to_unresolved_variable_90008": "<PERSON><PERSON><PERSON><PERSON> {0}. k nerozpoznané proměnné", "Add_a_return_statement_95111": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> return", "Add_a_return_type_to_the_function_declaration_9031": "Přidejte návratový typ do deklarace funkce.", "Add_a_return_type_to_the_function_expression_9030": "Přidejte návratový typ do výrazu funkce.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "Přidejte návratový typ do deklarace přistupujícího objektu get.", "Add_a_return_type_to_the_method_9034": "Přidejte do metody návratový typ.", "Add_a_type_annotation_to_the_parameter_0_9028": "Přidejte anotaci typu k parametru „{0}“.", "Add_a_type_annotation_to_the_property_0_9029": "Přidejte anotaci typu k vlastnosti „{0}“.", "Add_a_type_annotation_to_the_variable_0_9027": "Přidejte anotaci typu k proměnn<PERSON> „{0}“.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "Přidejte typ k parametru deklarace přistupujícího objektu set.", "Add_all_missing_async_modifiers_95041": "Přidat všechny chybějící modifikátory async", "Add_all_missing_attributes_95168": "Přidat všechny chybějící atributy", "Add_all_missing_call_parentheses_95068": "Přidat všechny chybějící závorky volání", "Add_all_missing_function_declarations_95157": "Přidat všechny chybějící deklarace <PERSON>", "Add_all_missing_imports_95064": "Přidat všechny chybějící importy", "Add_all_missing_members_95022": "Přidat všechny chybějící členy", "Add_all_missing_override_modifiers_95162": "Přidat všechny chybějící modifikátory override", "Add_all_missing_parameters_95190": "Přidejte všechny chybějící parametry.", "Add_all_missing_properties_95166": "Přidat všechny chybějící importy", "Add_all_missing_return_statement_95114": "Přidat všechny chybějící příkazy return", "Add_all_missing_super_calls_95039": "Přidat všechna chybějící volání pomocí super", "Add_all_missing_type_annotations_90067": "Přidejte všechny chybějící anotace typů.", "Add_all_optional_parameters_95193": "Přidejte všechny volitelné parametry.", "Add_annotation_of_type_0_90062": "Přidejte anotaci typu „{0}“.", "Add_async_modifier_to_containing_function_90029": "Přidat modifik<PERSON>tor async do obsahující funkce", "Add_await_95083": "<PERSON><PERSON><PERSON><PERSON> await", "Add_await_to_initializer_for_0_95084": "Přidat await do inicializátoru pro {0}", "Add_await_to_initializers_95089": "Přidat await do inicializátorů", "Add_braces_to_arrow_function_95059": "Přidat složené závorky k funkci šipky", "Add_const_to_all_unresolved_variables_95082": "Přidat const ke všem nerozpoznaným proměnným", "Add_const_to_unresolved_variable_95081": "Přidat const k nerozpoznané proměnné", "Add_definite_assignment_assertion_to_property_0_95020": "Přidat kontrolní výraz jednoznačného přiřazení k vlastnosti {0}", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Přidat kontrolní výrazy jednoznačného přiřazení do všech neinicializovaných vlastností", "Add_export_to_make_this_file_into_a_module_95097": "Přidat export {}, aby se tento soubor převedl na modul", "Add_extends_constraint_2211": "Přidejte omezení extends.", "Add_extends_constraint_to_all_type_parameters_2212": "Přidat omezení extends ke všem parametrům typu", "Add_import_from_0_90057": "Přidat import z {0}", "Add_index_signature_for_property_0_90017": "Přidat signaturu indexu pro vlastnost {0}", "Add_initializer_to_property_0_95019": "Přidat inicializační výraz k vlastnosti {0}", "Add_initializers_to_all_uninitialized_properties_95027": "Přidat inicializátory do všech neinicializovaných vlastností", "Add_missing_attributes_95167": "Přidat chybějící atributy", "Add_missing_call_parentheses_95067": "Přidat chybějící závorky volání", "Add_missing_comma_for_object_member_completion_0_95187": "Přidejte chybějící čárku pro dokončování členů objektu „{0}“.", "Add_missing_enum_member_0_95063": "Přidat chybějící člen výčtu {0}", "Add_missing_function_declaration_0_95156": "P<PERSON><PERSON>t ch<PERSON>běj<PERSON><PERSON><PERSON> {0}", "Add_missing_new_operator_to_all_calls_95072": "Přidat chybějící operátor new ke všem voláním", "Add_missing_new_operator_to_call_95071": "Přidat chybějící operátor new k volání", "Add_missing_parameter_to_0_95188": "Přidejte chybějící parametr do „{0}“.", "Add_missing_parameters_to_0_95189": "Přidejte chybějící parametry do „{0}“.", "Add_missing_properties_95165": "Přidat chybějící vlastnosti", "Add_missing_super_call_90001": "Přidat chybějící volání metody super()", "Add_missing_typeof_95052": "Přidat ch<PERSON>ěj<PERSON><PERSON>í typeof", "Add_names_to_all_parameters_without_names_95073": "Přidat názvy do všech parametrů bez názvů", "Add_optional_parameter_to_0_95191": "Přidejte volitelný parametr do „{0}“", "Add_optional_parameters_to_0_95192": "Přidat volitelné parametry do {0}", "Add_or_remove_braces_in_an_arrow_function_95058": "Přidat nebo odebrat složené závorky ve funkci šipky", "Add_override_modifier_95160": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON> override", "Add_parameter_name_90034": "Přidat název parametru", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Přidat kvalifikátor do všech nerozpoznaných proměnných odpovídajících názvu členu", "Add_resolution_mode_import_attribute_95196": "Přidat atribut importu resolution-mode", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "Přidat atribut importu resolution-mode do všech importů, při kter<PERSON>ch se importuje pouze typ, kter<PERSON> ho potřebují", "Add_return_type_0_90063": "Přidejte návratový typ „{0}“", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "Chcete-li typ nastavit jak<PERSON>, přidejte do tohoto výrazu operátor „satisfies“ a kontrolní výraz typu („satisfies T as T“).", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "Přidejte operátor „satisfies“ a kontrolní výraz vloženého typu s „{0}“.", "Add_to_all_uncalled_decorators_95044": "Přidat () do všech nevolaných dekorátorů", "Add_ts_ignore_to_all_error_messages_95042": "Př<PERSON>t @ts-ignore do všech chybových zpráv", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Pokud k přístupu používáte index, přidejte k typu řetězec undefined.", "Add_undefined_to_optional_property_type_95169": "Přidat hodnotu undefined do volitelného typu vlastnosti", "Add_undefined_type_to_all_uninitialized_properties_95029": "Přidat nedefinovaný typ do všech neinicializovaných vlastností", "Add_undefined_type_to_property_0_95018": "Přidat typ undefined k vlastnosti {0}", "Add_unknown_conversion_for_non_overlapping_types_95069": "<PERSON><PERSON><PERSON><PERSON><PERSON>d unknown pro typy, kter<PERSON> se nepřekrývají", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Přidat unknown do všech převodů pro typy, které se nepřekrývají", "Add_void_to_Promise_resolved_without_a_value_95143": "Přidat void k objektu Promise vyřešenému bez hodnoty", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Přidat void ke všem objektům Promise vyřešeným bez hodnoty", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Přidání souboru tsconfig.json vám pomůže uspořádat projekty, které obsahují jak soubory TypeScript, tak soubory JavaScript. Další informace najdete na adrese https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Všechny deklarace {0} musí mít identická omezení.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Všechny deklarace {0} musí mít stejné modifikátory.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Všechny deklarace {0} musí mít stejné parametry typu.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Všechny deklarace abstraktní metody musí jít po sobě.", "All_destructured_elements_are_unused_6198": "Žádný z destrukturovaných elementů se nepoužívá.", "All_imports_in_import_declaration_are_unused_6192": "Žádné importy z deklarace importu se nepoužívají.", "All_type_parameters_are_unused_6205": "Všechny parametry typů jsou nevyužité.", "All_variables_are_unused_6199": "Žádná z proměnných se nepoužívá.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Po<PERSON>lte, aby se soubory JavaScriptu staly součástí programu. K získání informací o chybách v těchto souborech použít možnost checkJS.", "Allow_accessing_UMD_globals_from_modules_6602": "Povolit přístup ke globálním proměnným UMD z modulů", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Povolte výchozí importy z modulů bez výchozího exportu. Nebude to mít vliv na generování kódu, jenom na kontrolu typů.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Pokud modul nemá výchozí export, povolte import X z Y.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Povolte import pomocných funkcí z knihovny tslib jednou za celý projekt místo jejich zahrnutí do každého souboru.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "Po<PERSON><PERSON><PERSON>, aby importy zahrnovaly přípony souborů TypeScriptu. Vyžaduje nastavení možnosti „--moduleResolution bundler“ a buď „--noEmit“, nebo „--emitDeclarationOnly“.", "Allow_javascript_files_to_be_compiled_6102": "Povolí kompilaci souborů javascript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Při řešení modulů povolit, aby se s více složkami zacházelo jako s jednou.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "<PERSON><PERSON><PERSON><PERSON> souboru {0}, k<PERSON><PERSON> se už zahrnul, se od názvu souboru {1} li<PERSON><PERSON> jen ve velkých a malých písmenech.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Deklarace ambientního modulu nemůže uvádět relativní název modulu.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Ambientní moduly se nedají zanořovat do jiných modulů nebo oborů n<PERSON>zvů.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Modul AMD nemůže obsahovat víc přiřazení názvů.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Abstraktní přístupový objekt nemůže mít implementaci.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Modifikátor přístupnosti se nedá použít spolu s privátním identifikátorem.", "An_accessor_cannot_have_type_parameters_1094": "Přístupový objekt nemůže obsahovat parametry typu.", "An_accessor_property_cannot_be_declared_optional_1276": "Vlastnost accessor nejde deklarovat jako vol<PERSON>.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Deklarace ambientního modulu je povolená jenom na nejvyšší úrovni v souboru.", "An_argument_for_0_was_not_provided_6210": "Neposkytl se argument pro {0}.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Neposkytl se argument, k<PERSON><PERSON> by od<PERSON><PERSON><PERSON><PERSON> tomuto vzoru vazby.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Aritmetický operand musí být typu any, number, bigint nebo typu výčtu.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Funkce šipky nemůže mít parametr this.", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "Asynchronní funkce nebo metoda v ES5 vyžaduje konstruktor „Promise“.  Ujistěte se, že máte deklaraci konstruktoru „Promise“, nebo do možnosti „--lib“ přidejte „ES2015“.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Asynchronní funkce nebo metoda musí vracet hodnotu ‚Promise‘. Přesv<PERSON>d<PERSON><PERSON>, že pro ni máte <PERSON>, nebo zah<PERSON> ‚ES2015‘ do možnosti ‚--lib‘.", "An_async_iterator_must_have_a_next_method_2519": "Asynchronní iter<PERSON>tor musí mít metodu next().", "An_element_access_expression_should_take_an_argument_1011": "Výraz přístupu k elementu by m<PERSON><PERSON> <PERSON>ř<PERSON><PERSON><PERSON><PERSON> argument.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Člen výčtu není možné pojmenovat privátním identifikátorem.", "An_enum_member_cannot_have_a_numeric_name_2452": "Člen výčtu nemůže mít číselný název.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Za názvem členu výčtu musí následovat znak <PERSON> (,), rov<PERSON><PERSON><PERSON><PERSON> (=) nebo pravé slož<PERSON> (}).", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Rozšířená verze těchto informací, zobrazující všechny možné možnosti kompilátoru", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Přiřazení exportu se nedá použít v modulu s jinými exportovanými elementy.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Přiřazení exportu se nedá používat v oboru názvů.", "An_export_assignment_cannot_have_modifiers_1120": "Přiřazení exportu nemůže mít modifikátory.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Přiřazení exportu musí být na nejvyšší úrovni deklarace souboru nebo modulu.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Deklarace exportu se dá použít pouze na nejvyšší úrovni modulu.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Deklarace exportu se dá použít pouze na nejvyšší úrovni oboru názvů nebo modulu.", "An_export_declaration_cannot_have_modifiers_1193": "Deklarace exportu nemůže mít modifikátory.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "K<PERSON>ž je povolená možnost verbatimModuleSyntax, musí deklarace „export =“ odkazovat na skutečnou hodnotu, ale „{0}“ se překládá na deklaraci „pouze typ“.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "Deklarace „export =“ musí odkazovat na hodnotu, k<PERSON><PERSON> je povolená možnost „verbatimModuleSyntax“, ale „{0}“ odkazuje jenom na typ.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "K<PERSON>ž je povolená možnost verbatimModuleSyntax, musí „export default“ odkazovat na skutečnou hodnotu, ale „{0}“ se překládá na deklaraci „pouze typ“.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "Pokud je povolená možnost „verbatimModuleSyntax“, musí „export default“ odkazovat na hodnotu, ale „{0}“ odkazuje jenom na typ.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "<PERSON>ení mož<PERSON>é testovat pravdivost výrazu typu void.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Rozšířená řídicí hodnota Unicode musí být mezi 0x0 a 0x10FFFF (včetně).", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Identifikátor nebo klíčové slovo nemůže následovat hned po číselném literálu.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Implementace se nedá deklarovat v ambientních kontextech.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Alias importu se nemůže odkazovat na deklaraci, která se exportovala pomocí export type.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Alias importu se nemůže odkazovat na deklaraci, která se importovala pomocí import type.", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "Pokud je povolená možnost „verbatimModuleSyntax“, alias importu nelze přeložit na deklaraci typu nebo deklaraci „pouze typ“.", "An_import_alias_cannot_use_import_type_1392": "Alias importu nemůže používat import type.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Deklarace importu se dá použít pouze na nejvyšší úrovni modulu.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Deklarace importu se dá použít pouze na nejvyšší úrovni oboru názvů nebo modulu.", "An_import_declaration_cannot_have_modifiers_1191": "Deklarace importu nemůže mít modifikátory.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "<PERSON><PERSON>ž je povolená možnost „allowImportingTsExtensions“, může cesta importu končit pouze příponou „{0}“.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Signatura indexu indexu nemůže obsahovat parametr rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Signatura indexu nemůže mít na konci čárku.", "An_index_signature_must_have_a_type_annotation_1021": "Signatura indexu musí mít anotaci typu.", "An_index_signature_must_have_exactly_one_parameter_1096": "Signatura indexu musí mít právě jeden parametr.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "V parametru signatury indexu nemůže být otazník.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "V parametru signatury indexu nemůže být modifikátor přístupnosti.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "V parametru signatury indexu nemůže být inicializátor.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "V parametru signatury indexu nemůže být anotace typu.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Typ parametru signatury indexu nemůže být typ literálu nebo obecný typ. Místo toho z<PERSON>žte použití namapovaného typu objektu.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Typ parametru signatury indexu musí b<PERSON>, č<PERSON><PERSON>, symbol nebo typ literálu šablony.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Po výrazu vytvoření instance nemůže následovat přístup k vlastnosti.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Rozhraní může rozšířit jenom identifikátor nebo kvalifikovaný název s volitelnými argumenty typu.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Rozhraní může roz<PERSON>iř<PERSON>t jen typ objektu nebo průsečík typů objektů se <PERSON>ky známými členy.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "Rozhraní nemůže rozšiřovat primitivní typ, jako je například „{0}“. M<PERSON>že rozšiřovat pouze jiné typy pojmenovaných objektů.", "An_interface_property_cannot_have_an_initializer_1246": "Vlastnost rozhraní nemůže mít inicializátor.", "An_iterator_must_have_a_next_method_2489": "<PERSON><PERSON><PERSON><PERSON> musí mít metodu next().", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "Při použití direktivy pragma @jsx s fragmenty JSX se vyžaduje direktiva pragma @jsxFrag.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Literál objektu nemůže obsahovat několik přístupových objektů get/set se stejným názvem.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Literál objektu nemůže mít víc vlastností se stejným názvem.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Literál objektu nemůže obsahovat vlastnost a přístupový objekt se stejným názvem.", "An_object_member_cannot_be_declared_optional_1162": "Člen objektu nemůže b<PERSON>t <PERSON>kla<PERSON> jako ne<PERSON>.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "Metoda „[Symbol.hasInstance]“ objektu musí vracet logickou hodnotu, aby ji bylo možné použít na pravé straně výrazu „instanceof“.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Nepovinný řetěz nemůže obsahovat privátní identifikátory.", "An_optional_element_cannot_follow_a_rest_element_1266": "Element optional nemůže následovat za elementem rest.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Tento kontejner zakrývá vnější hodnotu this.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Signatura přetížení nemůže b<PERSON><PERSON>kla<PERSON>ý jako gener<PERSON>.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Unární výraz s operátorem {0} se na levé straně výrazu umocnění nepovoluje. Zvažte možnost uzavření výrazu do závorek.", "Annotate_everything_with_types_from_JSDoc_95043": "Vše s typy z JSDoc opatřit poznámkami", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "Přidejte anotace typů funkce expando ve vlastnostech v oboru názvů.", "Annotate_with_type_from_JSDoc_95009": "Přidat poznámku s typem z JSDoc", "Another_export_default_is_here_2753": "Další výchozí hodnota exportu je tady.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "Jakákoli vlastnost Unicode, kter<PERSON> by m<PERSON><PERSON> odpovídat více než jednomu znaku, je k dispozici pouze v případě, že je nastaven příznak Unicode Sets (v).", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "<PERSON><PERSON><PERSON>, co by <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> více než jed<PERSON><PERSON> z<PERSON>, je v negované třídě znaků neplatné.", "Are_you_missing_a_semicolon_2734": "Nechybí středník?", "Argument_expression_expected_1135": "Očekává se výraz argumentu.", "Argument_for_0_option_must_be_Colon_1_6046": "Argument možnosti {0} mus<PERSON> být {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "Argument dynamického importu nemůže být element rozestření.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "Argument typu {0} nejde přiřadit k parametru typu {1}.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "Argument typu {0} se nedá přiřadit k parametru typu {1} s hodnotou exactOptionalPropertyTypes: true. Zvažte možnost přidat hodnotu undefined do typů vlastností cíle.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "<PERSON>ezadaly se argumenty pro parametr rest {0}.", "Array_element_destructuring_pattern_expected_1181": "Očekával se destrukturační vzor elementu pole.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "Pole s elementy spread nelze odvodit pomocí možnosti --isolatedDeclarations.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Kontrolní výrazy vyžadují, aby se všechny názvy v cíli volání deklarovaly s explicitní anotací typu.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Kontrolní výrazy vyžadují, aby c<PERSON>l <PERSON> byl identif<PERSON>, nebo k<PERSON><PERSON><PERSON><PERSON>.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "Přiřazování vlastností funkcím bez jejich deklarování není u s možností --isolatedDeclarations podporováno. Přidejte explicitní deklaraci pro vlastnosti přiřazené k této funkci.", "Asterisk_Slash_expected_1010": "Očekával se znak */.", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "Minimálně jeden přistupující objekt musí mít explicitní anotaci typu s možností --isolatedDeclarations.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Rozšíření pro globální rozsah může být jenom přímo vnořené v externích modulech nebo deklaracích ambientního modulu.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Rozšíření pro globální roz<PERSON>h by m<PERSON><PERSON> mít modifik<PERSON><PERSON> declare, pokud se neobjeví v kontextu, kter<PERSON> je už ambientní.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "Automatické zjišťování pro psaní je povolené v projektu {0}. Spouští se speciální průchod řešení pro modul {1} prostřednictvím umístění mezi<PERSON>ěti {2}.", "BUILD_OPTIONS_6919": "MOŽNOSTI SESTAVENÍ", "Backwards_Compatibility_6253": "Zpětná kompatibilita", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Výrazy základní třídy nemůžou odkazovat na parametry typu třídy.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Návratový typ z<PERSON>lad<PERSON><PERSON><PERSON> konstr<PERSON> {0} není typ objektu ani průsečík typů objektů se staticky známými členy.", "Base_constructors_must_all_have_the_same_return_type_2510": "Všechny základní konstruktory musí mít stejný návratový typ.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Základ<PERSON><PERSON> pro překlad neabsolutních názvů modulů.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "<PERSON><PERSON>ž je cíl nastavený níže než ES2020, literály typu BigInt nejsou k dispozici.", "Binary_digit_expected_1177": "Očekává se binární číslice.", "Binding_element_0_implicitly_has_an_1_type_7031": "Element vazby {0} má implicitně typ {1}.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "Elementy vazeb nelze exportovat přímo s možností --isolatedDeclarations.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Proměnná bloku {0} se používá před vlastní <PERSON>.", "Build_a_composite_project_in_the_working_directory_6925": "Sestavte složený projekt v pracovním adresáři.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Sestavujte všechny projekty včetně těch, kter<PERSON> se zdají aktuální.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Sestavit jeden nebo více projektů a jej<PERSON>, pokud jsou z<PERSON>aralé", "Build_option_0_requires_a_value_of_type_1_5073": "Možnost buildu {0} vyžaduje hodnotu typu {1}.", "Building_project_0_6358": "Sestavuje se projekt {0}...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "Instance integro<PERSON><PERSON><PERSON> iterá<PERSON><PERSON> jsou vytvářeny s typem „TReturn“ s hodnotou „undefined“ místo hodnoty „any“.", "COMMAND_LINE_FLAGS_6921": "PŘÍZNAKY PŘÍKAZOVÉHO ŘÁDKU", "COMMON_COMMANDS_6916": "BĚŽNÉ PŘÍKAZY", "COMMON_COMPILER_OPTIONS_6920": "BĚŽNÉ PARAMETRY KOMPILÁTORU", "Call_decorator_expression_90028": "Zavolat výraz dekorátoru", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Návratové typy signatury volání {0} a {1} nejsou kompatibilní.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Signatura volání s chybějící anotací návratového typu má implicitně návratový typ any.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Signatury volání bez <PERSON>ů mají nekompatibilní návratové typy {0} a {1}.", "Can_only_convert_logical_AND_access_chains_95142": "Přev<PERSON>t se dají jen logické řetězy přístupu AND.", "Can_only_convert_named_export_95164": "Lze převést pouze pojmenovaný export ", "Can_only_convert_property_with_modifier_95137": "Převést se dá jenom vlastnost s modifikátorem.", "Can_only_convert_string_concatenations_and_string_literals_95154": "Převést lze pouze zřetězení řetězců a řetězcové literály.", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "K {0}.{1} nelze z<PERSON> přís<PERSON>p, pro<PERSON><PERSON><PERSON> {0} je typ, niko<PERSON> n<PERSON> prostor. Chtěli jste načíst typ vlastnosti {1} v {0} pomocí {0}[{1}]?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "<PERSON><PERSON><PERSON> je povolena možnost „{0}“, nelze získat přístup k „{1}“ z jiného souboru bez kvalifikace. Místo toho použijte možnost „{2}“.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "<PERSON><PERSON>ž je povolená možnost „{0}“, nelze přistupovat k výčtům prostředí „const enum“.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "<PERSON><PERSON> konstruktoru {0} se nedá přiřadit k typu konstruktoru {1}.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Abstraktní typ konstruktoru nejde přiřadit neabstraktnímu typu konstruktoru.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Do {0} se ned<PERSON>, proto<PERSON><PERSON> je to t<PERSON><PERSON><PERSON>.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Nejde přiřadit k vlastnosti {0}, proto<PERSON>e je konstantní.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Do {0} se ned<PERSON>, proto<PERSON>e je to <PERSON>ce.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Do {0} se ned<PERSON>, proto<PERSON><PERSON> je to obor n<PERSON>.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Nejde přiřadit k vlastnosti {0}, proto<PERSON><PERSON> je jen pro <PERSON>.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Do {0} se ned<PERSON>, proto<PERSON>e je to výč<PERSON>.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Do {0} se ned<PERSON>, proto<PERSON>e je to import.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Nejde přiřadit k <PERSON>žce {0}, to nen<PERSON>.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Není možné přiřazovat hodnoty do privátní metody {0}. Do privátních metod se nedá zapisovat.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Modul {0} se nedá rozšířit, protože se překládá do entity, která není modul.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Modul {0} se ned<PERSON> r<PERSON>, protože se překládá na entitu, kter<PERSON> není modul.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "<PERSON><PERSON>ly nejde kompilovat pomocí možnosti {0}, pokud příznak --module nem<PERSON> hodnotu amd nebo system.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Nejde vytvořit instance abstraktní třídy.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Iterace se nedá delegovat na hodnotu, proto<PERSON><PERSON> metoda next jej<PERSON><PERSON> iterátoru očekává typ {1}, ale obsahující generátor v<PERSON><PERSON> {0}.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "{0} se nedá exportovat. Z modulu je možné exportovat jenom místní deklarace.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "T<PERSON><PERSON><PERSON> {0} se nedá rozšířit. Konstruktor třídy je označený jako privátní.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Nejde rozšířit rozhraní {0}. <PERSON><PERSON><PERSON> jste na mysli 'implements'?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Soubor tsconfig.json nejde najít v aktuálním adresáři: {0}", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Soubor tsconfig.json nejde najít v zadaném adresáři: {0}", "Cannot_find_global_type_0_2318": "Globální typ {0} se nena<PERSON><PERSON>.", "Cannot_find_global_value_0_2468": "Globální hodnota {0} se nenašla.", "Cannot_find_lib_definition_for_0_2726": "Nepovedlo se najít definici knihovny pro {0}.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "Nepovedlo se najít definici knihovny pro {0}. Neměli jste na mysli spíš {1}?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Nepovedlo se najít modul {0}. Zvažte možnost importovat modul s příponou .json pomocí --resolveJsonModule.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "Nepovedlo se najít modul „{0}“. Nechtěli jste nastavit možnost „moduleResolution“ na „nodenext“ nebo přidat do možnosti „paths“ aliasy?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "Nepovedlo se najít modul {0} nebo jeho odpovídající dekla<PERSON> typů.", "Cannot_find_name_0_2304": "<PERSON><PERSON><PERSON><PERSON> {0} se ne<PERSON><PERSON><PERSON>.", "Cannot_find_name_0_Did_you_mean_1_2552": "Nepovedlo se naj<PERSON><PERSON> {0}. <PERSON><PERSON><PERSON> jste na mysli {1}?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Název {0} se ned<PERSON> najít. <PERSON><PERSON><PERSON> j<PERSON> na mysli člena instance this.{0}?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Název {0} se ned<PERSON> najít. M<PERSON><PERSON> j<PERSON> na my<PERSON>li statický člen {1}.{0}?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "<PERSON><PERSON>ze nají<PERSON> n<PERSON> {0}. <PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON> to napsat v asynchronní funk<PERSON>?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Nepovedlo se najít n<PERSON>v ‚{0}‘. Potřebujete změnit cílovou knihovnu? Zkuste změnit možnost kompilátoru ‚lib‘ na ‚{1}‘ nebo novější.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Nepovedlo se najít n<PERSON> ‚{0}‘. Potřebujete změnit cílovou knihovnu? Zkuste změnit možnost kompilátoru ‚lib‘ tak, aby o<PERSON><PERSON> ‚dom‘.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "Nepodařilo se najít n<PERSON>v „{0}“. Potřebujete nainstalovat definice typů pro Bun? Zkuste použít „npm i --save-dev @types/bun“.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "Nepodařilo se najít n<PERSON>zev „{0}“. Potřebujete nainstalovat definice typů pro Bun? Zkuste použít „npm i --save-dev @types/bun“ a pak do pole typů v tsconfig přidejte „bun“.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Nepovedlo se najít n<PERSON>v {0}. Potřebujete nainstalovat definice typů pro spouštěč testů? Zkuste npm i --save-dev @types/jest nebo npm i --save-dev @types/mocha.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Nepovedlo se nají<PERSON> n<PERSON> ‚{0}‘. Potřebujete nainstalovat definice typů pro spouštěč testů? Zkuste ‚npm i --save-dev@ types/jest‘ nebo ‚npm i --save-dev @types/mocha‘ a pak do polí typů v tsconfig přidejte ‚jest‘ nebo ‚mocha‘.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Nepovedlo se naj<PERSON>t n<PERSON> {0}. Potřebujete nainstalovat definice typů pro jQuery? Zkuste npm i --save-dev @types/jquery.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Nepovedlo se najít n<PERSON> ‚{0}‘. Potřebujete nainstalovat definice typů pro jQuery? Zkuste ‚npm i --save-dev @types/jquery` a pak pro pole typů v tsconfig přidejte ‚jquery‘.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Nepovedlo se naj<PERSON>t n<PERSON> {0}. Potřebujete nainstalovat definice typů pro Node? Zkuste npm i --save-dev @types/node.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Nepovedlo se najít n<PERSON> ‚{0}‘. Potřebujete nainstalovat definice typů pro uzel? Zkuste ‚npm i --save-dev @types/node‘ a pak do pole typů v tsconfig přidejte ‚node‘.", "Cannot_find_namespace_0_2503": "<PERSON><PERSON>šel se obor n<PERSON> {0}.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "<PERSON><PERSON> n<PERSON> {0} nejde najít. <PERSON><PERSON><PERSON> jste na mysli „{1}“?", "Cannot_find_parameter_0_1225": "Nenašel se parametr {0}.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Nenašla se společná cesta podadresářů pro vstupní soubory.", "Cannot_find_type_definition_file_for_0_2688": "Nejde najít soubor definice pro {0}.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Soubory deklarací typů nejde importovat. Zvažte možnost místo {1} naimportovat {0}.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Proměnnou {0} s vnějším oborem nejde inicializovat ve stejném oboru jako <PERSON> {1} s oborem bloku.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Nejde vyvolat objekt, k<PERSON><PERSON> může být null.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Nejde vyvolat objekt, k<PERSON><PERSON> může být null nebo nedefinovaný.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Nejde vyvolat objekt, k<PERSON><PERSON> může být nedefinovaný.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Hodnota se ned<PERSON> iterovat, pro<PERSON><PERSON><PERSON> metoda next jej<PERSON><PERSON> iter<PERSON>u očekává typ {1}, ale p<PERSON>i destruk<PERSON> pole se v<PERSON><PERSON> p<PERSON> {0}.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Hodnota se nedá iterovat, pro<PERSON><PERSON><PERSON> metoda next jej<PERSON><PERSON> iter<PERSON> očekává typ {1}, ale rozsah pole bude v<PERSON><PERSON> p<PERSON> {0}.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Hodnota se nedá iterovat, pro<PERSON><PERSON><PERSON> metoda next jej<PERSON><PERSON> iter<PERSON> očekává typ {1}, ale for-of bude v<PERSON><PERSON> p<PERSON> {0}.", "Cannot_move_statements_to_the_selected_file_95183": "Příkazy nelze přesunout do vybraného souboru.", "Cannot_move_to_file_selected_file_is_invalid_95179": "Nelze přesunout do souboru, vybraný soubor je neplat<PERSON>ý.", "Cannot_read_file_0_5083": "Nejde přečíst soubor {0}.", "Cannot_read_file_0_Colon_1_5012": "Nejde č<PERSON> soubor {0}: {1}", "Cannot_redeclare_block_scoped_variable_0_2451": "Nejde předeklarovat proměnnou bloku {0}.", "Cannot_redeclare_exported_variable_0_2323": "Exportovanou proměnnou {0} nen<PERSON> mož<PERSON> z<PERSON>.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Nejde předeklarovat identifikátor {0} v klauzuli catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Nejde spustit volání funkce v poznámce typu.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Pokud se nezadá příznak -jsx, nepůjde JSX použít.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "K<PERSON>ž se povolená možnost „{0}“, nelze pro obor názvů typů nebo obor názvů „pouze typ“ použít „export import“.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Nejde používat importy, exporty nebo rozšíření modulu, pokud má příznak --module hodnotu none.", "Cannot_use_namespace_0_as_a_type_2709": "<PERSON><PERSON> {0} nejde použít jako typ.", "Cannot_use_namespace_0_as_a_value_2708": "<PERSON><PERSON> {0} nejde p<PERSON>žít jako hodnotu.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Klíčové slovo „this“ nejde použít v inicializátoru statické vlastnosti dekorované třídy.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Soubor {0} se ned<PERSON> z<PERSON>, proto<PERSON>e přepíše soubor .tsbuildinfo vygenerovaný odkazovaným projektem {1}.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "<PERSON> souboru {0} se ned<PERSON>t, proto<PERSON><PERSON> by se p<PERSON><PERSON><PERSON> více vstup<PERSON><PERSON><PERSON> soub<PERSON>.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "<PERSON> souboru {0} se ned<PERSON>, pro<PERSON><PERSON><PERSON> by p<PERSON><PERSON><PERSON> vs<PERSON><PERSON><PERSON><PERSON> soub<PERSON>.", "Catch_clause_variable_cannot_have_an_initializer_1197": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> catch ne<PERSON><PERSON><PERSON><PERSON> mít inicializátor.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Pokud se zadá typ prom<PERSON><PERSON><PERSON> k<PERSON> catch, jeho anotace musí být any nebo unknown.", "Change_0_to_1_90014": "Změnit {0} na {1}", "Change_all_extended_interfaces_to_implements_95038": "Změnit všechna rozšířená rozhraní na implements", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Změnit všechny typy jsdoc-style na TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Změnit všechny typy jsdoc-style na TypeScript (a přidat | undefined do typů s možnou hodnotou null)", "Change_extends_to_implements_90003": "Změnit extends na implements", "Change_spelling_to_0_90022": "Změnit pravopis na {0}", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Zkontrolujte vlastnosti třídy, k<PERSON><PERSON> sice j<PERSON><PERSON>, ale nejsou nastavené v konstruktoru.", "Check_side_effect_imports_6806": "Zkontrolujte importy typu „side effect“.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jest<PERSON> argumenty metod bind, call a apply odpovídají <PERSON><PERSON>.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, jest<PERSON> je {0} ne<PERSON><PERSON><PERSON><PERSON> odpovídající předpona pro {1}–{2}.", "Circular_definition_of_import_alias_0_2303": "Cyklická definice aliasu importu {0}", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Při překladu konfigurace se zjistila cykličnost: {0}.", "Circularity_originates_in_type_at_this_location_2751": "Z<PERSON><PERSON><PERSON><PERSON> cykličnosti je typ na tomto umístění.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "T<PERSON><PERSON>da {0} definuje členský přístupový objekt instance {1}, ale rozšířená třída {2} ho definuje jako <PERSON> funk<PERSON> instance.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "T<PERSON><PERSON><PERSON> {0} definuje členskou funkci instance {1}, ale rozšířená třída {2} ji definuje jako členský přístupový objekt instance.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "<PERSON><PERSON><PERSON><PERSON> {0} definuje vlastnost člena instance {1}, ale rozšířená třída {2} ji definuje jako členskou funkci instance.", "Class_0_incorrectly_extends_base_class_1_2415": "T<PERSON><PERSON><PERSON> {0} nesprávně rozšiř<PERSON>je základní třídu {1}.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "T<PERSON><PERSON><PERSON> {0} nesprávně implementuje třídu {1}. Nechtěli jste rozšířit třídu {1} a dědit její <PERSON>y jako podtřídu?", "Class_0_incorrectly_implements_interface_1_2420": "<PERSON><PERSON><PERSON><PERSON> {0} nesprávně implement<PERSON>je roz<PERSON>ní {1}.", "Class_0_used_before_its_declaration_2449": "Třída {0} se p<PERSON>žívá dříve, než se deklaruje.", "Class_constructor_may_not_be_a_generator_1368": "Konstruktor t<PERSON><PERSON><PERSON> b<PERSON>t generátor.", "Class_constructor_may_not_be_an_accessor_1341": "Konstruktor tř<PERSON><PERSON> ne<PERSON>ů<PERSON> být přístupový objekt.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "<PERSON><PERSON><PERSON> tř<PERSON><PERSON> ne<PERSON> implementovat seznam přetížení pro {0}.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>d <PERSON> mít více než jednu z<PERSON> ‚@augments‘ nebo ‚@extends‘.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Dekorátory tříd se nedají použít se statickým privátním identifikátorem. Zvažte možnost odebrat experimentální <PERSON>.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "Pole třídy „{0}“ definované nadřazenou třídou není v podřízené třídě přístupné přes třídu typu super.", "Class_name_cannot_be_0_2414": "<PERSON><PERSON><PERSON><PERSON> nemůže mít název {0}.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "<PERSON><PERSON>ž se cílí na ES5 s modulem {0}, název tří<PERSON> nemůže být Object.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Statická strana třídy {0} nesprávně rozš<PERSON><PERSON><PERSON><PERSON>u stranu základní třídy {1}.", "Classes_can_only_extend_a_single_class_1174": "<PERSON><PERSON><PERSON><PERSON> mů<PERSON> roz<PERSON>í<PERSON> jenom jednu třídu.", "Classes_may_not_have_a_field_named_constructor_18006": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mít pole s názvem constructor.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "<PERSON><PERSON><PERSON> o<PERSON>a<PERSON> ve třídě se vyhodnocuje ve striktním režimu jazyka JavaScript, který toto použití {0} nepovoluje. Další informace najdete tady: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode", "Command_line_Options_6171": "Možnosti příkazového řádku", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Zkompilujte projekt podle cesty k jeho konfiguračnímu souboru nebo do složky se souborem tsconfig.json.", "Compiler_Diagnostics_6251": "Diagnostika kompilátoru", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "Možnost kompilátoru „{0}“ nemůže mít prázdný řetězec.", "Compiler_option_0_expects_an_argument_6044": "Parametr kompilátoru {0} očekává argument.", "Compiler_option_0_may_not_be_used_with_build_5094": "Možnost kompilátoru „--{0}“ se nesm<PERSON> p<PERSON>žívat s „--build“.", "Compiler_option_0_may_only_be_used_with_build_5093": "Možnost kompilátoru „--{0}“ se smí používat jenom s „--build“.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "Možnost kompilátoru {0} hodnoty {1} je nestabilní. Ke ztlumení této chyby použijte noční TypeScript. Zkuste provést aktualizaci pomocí příkazu npm install -D typescript@next.", "Compiler_option_0_requires_a_value_of_type_1_5024": "Parametr kompilátoru {0} vyžaduje hodnotu typu {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Kompilátor si rezervuje název {0} při generování privátního identifikátoru pro nižší <PERSON>.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Zkompiluje projekt TypeScriptu umístěný v zadané cestě.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Zkompiluje aktuální projekt (tsconfig.json v pracovním adresáři).", "Compiles_the_current_project_with_additional_settings_6929": "Zkompiluje aktuální projekt s dalšími nastaveními.", "Completeness_6257": "Úplnost", "Composite_projects_may_not_disable_declaration_emit_6304": "Složené projekty nemůžou zakázat generování deklarací.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Složené projekty nemůžou zakázat přírůstkovou kompilaci.", "Computed_from_the_list_of_input_files_6911": "Vypočítáno ze seznamu vstup<PERSON><PERSON><PERSON> so<PERSON>", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "Počítané vlastnosti musí být číselné nebo řetězcové literá<PERSON>, proměnné nebo výrazy s tečkami s možností --isolatedDeclarations.", "Computed_property_names_are_not_allowed_in_enums_1164": "Názvy počítaných vlastností se ve výčtech nepovolují.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "Názvy počítaných vlastností v literálech třídy nebo objektu nelze odvodit pomocí možnosti --isolatedDeclarations.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "<PERSON>e výč<PERSON>, jeh<PERSON><PERSON> mají hodnoty typu string, se nepovolují vypočítané hodnoty.", "Concatenate_and_emit_output_to_single_file_6001": "Zřetězit a generovat výstup do jednoho souboru", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kter<PERSON> se mají nastavit kromě výchozích hodnot specifických pro překladač při překladu importů.", "Conflicts_are_in_this_file_6201": "V tomto souboru se nacházejí konflikty.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Zvažte přidání modifikátoru „declare“ do této třídy.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Návratové typy signatury konstruktu {0} a {1} nejsou kompatibilní.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Podpis konstruktoru s chybějící anotací návratového typu má implicitně návratový typ any.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Signatury konstruktů bez argumentů mají nekompatibilní návratové typy {0} a {1}.", "Constructor_implementation_is_missing_2390": "Chybí implementace konstruktoru.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Konstruktor třídy {0} je privátní a dostupný jenom v rámci deklarace třídy.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Konstruktor třídy {0} je chráněný a dostupný jenom v rámci deklarace třídy.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "<PERSON><PERSON><PERSON> se notace typu konstruktoru používá v typu sjednocení, musí být uzavřená do závorky.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "<PERSON><PERSON><PERSON> se notace typu konstruktoru používá v typu průniku, musí být uzavřená do závorky.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Konstruktory odvozených tříd musí obsahovat volání příkazu super.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Není zadaný obsažený soubor a nedá se určit kořenový adresář – přeskakuje se vyhledávání ve složce node_modules.", "Containing_function_is_not_an_arrow_function_95128": "Obsahující funkce není funkc<PERSON>.", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "<PERSON><PERSON><PERSON><PERSON>, která metoda se používá k detekci souborů JS ve formátu modulu.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "<PERSON><PERSON><PERSON>d typu {0} na typ {1} m<PERSON><PERSON><PERSON> být <PERSON>, protože ani jeden z těchto typů se s tím druhým dostatečně nepřekrývá. Pokud je to z<PERSON><PERSON><PERSON><PERSON>, převeďte nejdříve výraz na unknown.", "Convert_0_to_1_in_0_95003": "Převést {0} na {1} v {0}", "Convert_0_to_mapped_object_type_95055": "Převést {0} na typ mapovaného objektu", "Convert_all_const_to_let_95102": "Převést všechny const na let", "Convert_all_constructor_functions_to_classes_95045": "Převést všechny funkce konstruktoru na třídy", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Převést všechny neplatné znaky na kód entity HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Převést všechny opětovně exportované typy na exporty, při kter<PERSON>ch se exportují jen typy", "Convert_all_require_to_import_95048": "Převést všechna volání require na import", "Convert_all_to_async_functions_95066": "Převést vše na asynchronní funkce", "Convert_all_to_bigint_numeric_literals_95092": "Převést vše na číselné literály bigint", "Convert_all_to_default_imports_95035": "Převést vše na výchozí importy", "Convert_all_type_literals_to_mapped_type_95021": "Převést všechny literály typů na namapovaný typ", "Convert_all_typedef_to_TypeScript_types_95177": "Převeďte všechny typy typedef na typy TypeScript.", "Convert_arrow_function_or_function_expression_95122": "Převést funkci šipky nebo výraz funkce", "Convert_const_to_let_95093": "Převést const na let", "Convert_default_export_to_named_export_95061": "Převést výchozí export na pojmenovaný export", "Convert_function_declaration_0_to_arrow_function_95106": "P<PERSON><PERSON><PERSON>t deklaraci funkce {0} na funkci šipky", "Convert_function_expression_0_to_arrow_function_95105": "Převést výraz funkce {0} na funkci šipky", "Convert_function_to_an_ES2015_class_95001": "Převést funkci na třídu ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Převést neplatný znak na jeho kód entity HTML", "Convert_named_export_to_default_export_95062": "Převést pojmenovaný export na výchozí export", "Convert_named_imports_to_default_import_95170": "Převést pojmenované importy na výchozí import", "Convert_named_imports_to_namespace_import_95057": "Převést pojmenované importy na import oboru názvů", "Convert_namespace_import_to_named_imports_95056": "Převést import oboru názvů na pojmenované importy", "Convert_overload_list_to_single_signature_95118": "Převést seznam přetížení na jednu signaturu", "Convert_parameters_to_destructured_object_95075": "Převést parametry na destrukturovaný objekt", "Convert_require_to_import_95047": "Převést require na import", "Convert_to_ES_module_95017": "Převést na modul ES", "Convert_to_a_bigint_numeric_literal_95091": "Převést na číselný literál bigint", "Convert_to_anonymous_function_95123": "Převést na anonymní funkci", "Convert_to_arrow_function_95125": "Převést na funkci šipky", "Convert_to_async_function_95065": "Převést na asynchronní funkci", "Convert_to_default_import_95013": "Převést na výchozí import", "Convert_to_named_function_95124": "Převést na pojmenovanou funkci", "Convert_to_optional_chain_expression_95139": "Převést na nepovinný výraz řetězu.", "Convert_to_template_string_95096": "Převést na řetězec šablony", "Convert_to_type_only_export_1364": "Převést na export, při kterém se exportují jen typy", "Convert_typedef_to_TypeScript_type_95176": "Převeďte typedef na typ TypeScript.", "Corrupted_locale_file_0_6051": "<PERSON><PERSON><PERSON> pro<PERSON>ředí {0} je <PERSON><PERSON><PERSON><PERSON>.", "Could_not_convert_to_anonymous_function_95153": "Nepovedlo se převést na anonymní funkci.", "Could_not_convert_to_arrow_function_95151": "Nepovedlo se převést na funkci šipky.", "Could_not_convert_to_named_function_95152": "Nepovedlo se převést na pojmenovanou funkci.", "Could_not_determine_function_return_type_95150": "Nepovedlo se určit návratový typ funkce.", "Could_not_find_a_containing_arrow_function_95127": "Nepovedlo se najít obsahující <PERSON>.", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "<PERSON><PERSON><PERSON><PERSON> se soubor deklarací pro modul {0}. {1} má implicitně typ any.", "Could_not_find_convertible_access_expression_95140": "Nepovedlo se najít převoditelný výraz přístupu.", "Could_not_find_export_statement_95129": "Nešlo najít příkaz export.", "Could_not_find_import_clause_95131": "Nešlo najít klauzuli import.", "Could_not_find_matching_access_expressions_95141": "Nepovedlo se najít odpovídající výrazy přístupu.", "Could_not_find_name_0_Did_you_mean_1_2570": "Nepodařilo se naj<PERSON><PERSON> n<PERSON> {0}. <PERSON><PERSON><PERSON> jste na mysli {1}?", "Could_not_find_namespace_import_or_named_imports_95132": "Nepovedlo se najít import oboru názvů nebo pojmenované importy.", "Could_not_find_property_for_which_to_generate_accessor_95135": "Nepovedlo se najít vlastnost, pro kterou se má vygenerovat přístupový objekt.", "Could_not_find_variable_to_inline_95185": "Nepodařilo se naj<PERSON>t <PERSON>, která se má vložit.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Nepovedlo se přeložit cestu {0} s příponami {1}.", "Could_not_write_file_0_Colon_1_5033": "Ned<PERSON> se zapisovat do souboru {0}: {1}", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Vytvořte pro generované soubory JavaScriptu soubory sourcemap.", "Create_sourcemaps_for_d_ts_files_6614": "Pro soubory d.ts vytvořte soubory sourcemap.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Vytvoří tsconfig.json doporučenými nastaveními v pracovním adresáři.", "DIRECTORY_6038": "ADRESÁŘ", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "Desítkové řídicí sekvence a zpětné odkazy nejsou ve třídě znaků povoleny.", "Decimals_with_leading_zeros_are_not_allowed_1489": "Desetinná čísla s úvodními nulami nejsou povolena.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "Deklarace rozšiřuje deklaraci v jiném souboru. Toto není možné serializovat.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "Generování deklarace pro tento soubor vyžaduje zachování tohoto importu pro rozšíření. Toto není podpor<PERSON>no s možností --isolatedDeclarations.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Generování deklarací pro tento soubor vyžaduje, aby se použil privátní název {0}. Explicitní anotace typu může generování deklarací odblokovat.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Generování deklarací pro tento soubor v<PERSON>žaduje, aby se použil privátní název {0} z modulu {1}. Explicitní anotace typu může generování deklarací odblo<PERSON>t.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "Generování deklarace pro tento parametr vyžaduje implicitní <PERSON> m<PERSON> „undefined“ do jeho typu. Není podporováno s možností „--isolatedDeclarations“.", "Declaration_expected_1146": "Očekává se deklarace.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Název deklarace je v konfliktu s integrovaným globálním identifikátorem {0}.", "Declaration_or_statement_expected_1128": "Očekává se deklarace nebo příkaz.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Očekávala se deklarace nebo příkaz. Tento znak = následuje blok příkazů, tak<PERSON>e pokud jste chtěli napsat destrukturační přiřazení, mo<PERSON><PERSON> bude nutné uzavřít celé přiřazení do závorek.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Deklarace s kontrolními výrazy jednoznačného přiřazení musí mít také anotace typu.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Deklarace s inicializátory nemůžou mít také kontrolní výrazy jednoznačného přiřazení.", "Declare_a_private_field_named_0_90053": "Deklarovat privátní pole s názvem {0}", "Declare_method_0_90023": "Deklarovat metodu {0}", "Declare_private_method_0_90038": "Deklarovat privátní metodu {0}", "Declare_private_property_0_90035": "Deklarujte privátní vlastnost {0}.", "Declare_property_0_90016": "Deklarovat vlastnost {0}", "Declare_static_method_0_90024": "Deklarovat statickou metodu {0}", "Declare_static_property_0_90027": "Deklarovat statickou vlastnost {0}", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Návratový typ funkce dekoratéru {0} se nedá přiřadit k typu {1}.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Návratový typ funkce dekoratéru je {0}, ale oček<PERSON> se, že bude void nebo any.", "Decorator_used_before_export_here_1486": "Dekoratér je tu použit před možností „export“.", "Decorators_are_not_valid_here_1206": "Dekorá<PERSON> tady nejsou plat<PERSON>.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Dekorátory nejde použít na víc přístupových objektů get/set se stejným názvem.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "Dekoratéry se nemůžou vyskytovat po „export“ nebo „export default“, pokud se taky vyskytují před možností „export“.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Dekoratéry musí předcházet název a všechna klíčová slova deklarace vlastností.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "Výchozí prom<PERSON><PERSON><PERSON> catch jako unknown nam<PERSON>to any.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Výchozí export modulu má nebo používá privátní název {0}.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "Výchozí exporty nelze odvodit pomocí --isolatedDeclarations.", "Default_library_1424": "Výchozí knihovna", "Default_library_for_target_0_1425": "Výchozí knihovna pro cíl {0}", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Definice následujících identifikátorů je v konfliktu s definicemi v jiném souboru: {0}", "Delete_all_unused_declarations_95024": "Odstranit všechny nepoužívané deklarace", "Delete_all_unused_imports_95147": "Odstranit všechny nepoužívané importy", "Delete_all_unused_param_tags_95172": "Odstranit všechny nepoužívané značky @param", "Delete_the_outputs_of_all_projects_6365": "Odstraňte výstupy všech projektů.", "Delete_unused_param_tag_0_95171": "Odstranit nepoužívanou značku @param {0}", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[<PERSON><PERSON><PERSON><PERSON>] Použijte místo toho --jsxFactory. Určí objekt vyvolaný pro createElement při cílení na generování JSX react.", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[<PERSON><PERSON><PERSON><PERSON>] Použijte místo toho --outFile. Zřetězí a vygeneruje výstup do jednoho souboru.", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[<PERSON><PERSON><PERSON><PERSON>] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> místo toho --skip<PERSON>ibCheck. Přeskočí kontrolu typů výchozích souborů deklarací knihovny.", "Deprecated_setting_Use_outFile_instead_6677": "Nastavení je zastaralé. Místo něj použijte outFile.", "Did_you_forget_to_use_await_2773": "Nezapomněli jste p<PERSON>žít await?", "Did_you_mean_0_1369": "<PERSON><PERSON><PERSON> jste na mysli {0}?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "<PERSON><PERSON><PERSON> j<PERSON> na mysli omezení {0} na typ new (...args: any[]) => {1}?", "Did_you_mean_to_call_this_expression_6212": "Nechtěli jste zavolat tento výraz?", "Did_you_mean_to_mark_this_function_as_async_1356": "<PERSON>echtěli jste označit tuto funkci jako async?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Neměli jste v úmyslu použít znak :? Znak = může následovat pouze po názvu vlastnosti, k<PERSON>ž je obsahující objekt literálu součástí vzoru destrukturalizace.", "Did_you_mean_to_use_new_with_this_expression_6213": "Nechtěli jste u tohoto výrazu použít new?", "Digit_expected_1124": "Očekává se číslice.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Adresář {0} neexistuje. Všechna vyhledávání v něm se přeskočí.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "Adresář {0} neobsahuje package.json scope. Importy nebudou vyřešeny.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "V generovaných souborech JavaScriptu zakažte přidávání direktiv „use strict“.", "Disable_checking_for_this_file_90018": "Zakázat kontrolu tohoto souboru", "Disable_emitting_comments_6688": "Zakázat generování koment<PERSON>.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Zakažte generování deklarací s příznakem „@internal“ v komentářích JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Zakažte generování souborů z kompilace.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Zakažte generování <PERSON>, pokud jsou při kontrole typů nahlášeny jakékoli chyby.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Zakažte v generovaném kódu mazání deklarací const enum.", "Disable_error_reporting_for_unreachable_code_6603": "Zakažte hlášení chy<PERSON>, pokud je kód <PERSON>.", "Disable_error_reporting_for_unused_labels_6604": "Zakažte hlášení chyb u nepoužitých popisků.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "Zakažte úplnou kontrolu typ<PERSON> (budou hl<PERSON><PERSON><PERSON>y pouze kritické chyby analýzy a generování).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Zakázat v kompilovaném výstupu generování vlastních pomocných funkcí, jako je __extends.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Zakažte zahrnutí všech souborů knihoven, včetně výchozí lib.d.ts.", "Disable_loading_referenced_projects_6235": "Zakažte načítání odkazovaných projektů.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Zakažte v odkazech na složené projekty místo deklaračních souborů používat preferované zdrojové soubory.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Zakažte při vytváření literálů objektů hlášení zbytečně velkého počtu chyb vlastností.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Zakažte překlad odkazů symlink na jejich skutečnou cestu (realpath). Toto nastavení koreluje se stejným příznakem uzlu.", "Disable_size_limitations_on_JavaScript_projects_6162": "Zakázat omezení velikosti v projektech JavaScriptu", "Disable_solution_searching_for_this_project_6224": "Zakažte vyhledávání řešení pro tento projekt.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Zakáže striktní kontroly generických signatur v typech funkcí.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Zakázat v javascriptových projektech získávání typů", "Disable_truncating_types_in_error_messages_6663": "Zakázat v chybových zprávách zkracování typů.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Zakažte možnost používat zdrojové soubory místo souborů deklarací z odkazovaných projektů.", "Disable_wiping_the_console_in_watch_mode_6684": "Zakažte vymazání konzole v režimu sledování.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Při získávání typů se zakáže odvozování. Názvy souborů se vyhledají v projektu.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Zakázat import, require nebo <reference> zvětšování počtu souborů, které by typeScript měl přidat do projektu.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Zakažte odkazy na stejný soubor s nekonzistentně použitými malými a velkými písmeny.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Nepřidávat odkazy se třemi lomítky nebo importované moduly do seznamu kompilovaných souborů", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "Nepovolit konstruktory modulu runtime, které nejsou součástí ECMAScriptu", "Do_not_emit_comments_to_output_6009": "Negenerovat komentáře pro výstup", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Negenerovat deklarace pro kód s anotací @internal", "Do_not_emit_outputs_6010": "Negenerovat výstupy", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Negenerovat výstupy, pokud byly ozná<PERSON>y chyby", "Do_not_emit_use_strict_directives_in_module_output_6112": "Negenerujte direktivy use strict ve výstupu modulu.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Nemazat deklarace konstantního výčtu v generovaném kódu", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Negenerovat v kompilovaném výstupu vlastní pomocné funkce jako __extends", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Nezahrnovat výchozí soubor knihov<PERSON> (lib.d.ts)", "Do_not_report_errors_on_unreachable_code_6077": "Neoznamují se chyby v nedosažitelném kódu.", "Do_not_report_errors_on_unused_labels_6074": "Neoznamují se chyby v nepoužívaných popiscích.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Nepřekládat skutečnou cestu symbolických odkazů", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "Netransformujte ani nevynechávejte žádné importy nebo exporty, které nejsou ozna<PERSON>eny jako „pouze typ“, a zajistěte, aby byly zapsány ve formátu výstupního souboru podle nastavení „module“.", "Do_not_truncate_error_messages_6165": "Nezkracovat chybové zprávy", "Duplicate_function_implementation_2393": "Duplicitní implementace funkce", "Duplicate_identifier_0_2300": "Duplicitní identifikátor {0}", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Duplicitní identifikátor {0}. Kompilátor si vyhrazuje název {1} v oboru nejvyšší úrovně pro daný modul.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Duplicitní identifikátor {0}. Kompilátor rezervuje název {1} v oboru nejvyšší úrovně modulu, který obsahuje asynchronní funkce.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Duplicitní identifikátor {0}. Kompilátor rezervuje název {1}, k<PERSON>ž se generují odkazy super ve statických inicializátorech.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Duplicitní identifikátor {0}. Kompilátor p<PERSON>í<PERSON> {1} pro podporu asynchronních funkcí.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Duplicitní identifikátor {0}. Statické elementy a elementy instancí nemůžou sdílet stejný privátní název.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Duplicitní identifikátor arguments. Kompilátor pomocí identifikátoru arguments inicializuje parametry rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Duplicitní identifikátor _newTarget. Kompilátor p<PERSON>žívá deklaraci proměnné _newTarget k zachycení odkazu na metavlastnost new.target.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Duplicitní identifikátor _this. Kompilátor pomoc<PERSON> de<PERSON>race proměnné _this zaznamenává odkaz na příkaz this.", "Duplicate_index_signature_for_type_0_2374": "Duplicitní signatura indexu pro typ {0}.", "Duplicate_label_0_1114": "<PERSON><PERSON><PERSON><PERSON><PERSON> popise<PERSON> {0}", "Duplicate_property_0_2718": "Duplicitní vlastnost {0}.", "Duplicate_regular_expression_flag_1500": "Duplikovaný příznak regulárního výrazu", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Specifiká<PERSON> importu musí být typu string, ale tady má typ {0}.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Dynamické importy se podporují jen v případě, že příznak --module je nastavený na es2020, es2022, esnext, commonjs, amd, system, umd, node16, node18 nebo nodenext.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "Dynamické importy můžou jako argumenty přijímat jenom specifikátor modulu a volitelnou sadu atributů.", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "Dynamické importy podporu<PERSON><PERSON> argument, pouze pokud je možnost --module nastavena na esnext, node16, node18, nodenext nebo preserve.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "Když je možnost „module“ nastavená na „preserve“, v modulu CommonJS se nepovoluje syntaxe ESM.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "Když je povolená syntaxe „verbatimModuleSyntax“, není v modulu CommonJS povolená syntaxe ESM.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "<PERSON><PERSON><PERSON><PERSON> „{0}.{1}“ se liší ve své hodnotě. <PERSON><PERSON> „{2}“, ale zadáno bylo „{3}“.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Každý člen typu sjednocení {0} má signatury konstruktu, ale žádná z těchto signatur není kompatibilní s jinou signaturou.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Každý člen typu sjednocení {0} má signatury, ale žádná z těchto signatur není kompatibilní s jinou signaturou.", "Editor_Support_6249": "Podpora editoru", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "Element má implicitně typ any, protože pomocí výrazu typu {0} není možné indexovat typ {1}.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "Element má implicitně typ any, protože indexový výraz není typu number.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "Element má implicitně typ any, protože typ {0} nemá žádnou signaturu indexu.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "Element má implicitně typ any, protože typ {0} nemá žádnou signaturu indexu. Nechtěli jste zavolat {1}?", "Emit_6246": "Generovat", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Generovat pole tř<PERSON>dy ECMAScript-standard-compliant.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Vygeneruje na začátku výstupních souborů značku pořadí bajtů ve formátu UTF-8.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Vygeneruje jediný soubor se zdrojovými mapováními namísto samostatného souboru.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Vygenerujte profil procesoru v8 spuštěného kompilátoru pro ladění.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Vygenerujte další JavaScript, aby se podpora importování modulů CommonJS ulehčila. Tím se za účelem kompatibility typů povolí „allowSyntheticDefaultImports“.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Vygenerujte pole t<PERSON><PERSON><PERSON> pomocí Define namísto Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Vygenerujte metadata o typu návrhu pro dekorované deklarace ve zdrojových souborech.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Generovat kompatibiln<PERSON><PERSON><PERSON><PERSON> k<PERSON>, ale při iteraci použít režim s komentářem (verbose) a méně výkonný JavaScript.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Vygeneruje zdroj spolu se zdrojovými mapováními v jednom souboru. Vyžaduje, aby byla nastavená možnost --inlineSourceMap nebo --sourceMap.", "Enable_all_strict_type_checking_options_6180": "Povolí všechny možnosti striktní kontroly typů.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Povolte ve výstupu TypeScriptu barvu a formátování, aby byly chyby kompilátoru č<PERSON>lnějš<PERSON>.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Povolte omezení, která v projektu TypeScriptu umožní používat odkazy na projekt.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Povolte hlášení chyb u cest kódu, kter<PERSON>ce výslovně nevrátí.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Povolte hlášení chyb u výrazů a deklarací s implicitním typem any.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Povolit hlášení chyb v příkazech switch v případě fallthrough.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Povolit hlášení chyb v javascriptových souborech se zkontrolovanými typy.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Povolte hlášení chy<PERSON>, k<PERSON><PERSON> se místní proměnná nepřečte.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Povolte hlášení chyb, k<PERSON><PERSON> má „this“ určený typ „any“.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "Povolte experimentální podporu pro starší experimentální dekoratéry.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "Povolte import so<PERSON><PERSON><PERSON> s libovolnou příponou za předpokladu, že je k dispozici soubor deklarace.", "Enable_importing_json_files_6689": "Povolte importování so<PERSON>ů .json.", "Enable_lib_replacement_6808": "Povolit nahrazení knihovny", "Enable_project_compilation_6302": "Povolit kompilování projektu", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Povolte ve funkcích metody bind, call a apply.", "Enable_strict_checking_of_function_types_6186": "Povolí striktní kontrolu typů funkcí.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Povolí striktní kontrolu inicializace vlastností ve třídách.", "Enable_strict_null_checks_6113": "Povolte striktní kontroly hodnot null.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Povolte v konfiguračním souboru možnost experimentalDecorators.", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Povolte v konfiguračním souboru příznak --jsx.", "Enable_tracing_of_the_name_resolution_process_6085": "Povolte trasování procesu překladu IP adres.", "Enable_verbose_logging_6713": "Povolte podrobné protokolování.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Povolí interoperabilitu generování mezi moduly CommonJS a ES prostřednictvím vytváření objektů oboru názvů pro všechny importy. Implikuje allowSyntheticDefaultImports.", "Enables_experimental_support_for_ES7_decorators_6065": "Povolí experimentální podporu pro dekorátory ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Povolí experimentální podporu pro generování metadat typu pro dekorátory.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Vynucuje použití indexovaných přístupových objektů pro klíče deklarované přes indexovaný typ.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Zajistěte označení přepisovaných členů v odvozených třídách modifikátorem override.", "Ensure_that_casing_is_correct_in_imports_6637": "Při importu ověřovat správnost používání malých a velkých písmen.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Zajistit bezpečnou transpilaci všech souborů bez spoléhání na jiné importy.", "Ensure_use_strict_is_always_emitted_6605": "<PERSON><PERSON><PERSON>ěte generování direktivy „use strict“.", "Entering_conditional_exports_6413": "Vstup do podmíněných exportů", "Entry_point_for_implicit_type_library_0_1420": "Vstupní bod pro knihovnu implicit<PERSON><PERSON><PERSON> typů {0}", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Vstupní bod pro knihovnu implicit<PERSON><PERSON><PERSON> typů {0} s packageId {1}", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Vstupní bod pro knihovnu typů {0} zadanou v compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Vstupní bod pro knihovnu typů {0} zadanou v compilerOptions s packageId {1}", "Enum_0_used_before_its_declaration_2450": "Výčet {0} se p<PERSON>žívá dříve, než se deklaruje.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Deklarace výčtu jdou sloučit jenom s oborem názvů nebo jinými deklaracemi výčtu.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Všechny deklarace výčtu musí být konstantní nebo nekonstantní.", "Enum_member_expected_1132": "Očekává se člen výčtu.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "K<PERSON>ž je povolená možnost „isolatedModules“, musí mít člen výčtu následující po neliterálovém číselném členu inicializátor.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "Inicializátory členů výčtu musí být počítatelné bez odkazů na externí symboly s možností „--isolatedDeclarations“.", "Enum_member_must_have_initializer_1061": "Člen výčtu musí mít inicializátor.", "Enum_name_cannot_be_0_2431": "Název výčtu nemůže být {0}.", "Errors_Files_6041": "Soubory chyb", "Escape_sequence_0_is_not_allowed_1488": "Řídicí sekvence „{0}“ není povolen<PERSON>.", "Examples_Colon_0_6026": "Příklady: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "Nadměrná složitost při porovnávání typů „{0}“ a „{1}“.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Nadměrná hloubka zásobníku při porovnávání typů {0} a {1}", "Exiting_conditional_exports_6416": "Opuštění podmíněných exportů.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Očekávané argumenty typu {0}–{1}; zadejte je se znač<PERSON>u @extends.", "Expected_0_arguments_but_got_1_2554": "<PERSON><PERSON><PERSON><PERSON><PERSON> se tento počet argumentů: {0}. Počet předaných argumentů: {1}", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "<PERSON><PERSON><PERSON><PERSON><PERSON> se tento počet argumentů: {0}, ale byl přijat tento počet: {1}. Nezapomněli jste zahrnout void do argumentu typu pro objekt Promise?", "Expected_0_type_arguments_but_got_1_2558": "Očekávaly se argumenty typu {0}, ale předaly se argumenty typu {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Očekávané argumenty typu {0}; zadejte je se znač<PERSON>u @extends.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Očekával se 1 argument, ale bylo jich 0. New Promise() potřebuje pomocný parametr JSDoc k vytvoření resolve, který se dá volat bez argumentů.", "Expected_a_Unicode_property_name_1523": "<PERSON><PERSON> očekáván název vlastnosti Unicode.", "Expected_a_Unicode_property_name_or_value_1527": "<PERSON><PERSON> <PERSON> název nebo hodnota vlastnosti Unicode.", "Expected_a_Unicode_property_value_1525": "<PERSON><PERSON>vána hodnota vlastnosti Unicode.", "Expected_a_capturing_group_name_1514": "<PERSON><PERSON> <PERSON><PERSON><PERSON>ván název zachycující skupiny.", "Expected_a_class_set_operand_1520": "By<PERSON> očekáván operand nastavení třídy.", "Expected_at_least_0_arguments_but_got_1_2555": "Očekával se aspoň tento počet argumentů: {0}. Počet předaných argumentů: {1}", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Očekávala se odpovídající ukončující značka JSX pro {0}.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Pro fragment JSX se očekávala odpovídající uzavírací značka.", "Expected_for_property_initializer_1442": "Pro inicializátor vlastnosti se očekával znak =.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Očekávaný typ pole {0} v souboru package.json byl {1}, získal se typ {2}.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Explicit<PERSON><PERSON> zadaný druh překladu modulu: {0}.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Pokud není možnost target nastavená na es2016 nebo novější, nedají se hodnoty bigint umocnit.", "Export_0_from_module_1_90059": "Exportovat {0} z modulu {1}", "Export_all_referenced_locals_90060": "Exportovat všechny odkazované místní hodnoty", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Přiřazení exportu nelze použít, pokud jsou cílem moduly ECMAScript. Zkuste místo toho použít export default nebo jiný formát modulu.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "<PERSON><PERSON>ž má příznak --module hodnotu system, nepodporuje se přiřazení exportu.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Konflikty deklarace exportu s exportovanou deklarací {0}", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Deklarace exportu nejsou povolené v oboru názvů.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "Specifikátor exportu {0} neexistuje v package.json scope na cestě {1}.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "<PERSON><PERSON> typu {0} má nebo používá privátní název {1}.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "<PERSON><PERSON> typu {0} má nebo používá privátní název {1} z modulu {2}.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Exportovaná proměnná {0} má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Exportovaná proměnná {0} má nebo používá název {1} z privátního modulu {2}.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Exportovaná proměnná {0} má nebo používá privátní název {1}.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Exporty a přiřazení exportů nejsou povolené v rozšířeních modulů.", "Expression_expected_1109": "Očekával se výraz.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "Výraz musí být uzavřen v závorkách, aby se mohl používat jako dekoratér.", "Expression_or_comma_expected_1137": "Očekával se výraz nebo čárka.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "Výraz vytvoří typ řazené kolekce členů, k<PERSON><PERSON> se ned<PERSON> reprezent<PERSON>t, proto<PERSON>e je p<PERSON><PERSON><PERSON><PERSON> velk<PERSON>.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "Výraz vytvoří typ sjedn<PERSON>ení, k<PERSON><PERSON> se ned<PERSON> reprez<PERSON>t, proto<PERSON>e je p<PERSON><PERSON><PERSON> s<PERSON>.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Výraz se přeloží na identifikátor _super, pomocí kterého kompilátor zaznamenává odkaz na základní třídu.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "Výraz se vyhodnocuje na deklaraci proměnné _newTarget, kterou kompilátor p<PERSON>žívá k zachycení odkazu na metavlastnost new.target.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Výraz se přeloží na deklaraci proměnné _this, pomo<PERSON><PERSON> které kompilátor zaznamenává odkazy na příkaz this.", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "Typ výrazu nejde odvodit pomocí --isolatedDeclarations.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "Klau<PERSON>le Extends nemůže obsahovat výraz s možností „--isolatedDeclarations“.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "<PERSON><PERSON><PERSON><PERSON> extends pro odvozený typ „{0}“ má nebo používá privátní název „{1}“.", "Extract_base_class_to_variable_90064": "Extrahovat základní třídu do proměnné", "Extract_binding_expressions_to_variable_90066": "Extrahujte výrazy s vazbami do proměnné", "Extract_constant_95006": "Extrahovat konstantu", "Extract_default_export_to_variable_90065": "Extrahovat výchozí export do proměnné", "Extract_function_95005": "Extrahovat funkci", "Extract_to_0_in_1_95004": "Extrahovat do {0} v {1}", "Extract_to_0_in_1_scope_95008": "Extrahovat do {0} v oboru {1}", "Extract_to_0_in_enclosing_scope_95007": "Extrahovat do {0} v nadřazeném oboru", "Extract_to_interface_95090": "Extrahovat do rozhraní", "Extract_to_type_alias_95078": "Extrahovat do aliasu typu", "Extract_to_typedef_95079": "Extrahovat do typedef", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "Extrahovat do proměnné a nahradit pomocí „{0} as typeof {0}“", "Extract_type_95077": "Typ extrahování", "FILE_6035": "SOUBOR", "FILE_OR_DIRECTORY_6040": "SOUBOR NEBO ADRESÁŘ", "Failed_to_find_peerDependency_0_6283": "Nepodařilo se najít peerDependency „{0}“.", "Failed_to_resolve_under_condition_0_6415": "Nepodařilo se přeložit za podmínky „{0}“.", "Fallthrough_case_in_switch_7029": "Případ Fallthrough v příkazu switch", "File_0_does_not_exist_6096": "Soubor {0} neexistuje.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Podle dřívějších vyhledávání v mezipaměti soubor {0} neexistuje.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Podle dřívějších vyhledávání v mezipaměti soubor {0} existuje.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "<PERSON><PERSON><PERSON> „{0}“ existuje – použijte ho jako výsledek překladu IP adres.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "<PERSON><PERSON>or {0} má nepodporovanou příponu. <PERSON><PERSON><PERSON> přípony jsou {1}.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "Soubor {0} je javascriptový soubor. Nechtěli jste povolit možnost allowJs?", "File_0_is_not_a_module_2306": "<PERSON><PERSON><PERSON> {0} nen<PERSON> modul.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "Soubor {0} není uvedený na seznamu souborů projektu {1}. Projekty musí uvádět všechny soubory nebo používat vzor include.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Soubor {0} není pod kořenovým adresářem rootDir {1}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se, že rootDir bude obsahovat všechny zdrojové soubory.", "File_0_not_found_6053": "<PERSON><PERSON><PERSON> {0} se ne<PERSON><PERSON><PERSON>.", "File_Management_6245": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "File_appears_to_be_binary_1490": "<PERSON><PERSON><PERSON> <PERSON>, že soubor je binární.", "File_change_detected_Starting_incremental_compilation_6032": "Zjistila se změna souboru. Spouští se přírůstková kompilace...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "<PERSON><PERSON>or je modul CommonJS, proto<PERSON>e {0} nemá pole type", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Soubor je modul CommonJS, proto<PERSON><PERSON> {0} má pole type, jehož hodnota není module", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Soubor je modul CommonJS, protože se nenašel package.json", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Soubor je modul ECMAScript, protože {0} má pole type s hodnotou module", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Soubor je modul CommonJS; může být převeden na modul ES.", "File_is_default_library_for_target_specified_here_1426": "<PERSON>ubor je výchozí knihovna pro cíl, k<PERSON><PERSON> se zadal na tomto místě.", "File_is_entry_point_of_type_library_specified_here_1419": "<PERSON><PERSON><PERSON> je vstupní bod knihovny typů, kter<PERSON> se zadala na tomto místě.", "File_is_included_via_import_here_1399": "Soubor se zahrnuje pomocí importu na tomto místě.", "File_is_included_via_library_reference_here_1406": "Soubor se zahrnuje pomocí odkazu na knihovnu na tomto místě.", "File_is_included_via_reference_here_1401": "Soubor se zahrnuje pomocí odkazu na tomto místě.", "File_is_included_via_type_library_reference_here_1404": "Soubor se zahrnuje pomocí odkazu na knihovnu typů na tomto místě.", "File_is_library_specified_here_1423": "<PERSON><PERSON>or je knihovna zadaná na tomto místě.", "File_is_matched_by_files_list_specified_here_1410": "Soubor se srovnává se seznamem files zadaným na tomto místě.", "File_is_matched_by_include_pattern_specified_here_1408": "Soubor se srovnává podle vzoru zahrnutí zadaného na tomto místě.", "File_is_output_from_referenced_project_specified_here_1413": "Soubor je výstup z odkazovaného projektu zadaného na tomto místě.", "File_is_output_of_project_reference_source_0_1428": "Soubor je výstup zdroje odkazů na projekt {0}.", "File_is_source_from_referenced_project_specified_here_1416": "Soubor je zdroj z odkazovaného projektu zadaného na tomto místě.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Název souboru {0} se od už zahrnutého názvu souboru {1} li<PERSON><PERSON> jenom velikostí písmen.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>u „{0}“ m<PERSON> příponu „{1}“ – místo toho se hledá: „{2}“.", "File_name_0_has_a_1_extension_stripping_it_6132": "<PERSON><PERSON><PERSON><PERSON> souboru {0} m<PERSON> příponu {1} – odstraňuje se", "File_redirects_to_file_0_1429": "Soubor se přesměrovává na soubor {0}.", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "Specifikace souboru nemůže obsahovat nadřazený adresář (..), který se vyskytuje za rekurzivním zástupným znakem adresáře (**): {0}.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "Specifikace souboru nemůže končit rekurzivním zástupným znakem adresáře (**): {0}.", "Filters_results_from_the_include_option_6627": "Filtrovat výsledky možnosti „zahrnout“.", "Fix_all_detected_spelling_errors_95026": "Opravit všechny zjištěné pravopisné chyby", "Fix_all_expressions_possibly_missing_await_95085": "Opravit všechny výrazy, kde je mož<PERSON>, že ch<PERSON><PERSON><PERSON> await", "Fix_all_implicit_this_errors_95107": "Opravit všechny chyby implicit-'this'", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Opravit všechny nesprávné návratové typy asynchron<PERSON><PERSON><PERSON>", "Fix_all_with_type_only_imports_95182": "Opravit vše s importy „type-only“", "Found_0_errors_6217": "<PERSON><PERSON><PERSON> se tento počet chyb: {0}.", "Found_0_errors_Watching_for_file_changes_6194": "<PERSON>l nalezen tento počet chyb: {0}. Sledují se změny souborů.", "Found_0_errors_in_1_files_6261": "V {1} souborech byly na<PERSON><PERSON>y chyby ({0}).", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "<PERSON>e stejném souboru byly na<PERSON><PERSON> chyby ({0}). Začínají na: {1}", "Found_1_error_6216": "Našla se 1 chyba.", "Found_1_error_Watching_for_file_changes_6193": "Byla nalezena 1 chyba. Sledují se změny souborů.", "Found_1_error_in_0_6259": "Našla se 1 chyba v {0}.", "Found_package_json_at_0_6099": "Soubor package.json se našel v {0}.", "Found_peerDependency_0_with_1_version_6282": "<PERSON><PERSON><PERSON><PERSON>: peerDependency – „{0}“ s verz<PERSON> „{1}“", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "Deklarace <PERSON>í nejsou povolené uvnitř bloků ve striktním režimu, pokud je cíl „ES5“.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "Deklarace funkcí nejsou povolené uvnitř bloků ve striktním režimu, pokud je cíl „ES5“. Definice tříd jsou automaticky ve striktním režimu.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "Deklarace funkcí nejsou povolené uvnitř bloků ve striktním režimu, pokud je cíl „ES5“. <PERSON><PERSON><PERSON> jsou automaticky ve striktním režimu.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Výraz funkce s chybějící anotací návratového typu má implicitně návratový typ {0}.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Implementace funkce chybí nebo nenásleduje hned po deklaraci.", "Function_implementation_name_must_be_0_2389": "Název implementace funkce musí b<PERSON>t {0}.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Funkce implicitně obsahuje návratový typ any, proto<PERSON>e neobsahuje anotaci návratového typu a odkazuje se na ni přímo nebo nepřímo v jednom z jejích návratových výrazů.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Ve funkci chybí koncový příkaz return a návratový typ neobsahuje undefined.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "Funkce musí mít explicitní anotaci návratového typu s možností „--isolatedDeclarations“.", "Function_not_implemented_95159": "<PERSON><PERSON> není <PERSON>.", "Function_overload_must_be_static_2387": "Přetížení <PERSON>ce musí být <PERSON>.", "Function_overload_must_not_be_static_2388": "Přetížení <PERSON>ce nesmí b<PERSON>t <PERSON>.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "<PERSON><PERSON><PERSON> se notace typu funkce používá v typu sjednocení, musí být uzavřená do závorky.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "<PERSON><PERSON>ž se notace typu funkce používá v typu průniku, musí být uzavřená do závorky.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Typ funkce s chybějící anotací návratového typu má implicitně návratový typ {0}.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "<PERSON><PERSON> s těly se dá sloučit jenom s tř<PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON>u <PERSON>.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Vygenerujte ze souborů TypeScriptu a JavaScriptu projektu soubory d.ts.", "Generate_get_and_set_accessors_95046": "Generovat přístupové objekty get a set", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Generovat přístupové objekty get a set pro všechny přepisující vlastnosti", "Generates_a_CPU_profile_6223": "Vygeneruje profil procesoru.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Pro každý odpovídající soubor .d.ts vygeneruje sourcemap.", "Generates_an_event_trace_and_a_list_of_types_6237": "Generuje trasování události a seznam typů.", "Generates_corresponding_d_ts_file_6002": "Generuje odpovídající soubor .d.ts.", "Generates_corresponding_map_file_6043": "Generuje odpovídající soubor .map.", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "Generátor má implicitně typ yield {0}. Zvažte možnost přidat anotaci návratového typu.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Generátory nejsou v ambientním kontextu povolené.", "Generic_type_0_requires_1_type_argument_s_2314": "Obecný typ {0} vyžaduje argumenty typu {1}.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Obecný typ {0} vyžaduje konkrétní počet argumentů ({1} až {2}).", "Global_module_exports_may_only_appear_at_top_level_1316": "Exporty globálního modulu se můžou objevit jenom na nejvyšší úrovni.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Exporty globálního modulu se můžou objevit jenom v souborech deklarací.", "Global_module_exports_may_only_appear_in_module_files_1314": "Exporty globálního modulu se můžou objevit jenom v souborech modulů.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Globální typ {0} musí být typu třída nebo rozhraní.", "Global_type_0_must_have_1_type_parameter_s_2317": "Globální typ {0} musí mít parametry typu {1}.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Opakovan<PERSON> kompilace --incremental a --watch p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dají, že změny v souboru budou mít vliv jen na soubory, které na něm přímo závisejí.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Opakované kompilace v projektech, které používají režimy „incremental“ a „watch“ předpokládají, že změny v souboru budou mít vliv pouze na soubory, které na daném souboru přímo závisejí.", "Hexadecimal_digit_expected_1125": "Očekávala se šestnáctková číslice.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "<PERSON><PERSON><PERSON>ával se identifikátor. {0} je vyhrazené slovo na nejvyšší úrovni modulu.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Očekával se identifikátor. Ve striktním režimu je {0} rezervované slovo.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Očekával se identifikátor. Ve striktním režimu je {0} rezervované slovo. Definice tříd jsou automaticky ve striktním režimu.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Očekával se identifikátor. Ve striktním režimu je {0} rezervované slovo. Moduly jsou automaticky ve striktním režimu.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Očekává se identifikátor. {0} je rezervo<PERSON> slovo, které se tady nedá p<PERSON>.", "Identifier_expected_1003": "Očekával se identifikátor.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Očekává se identifikátor. __esModule je při transformaci modulů ECMAScript rezervované jako označení exportu.", "Identifier_or_string_literal_expected_1478": "Očekává se identifikátor nebo řetězcový literál.", "Identifier_string_literal_or_number_literal_expected_1496": "Očekával se identifikátor, řetězcový literál nebo číselný literál.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Pokud b<PERSON><PERSON> ‚{0}‘ ve skutečnosti zveřejňuje tento modul, zvažte možnost poslat žádost o přijetí změn, aby se připojila adresa ‚https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}‘", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Poku<PERSON> {0} skutečně zpřístupňuje tento modul, zkuste přidat nový soub<PERSON> (.d.ts), k<PERSON><PERSON> obsahu<PERSON> declare module {1};", "Ignore_this_error_message_90019": "Ignorovat tuto chybovou zprávu", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignoruje se tsconfig.js, zkompiluje zadané soubory s výchozími možnostmi kompilátoru.", "Implement_all_inherited_abstract_classes_95040": "Implementovat všechny zděděné abstraktní třídy", "Implement_all_unimplemented_interfaces_95032": "Implementovat všechna neimplementovaná rozhraní", "Implement_inherited_abstract_class_90007": "Implementovat zděděnou abstraktní třídu", "Implement_interface_0_90006": "Implementovat rozhraní {0}", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "Klauzule implements exportované třídy {0} má nebo používá privátní název {1}.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "Implicit<PERSON><PERSON> p<PERSON> symbol na string za běhu neproběhne úspěšně. Zvažte možnost zabalit tento výraz do String(...).", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "Import „{0}“ je v konfliktu s globální hodnotou použitou v tomto souboru, proto se musí deklarovat s importem „type-only“, pokud je povolena možnost „isolatedModules“.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "Import „{0}“ je v konfliktu s lokální hodnotou, proto se musí deklarovat s importem „type-only“, pokud je povolena možnost „isolatedModules“.", "Import_0_from_1_90013": "Importovat {0} z: {1}", "Import_assertion_values_must_be_string_literal_expressions_2837": "Hodnoty kontrolních výrazů importu musí být výrazy formou řetězcových literálů.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "V příkazech, které se kompilují na volání CommonJS „require“, se nepovolují kontrolní výrazy importu.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "Kontrolní výrazy importu jsou podporovány pouze v případě, že je možnost --module nastavena na esnext, node18, nodenext nebo preserve.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Kontrolní výrazy importu se nedají použít s importy nebo exporty, kter<PERSON> jsou jenom typ.", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "Kontrolní výrazy importu byly nahrazeny atributy importu. <PERSON><PERSON><PERSON> assert použijte with.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Přiřazení importu nelze p<PERSON>, pokud jsou cílem moduly ECMAScript. Zkuste místo toho použít import * as ns from \"mod\", import {a} from \"mod\", import d from \"mod\" nebo jiný formát modulu.", "Import_attribute_values_must_be_string_literal_expressions_2858": "Hodnoty atributů importu musí být výrazy formou řetězcových literálů.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "V příkazech, které se kompilují na volání CommonJS „require“, se nepovolují atributy importu.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "Atributy importu jsou podporovány pouze v případě, že je možnost --module nastavena na esnext, node18, nodenext nebo preserve.", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "Atributy importu se nedají použít s importy nebo exporty „type-only“.", "Import_declaration_0_is_using_private_name_1_4000": "<PERSON><PERSON><PERSON>u {0} používá privátní název {1}.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "Deklarace importu je v konfliktu s místní deklarací {0}.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Deklarace importu v oboru názvů nemůžou odkazovat na modul.", "Import_emit_helpers_from_tslib_6139": "Importovat pomocné rutiny pro generování z tslib", "Import_may_be_converted_to_a_default_import_80003": "Import se může převést na výchozí import.", "Import_name_cannot_be_0_2438": "Název importu nemůže b<PERSON>t {0}.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Deklarace importu nebo exportu v deklaraci ambientního modulu nemůže odkazovat na modul pomocí jeho relativního názvu.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "Specifikátor importu {0} neexistuje v package.json scope na cestě {1}.", "Imported_via_0_from_file_1_1393": "Import<PERSON><PERSON> přes {0} ze souboru {1}", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Importováno přes {0} ze souboru {1}, aby se provedl import importHelpers tak, jak je to zadáno v compilerOptions", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Import<PERSON><PERSON> přes {0} ze souboru {1}, aby se <PERSON>l import v<PERSON><PERSON>bn<PERSON><PERSON> funkcí jsx a jsxs", "Imported_via_0_from_file_1_with_packageId_2_1394": "Import<PERSON><PERSON> přes {0} ze souboru {1} s packageId {2}", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Importováno přes {0} ze souboru {1} s packageId {2}, aby se provedl import importHelpers tak, jak je to zadáno v compilerOptions", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Importová<PERSON> přes {0} ze souboru {1} s packageId {2}, aby se provedl import vý<PERSON>bních funkcí jsx a jsxs", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "Import souboru JSON do modulu ECMAScript vyžaduje atribut importu type: \"json\", pokud je možnost module nastavená na {0}.", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Importy nejsou povolené v rozšířeních modulů. Zvažte jejich přesunutí do uzavírajícího externího modulu.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Inicializátor členu v deklaracích ambientního výčtu musí být konstantní výraz.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "Ve výčtu s víc deklaracemi může být jenom u jedné deklarace vynechaný inicializátor u prvního elementu výčtu.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Zahrnout seznam souborů. <PERSON>to mož<PERSON>t, na rozdíl od možnosti „include“, nepodporuje vzory glob.", "Include_modules_imported_with_json_extension_6197": "Zahrnout moduly importované s příponou .json", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Do souborů sourcemap v generovaném JavaScriptu zahrňte zdrojový kód.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Zahrňte do generovaného JavaScriptu soubory sourcemap.", "Includes_imports_of_types_referenced_by_0_90054": "Zahrnuje importy typů, na které odkazuje {0}", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Včetně --watch, -w začne sledovat aktuální projekt ohledně změn souboru. Po nastavení můžete konfigurovat režim sledování pomocí:", "Incomplete_quantifier_Digit_expected_1505": "Neúplný kvantifikátor <PERSON>v<PERSON> se číslice.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "Signatura indexu pro typ {0} chybí v typu {1}.", "Index_signature_in_type_0_only_permits_reading_2542": "Signatura indexu v typu {0} povoluje jen čtení.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Jednotli<PERSON>é de<PERSON> ve sloučené de<PERSON> {0} musí být všechny exportované nebo všechny místní.", "Infer_all_types_from_usage_95023": "Odvodit všechny typy z použití", "Infer_function_return_type_95148": "Odvodit návratový typ funkce", "Infer_parameter_types_from_usage_95012": "Odvodit typy parametrů z využití", "Infer_this_type_of_0_from_usage_95080": "Vyvodit typ this pro {0} z použití", "Infer_type_of_0_from_usage_95011": "Odvodit typ {0} z využití", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "Odvozování z výrazů tříd není podporováno s možností „--isolatedDeclarations“.", "Initialize_property_0_in_the_constructor_90020": "Inicializovat vlastnost {0} v konstruktoru", "Initialize_static_property_0_90021": "Inicializovat statickou vlastnost {0}", "Initializer_for_property_0_2811": "Inici<PERSON><PERSON><PERSON><PERSON> „{0}“", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "Inicializátor instance členské proměnné {0} nemůže odkazovat na identifikátor {1} deklarovaný v konstruktoru.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Inicializátory nejsou povolené v ambientních kontextech.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Inicializuje projekt TypeScript a vytvoří soubor tsconfig.json.", "Inline_variable_95184": "Vložená proměnná", "Insert_command_line_options_and_files_from_a_file_6030": "Vložte parametry příkazového řádku a soubory ze souboru.", "Install_0_95014": "<PERSON>ins<PERSON>ovat {0}", "Install_all_missing_types_packages_95033": "Nainstalovat všechny chybějící balíčky typů", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "Rozhraní {0} nemůže současně rozšiřovat typ {1} i {2}.", "Interface_0_incorrectly_extends_interface_1_2430": "Rozhraní {0} nesprávně rozši<PERSON><PERSON><PERSON> rozhraní {1}.", "Interface_declaration_cannot_have_implements_clause_1176": "De<PERSON>race rozhraní nemůže obsahovat klauzuli implements.", "Interface_must_be_given_a_name_1438": "Rozhraní musí mít název.", "Interface_name_cannot_be_0_2427": "Název rozhraní nemů<PERSON>e být {0}.", "Interop_Constraints_6252": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interpretujte volitelné typy vlastností jako <PERSON>, místo přid<PERSON> „undefined“.", "Invalid_character_1127": "Neplatný znak", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Neplatný specifikátor importu {0} nemá žádná možná řešení.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Neplatný název modulu v rozšíření. Modul {0} se převede na netypový modul v {1}, který se nedá rozšířit.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "V rozšíření je neplatný název modulu, modul {0} se nedá najít.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Neplatný volitelný řetěz z nového výrazu. Chtěli jste volat {0}()?", "Invalid_reference_directive_syntax_1084": "Neplatná syntaxe direktivy reference", "Invalid_syntax_in_decorator_1498": "Neplatná syntaxe v dekoratéru", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Neplatné <PERSON> „{0}“. Nelze jej použít uvnitř statického bloku třídy.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Neplatn<PERSON> {0}. <PERSON><PERSON><PERSON> jso<PERSON>ky ve striktním režimu.", "Invalid_use_of_0_in_strict_mode_1100": "Neplatné p<PERSON> {0} ve striktním režimu", "Invalid_value_for_ignoreDeprecations_5103": "Neplatná hodnota pro možnost „--ignoreDeprecations“", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Neplatná hodnota pro jsxFactory. {0} není platný identifikátor nebo kvalifikovaný název.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Neplatná hodnota pro jsxFragmentFactory. {0} není platný identifikátor nebo kvalifikovaný název.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Neplatná hodnota --reactNamespace. {0} není platný identifikátor.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Pravděpo<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON> tyto dva výrazy šablony. Tvoří výraz šablony se značkami, který se nedá vyvolat.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Typ prvku {0} není platný prvek JSX.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Typ instance {0} není platný prvek JSX.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Návratový typ {0} není platný prvek JSX.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "<PERSON>p „{0}“ není platný typ elementu JSX.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "Značka JSDoc @{0} {1} neodpovídá klauzuli extends {2}.", "JSDoc_0_is_not_attached_to_a_class_8022": "Značka JSDoc @{0} není připojená k třídě.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc ... se může nacházet jen v posledním parametru signatury.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "Značka JSDoc @param má název {0}, ale neexistuje <PERSON>dný parametr s tímto názvem.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "Značka JSDoc @param má název {0}, ale žádný parametr s tímto názvem neexistuje. Musí odpovídat hodnotě arguments, pokud má typ pole.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "JSDoc typedef se dá převést na typ TypeScript.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "Značka JSDoc @typedef by m<PERSON><PERSON> mít poznámku k typu nebo by za n<PERSON> měly n<PERSON><PERSON><PERSON>t z<PERSON><PERSON><PERSON> @property nebo @member.", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "JSDoc typedef lze převádět na typy TypeScript.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Typy JSDoc se můžou používat jenom v dokumentačních komentářích.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Typy JSDoc se můžou přesunout na typy TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Atributy JSX musí mít přiřazený neprázdný výraz.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "Element JSX {0} nemá odpovídající uzavírací značku.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "Třída elementu JSX nepodporuje atributy, protože nemá vlastnost {0}.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "Element JSX má implicitně typ any, protože neexistuje ž<PERSON>dné rozhraní JSX.{0}.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "Element JSX má implicitně typ any, protože neexistuje globální typ JSX.Element.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Typ elementu JSX {0} nemá žádnou signaturu konstrukce nebo volání.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Elementy JSX nemůžou mít víc atributů se stejným názvem.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Výrazy JSX nemůžou používat operátor <PERSON>. Nechtěli jste napsat pole?", "JSX_expressions_must_have_one_parent_element_2657": "Výrazy JSX musí mít jeden nadřazený element.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Fragment JSX nemá odpovídající uzavírací značku.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Výrazy přístupu k vlastnosti JSX nemůžou obsahovat názvy oborů názvů JSX.", "JSX_spread_child_must_be_an_array_type_2609": "Podřízený objekt JSX spread musí být typu pole.", "JavaScript_Support_6247": "Podpora JavaScriptu", "Jump_target_cannot_cross_function_boundary_1107": "Cíl odkazu nemůže překročit hranici funkce.", "KIND_6034": "DRUH", "Keywords_cannot_contain_escape_characters_1260": "<PERSON><PERSON><PERSON><PERSON><PERSON> slova nemůžou obsahovat řídicí znaky.", "LOCATION_6037": "UMÍSTĚNÍ", "Language_and_Environment_6254": "Jazyk a prostředí", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Levá strana operátoru čárky se nepoužívá a nemá žádné vedlejší <PERSON>.", "Library_0_specified_in_compilerOptions_1422": "Knihovna {0} zadaná v compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Knihovna odkazovaná přes {0} ze souboru {1}", "Line_break_not_permitted_here_1142": "Na tomto místě se konec řádku nepovoluje.", "Line_terminator_not_permitted_before_arrow_1200": "Konec řádku před šipkou se nepovoluje.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Seznam přípon n<PERSON><PERSON><PERSON>, které se mají v<PERSON>hledat při překladu modulu", "List_of_folders_to_include_type_definitions_from_6161": "Seznam složek, ze kterých se zahrnou definice typů", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Seznam koř<PERSON><PERSON><PERSON><PERSON> slo<PERSON>ek, j<PERSON><PERSON><PERSON> k<PERSON>ý obsah představuje strukturu projektu za běhu", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Nač<PERSON>t<PERSON> se {0} z kořenového adresáře {1}, umíst<PERSON><PERSON><PERSON> kandi<PERSON> {2}.", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "Načítá se modul „{0}“ ze složky node_modules. Cílové typy souborů: {1}.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "Načítá se modul jako soubor/s<PERSON><PERSON><PERSON>, umístěn<PERSON> kandidátského modulu: „{0}“, cílové typy souborů: {1}.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Národní prostředí musí mít tvar <jazyk> nebo <jazyk>–<oblast>. Třeba {0} nebo {1}.", "Log_paths_used_during_the_moduleResolution_process_6706": "Cesty protokolu používané v procesu moduleResolution.", "Longest_matching_prefix_for_0_is_1_6108": "Nejdelší odpovídající předpona pro {0} je {1}.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Hledání ve složce node_modules, počáteční umístění {0}", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Nastavit všechna volání metody super() prvním příkazem v jejich konstruktoru", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "Vytvořte klíč jenom ze zpětných řetězců místo z řetězců, č<PERSON>el nebo symbolů (možnost ze starší verze).", "Make_super_call_the_first_statement_in_the_constructor_90002": "Nastavit volání metody super() jako první příkaz v konstruktoru", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "<PERSON><PERSON> mapovaného objektu má implicitně typ šablony any.", "Mark_array_literal_as_const_90070": "Označit literál pole jako const", "Matched_0_condition_1_6403": "Odpovídá {0} podmínce {1}", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Porovnává se ve výchozím nastavení se vzorem zahrnutí **/*.", "Matched_by_include_pattern_0_in_1_1407": "Porovnáváno podle vzoru zahrnutí {0} v {1}", "Member_0_implicitly_has_an_1_type_7008": "<PERSON><PERSON> {0} má implicitně typ {1}.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "<PERSON><PERSON> {0} má implicitně typ {1}, ale je možn<PERSON>, že lep<PERSON> typ by se vyvodil z použití.", "Merge_conflict_marker_encountered_1185": "Zjistila se značka konfliktu sloučení.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "Spojená deklarace {0} nemůže obsahovat výchozí deklaraci exportu. Zvažte namísto toho možnost přidat samostatnou deklaraci export default {0}.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "Metavlastnost {0} je povolená jenom v těle deklarace funkce, výrazu funkce nebo konstruktoru.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "Metoda {0} ne<PERSON><PERSON><PERSON><PERSON> m<PERSON>, proto<PERSON>e je označená jako abstraktní.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Metoda {0} z exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Metoda {0} z exportovaného rozhraní má nebo používá privátní název {1}.", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "Metoda musí mít explicitní anotaci návratového typu s možností „--isolatedDeclarations“.", "Method_not_implemented_95158": "Metoda není <PERSON>.", "Modifiers_cannot_appear_here_1184": "<PERSON><PERSON> p<PERSON>t modifik<PERSON>.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "Modul {0} se dá importovat podle výchozího nastavení jen pomocí příznaku {1}.", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Modul {0} nejde importovat pomocí této konstrukce. Specifikátor se převede jenom na modul ES, který se nedá importovat s příkazem require. Místo toho použijte import ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "Modul {0} deklaruje {1} místně, ale exportuje se jako {2}.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "Modul {0} deklaruje {1} místně, ale neexportuje se.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "Modul {0} neo<PERSON><PERSON><PERSON><PERSON> na typ, ale používá se tady jako typ. Měli jste na mysli typeof import('{0}')?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "Modul {0} neodka<PERSON><PERSON> na hodnotu, ale používá se tady jako hodnota.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Modul {0} už exportoval člena s názvem {1}. Zvažte možnost vyřešení nejednoznačnosti explicitním opakováním exportu.", "Module_0_has_no_default_export_1192": "Modul {0} nemá žádný výchozí export.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "Modul {0} nemá žádný výchozí export. Nechtěli jste místo toho použít import { {1} } from {0}?", "Module_0_has_no_exported_member_1_2305": "V modulu {0} není žádný exportovaný člen {1}.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "Modul {0} nemá žádný exportovaný člen {1}. Nechtěli jste místo toho použít import { {1} } from {0}?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "<PERSON><PERSON>l {0} je skrytý místní dekla<PERSON> se stejným názvem.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Modul {0} používá export = a nedá se použít s možností export *.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Modul {0} se převedl jako lokálně deklarovaný ambientní modul v souboru {1}.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "<PERSON><PERSON><PERSON> „{0}“ byl př<PERSON> na „{1}“, ale není nastavena možnost „--allowArbitraryExtensions“.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "<PERSON><PERSON><PERSON> {0} se p<PERSON><PERSON>žil na {1}, nen<PERSON> ale nastavená možnost --jsx.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "Modul {0} se př<PERSON>žil na {1}, ale nepoužívá se --resolveJsonModule.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Názvy deklarací modulů můžou používat jenom řetězce v jednoduchých nebo dvojitých uvozovkách.", "Module_name_0_matched_pattern_1_6092": "Název modulu {0}, odpovídající vzor {1}", "Module_name_0_was_not_resolved_6090": "======== Název modulu {0} nebyl přeložen. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Název modulu {0} byl úsp<PERSON>šně přeložen na {1}. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Název modulu {0} se úspěšně přeložil na {1} s ID balíčku {2}. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "<PERSON>uh překladu modulu nebyl určen, pou<PERSON><PERSON><PERSON> se {0}.", "Module_resolution_using_rootDirs_has_failed_6111": "Překlad modulu pomocí rootDirs se nepovedl.", "Modules_6244": "<PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Přesunout modifikátory elementu popsané řazené kolekce členů na popisky", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "Přesuňte výraz ve výchozím exportu do proměnné a přidejte k němu anotaci typu.", "Move_to_a_new_file_95049": "Přesunout do nového souboru", "Move_to_file_95178": "Přesunout do souboru", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Více po sobě j<PERSON><PERSON><PERSON><PERSON> č<PERSON>ý<PERSON> oddělov<PERSON> se nepovoluje.", "Multiple_constructor_implementations_are_not_allowed_2392": "Víc implementací konstruktoru se nepovoluje.", "NEWLINE_6061": "NOVÝ ŘÁDEK", "Name_is_not_valid_95136": "<PERSON><PERSON><PERSON>v nen<PERSON> p<PERSON>.", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "Pojmenované zachytávací skupiny jsou k dispozici jen při cílení na „ES2018“ nebo novější.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "Pojmenované zachytávací skupiny se stejným názvem se musí navzájem vylučovat.", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "Pojmenované importy ze souboru JSON do modulu ECMAScript nejsou povolené, k<PERSON><PERSON> je možnost module nastavená na {0}.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Pojmenovaná vlastnost {0} není u typu {1} stejná jako u typu {2}.", "Namespace_0_has_no_exported_member_1_2694": "<PERSON><PERSON> {0} nemá žádný exportovaný člen {1}.", "Namespace_must_be_given_a_name_1437": "<PERSON><PERSON> n<PERSON>v<PERSON> musí mít název.", "Namespace_name_cannot_be_0_2819": "Název oboru n<PERSON>zvů ne<PERSON>ů<PERSON> být „{0}“.", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "Pokud je povolena možnost „{0}“, nejsou v souborech globálních skriptů povoleny obory názvů. Pokud tento soubor nemá být globálním skriptem, nastavte možnost „moduleDetection“ na hodnotu „force“ nebo přidejte prázdný příkaz „export {}“.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "U parametrů „this“ nelze použít dekoratéry ani modifikátory.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Žádný základní konstruktor nemá zadaný počet argumentů typu.", "No_constituent_of_type_0_is_callable_2755": "Žádný konstituent typu {0} se ned<PERSON> zavolat.", "No_constituent_of_type_0_is_constructable_2759": "Žádný konstituent typu {0} se nedá vytvořit.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "V typu {1} se nenašla žádná signatura indexu s typem parametru {0}.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "V konfiguračním souboru {0} se nena<PERSON><PERSON> ž<PERSON> vstupy. Pro zahrnutí jsou zadané tyto cesty: {1} a pro vyloučení tyto cesty: {2}.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Funkce už není podpor<PERSON>ná. Ve starších verzích sloužila k ručnímu nastavení kódování textu při čtení souborů.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "<PERSON><PERSON><PERSON>é přetížení neočekává tento počet argumentů: {0}. Existují ale přetížení, kter<PERSON> očekávají buď {1}, nebo tento počet argumentů: {2}", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "<PERSON><PERSON><PERSON>é přetížení neočekává tento počet argumentů typů: {0}. Existují ale přetížení, kter<PERSON> o<PERSON>ekávají buď {1}, nebo tento počet argumentů typů: {2}", "No_overload_matches_this_call_2769": "<PERSON><PERSON><PERSON><PERSON> přetížení neodpovídá tomuto vol<PERSON>.", "No_type_could_be_extracted_from_this_type_node_95134": "Z tohoto uzlu typů nešlo extrahovat žádný typ.", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "V oboru pro sdruženou vlastnost {0} neexistuje žádná hodnota. Buď nějakou deklarujte, nebo poskytněte inicializátor.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "Neabstraktn<PERSON> tří<PERSON> „{0}“ neimplementuje zděděného abstraktního <PERSON> {1} ze tř<PERSON><PERSON> „{2}“.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "V neabstraktní tří<PERSON>ě „{0}“ chybí implementace pro následující <PERSON> „{1}“: {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "V neabstraktní třídě {0} chybí implementace pro následující <PERSON> „{1}“: {2} a {3} da<PERSON><PERSON><PERSON>(ch).", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Výraz neabstraktní třídy neimplementuje zděděný abstraktní člen {0} z třídy {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "Ve výrazu neabstraktní třídy ch<PERSON>í implementace pro následující č<PERSON>y „{0}“: {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "Ve výrazu neabstraktní třídy chybí implementace pro následující <PERSON> „{0}“: {1} a {2} da<PERSON><PERSON><PERSON>(ch).", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Kontrolní výrazy jiné než <PERSON> se dají používat jen v typescriptových souborech.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "Nerelativní cesty nejsou povolené, pokud není nastavená hodnota baseUrl. Nezapomněli jste na úvodní znak „./“?", "Non_simple_parameter_declared_here_1348": "<PERSON><PERSON><PERSON><PERSON> se tady parametr, k<PERSON><PERSON> nen<PERSON> j<PERSON>.", "Not_all_code_paths_return_a_value_7030": "Ne všechny cesty kódu vracejí hodnotu.", "Not_all_constituents_of_type_0_are_callable_2756": "Ne všichni konstituenti typu {0} se daj<PERSON> zavolat.", "Not_all_constituents_of_type_0_are_constructable_2760": "Ne všichni konstituenti typu {0} se dají v<PERSON>ř<PERSON>.", "Numbers_out_of_order_in_quantifier_1506": "Čísla ve kvantifikátoru jsou mimo pořadí.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Číselné liter<PERSON>ly s absolut<PERSON><PERSON><PERSON> hodnotami, kter<PERSON> se rovnají hodnotě 2^53 nebo v<PERSON><PERSON><PERSON><PERSON>, se nedají reprezentovat přesně jako cel<PERSON>, proto<PERSON><PERSON> jsou p<PERSON><PERSON><PERSON> velk<PERSON>.", "Numeric_separators_are_not_allowed_here_6188": "<PERSON><PERSON><PERSON><PERSON><PERSON>e tady nejsou povolené.", "Object_is_of_type_unknown_2571": "Objekt je typu Neznámý.", "Object_is_possibly_null_2531": "Objekt je pravděpodobně null.", "Object_is_possibly_null_or_undefined_2533": "Objekt je pravděpodobně null nebo undefined.", "Object_is_possibly_undefined_2532": "Objekt je pravděpodobně undefined.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Literál objektu může specifikovat jenom známé vlastnosti a {0} v typu {1} neexistuje.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Literál objektu může urč<PERSON>t jenom z<PERSON> vlastnosti, ale {0} v typu {1} neexistuje. Chtěli jste zapsat {2}?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "Vlastnost {0} literálu objektu má implicitně typ {1}.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> o<PERSON> sdr<PERSON>lastnos<PERSON>, nelze odvodit pomocí možnosti „--isolatedDeclarations“.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> obsahují přiřazení rozprostření, se nedají odvodit pomocí možnosti „--isolatedDeclarations“.", "Octal_digit_expected_1178": "Očekává se osmičková číslice.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "Osmičkové řídicí sekvence a zpětné odkazy nejsou ve třídě znaků povoleny. Pokud to bylo zam<PERSON>šleno jako <PERSON> sekven<PERSON>, použi<PERSON><PERSON> místo toho syntaxi „{0}“.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "Osmič<PERSON>é řídicí sekvence nejsou povoleny. Použijte syntaxi „{0}“.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "Osmičkové literály nejsou povoleny. Použijte syntaxi „{0}“.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "Jedna hodnota z „{0}.{1}“ je <PERSON><PERSON><PERSON><PERSON> „{2}“ a druhá hodnota se považuje za neznámou číselnou hodnotu.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "V příkazu for...in se povoluje deklarovat jenom jednu proměnnou.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "V příkazu for...of se povoluje deklarovat jenom jednu proměnnou.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Klíčovým slovem new se dá volat jenom funkce void.", "Only_ambient_modules_can_use_quoted_names_1035": "Názvy v uvozovkách můžou mít jenom ambientní moduly.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Spolu s --{0} se podporují jenom moduly amd a system.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "Po<PERSON><PERSON><PERSON> m<PERSON> „--isolatedDeclarations“ lze odvodit pouze pole const.", "Only_emit_d_ts_declaration_files_6014": "Bude vydávat jen soubory de<PERSON>í .d.ts.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Zahrňte do výstupu jenom soubory d.ts, nikoli soubory JavaScriptu.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Prostřednictvím klíčového slova super jsou přístupné jenom veřejné a chráněné metody základní tří<PERSON>.", "Operator_0_cannot_be_applied_to_type_1_2736": "Operátor {0} se nedá p<PERSON>žít na typ {1}.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Operátor {0} nejde p<PERSON>žít u typů {1} a {2}.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "V rámci třídy znaků nelze kombinovat operátory. Místo toho je zabalte do vnořené třídy.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Při úpravách vyloučit projekt z kontroly odkazů ve více projektech.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "Možnost „{0}={1}“ byla odebrána. Odeberte ji prosím z konfigurace.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "Možnost „{0}={1}“ je zastaralá a v jazyce TypeScript {2} přestane fungovat. Tuto chybu můžete potlačit zadáním compilerOption '\"ignoreDeprecations\": \"{3}\"'.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "Možnost {0} jde zadat jenom v souboru tsconfig.json nebo nastavit na příkazovém řádku na hodnotu false nebo null.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "Možnost {0} jde zadat jenom v souboru tsconfig.json nebo nastavit na příkazovém řádku na hodnotu null.", "Option_0_can_only_be_specified_on_command_line_6266": "Možnost „{0}“ lze zadat pouze na příkazovém řádku.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "Možnost {0} jde použít jenom při zadání možnosti --inlineSourceMap nebo možnosti --sourceMap.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "Možnost „{0}“ se dá použít jenom v případě, že je možnost „moduleResolution“ nastavená na „node16“, „nodenext“ nebo „bundler“.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "Možnost „{0}“ se dá použít jenom v případě, že je možnost „modul“ nastavená na „preserve“ nebo na „es2015“ a novější.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "K<PERSON>ž je možnost jsx nastavená na {1}, možnost {0} se nedá zadat.", "Option_0_cannot_be_specified_with_option_1_5053": "Možnosti {0} a {1} nejde zadat zároveň.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Možnost {0} nejde zadat bez možnosti {1}.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Možnost {0} nejde zadat bez možnosti {1} nebo {2}.", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "Možnost „{0}“ byla odebrána. Odeberte ji prosím z konfigurace.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "Možnost „{0}“ je zastaralá a v jazyce TypeScript {1} přestane fungovat. Tuto chybu můžete potlačit zadáním compilerOption '\"ignoreDeprecations\": \"{2}\"'.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "Možnost „{0}“ je redundantní a nelze ji zadat s možností „{1}“.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "Možnost „allowImportingTsExtensions“ se dá použít jenom v případě, že je nastavená možnost „noEmit“ nebo „emitDeclarationOnly“.", "Option_build_must_be_the_first_command_line_argument_6369": "Možnost --build musí být prvním argumentem příkazového řádku.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "Možnost ‚--incremental‘ se dá zadat jen pomocí tsconfig, při generování do jednoho souboru nebo když se zadá možnost ‚--tsBuildInfoFile‘.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "Možnost isolatedModules jde použít jenom v případě, že je poskytnutá možnost --module nebo že možnost target je ES2015 nebo vyšší verze.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "<PERSON><PERSON>ž je možnost „module“ nastavená na „{0}“, možnost „moduleResolution“ musí být nastavená na „{1}“ (nebo musí zůstat nezadaná).", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "K<PERSON>ž je možnost „moduleResolution“ nastavená na „{1}“, možnost „module“ musí být nastavená na „{0}“.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "K<PERSON>ž je povolená možnost „{0}“, možnost „preserveConstEnums“ se nedá zakázat.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Možnost project se na příkazovém řádku nedá kombinovat se zdrojovým souborem.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "Možnost „--resolveJsonModule“ se nedá zadat, pokud je možnost „moduleResolution“ nastavená na hodnotu „classic“.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "Možnost „--resolveJsonModule“ se ned<PERSON> zadat, pokud je možnost „module“ nastavená na „none“, „system“ nebo „umd“.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "Možnost „verbatimModuleSyntax“ nejde p<PERSON>žít, pokud je možnost „module“ nastavená na „UMD“, „AMD“ nebo „System“.", "Options_0_and_1_cannot_be_combined_6370": "Možnosti {0} a {1} nej<PERSON> k<PERSON>.", "Options_Colon_6027": "Možnosti:", "Output_Formatting_6256": "Formátování výstupu", "Output_compiler_performance_information_after_building_6615": "Po sestavení generovat informace o výkonu kompilátoru.", "Output_directory_for_generated_declaration_files_6166": "Výstupní ad<PERSON>ř pro vygenerované soubory <PERSON>", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Výstupní soubor {0} se nesestavil ze zdrojového souboru {1}.", "Output_from_referenced_project_0_included_because_1_specified_1411": "Výstup z odkazovaného projektu {0}, k<PERSON><PERSON> se zahrnul, proto<PERSON>e je zadané {1}", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "Výstup z odkazovaného projektu {0}, k<PERSON><PERSON> se zahrnul, proto<PERSON>e možnost --module se nastavila na none", "Output_more_detailed_compiler_performance_information_after_building_6632": "Do výstupu po sestavení zahrňte podrobnější informace o výkonu kompilátoru.", "Overload_0_of_1_2_gave_the_following_error_2772": "Přetížení {0} z {1}, {2}, v<PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>í chy<PERSON>.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Signatury přetížení musí být všechny abstraktní nebo neabstraktní.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Signatury přetížení musí být všechny ambientní nebo neambientní.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Signatury přetížení musí být všechny exportované nebo neexportované.", "Overload_signatures_must_all_be_optional_or_required_2386": "Signatury přetížení musí být všechny nepovinné nebo povinné.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Signatury přetížení musí být všechny veřejné, privátní nebo chráně<PERSON>é.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Parametr {0} nemůže odkazovat na identifikátor {1} deklarovaný za ním.", "Parameter_0_cannot_reference_itself_2372": "Parametr {0} nemůže odkazovat sám na sebe.", "Parameter_0_implicitly_has_an_1_type_7006": "Parametr {0} má implicitně typ {1}.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Parametr {0} má implicitně typ {1}, ale je možn<PERSON>, že lep<PERSON> typ by se vyvodil z použití.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Parametr {0} není na stejné pozici jako parametr {1}.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Parametr {0} přístupového objektu má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Parametr {0} přístupového objektu má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Parametr {0} přístupového objektu má nebo používá privátní název {1}.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Parametr {0} signatury volání z exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Parametr {0} signatury volání z exportovaného rozhraní má nebo používá privátní název {1}.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Parametr {0} konstruktoru z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Parametr {0} konstruktoru z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Parametr {0} konstruktoru z exportované třídy má nebo používá privátní název {1}.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Parametr {0} signatury konstruktoru z exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Parametr {0} signatury konstruktoru z exportovaného rozhraní má nebo používá privátní název {1}.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Parametr {0} exportovan<PERSON> má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Parametr {0} exportovan<PERSON> má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Parametr {0} exportovan<PERSON> má nebo používá privátní název {1}.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Parametr {0} signatury indexu z exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Parametr {0} signatury indexu z exportovaného rozhraní má nebo používá privátní název {1}.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Parametr {0} metody z exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Parametr {0} metody z <PERSON>ovaného rozhraní má nebo používá privátní název {1}.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Parametr {0} veřejné metody z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Parametr {0} veřejné metody z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Parametr {0} veřejné metody z exportované třídy má nebo používá privátní název {1}.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Parametr {0} ve<PERSON><PERSON>n<PERSON> statické metody z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Parametr {0} ve<PERSON>ejné statické metody z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Parametr {0} veř<PERSON>né static<PERSON>é metody z exportované třídy má nebo používá privátní název {1}.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Parametr nemůže obsahovat otazník a inicializátor.", "Parameter_declaration_expected_1138": "Očekává se deklarace parametru.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Parametr m<PERSON>, ale žádný typ. <PERSON><PERSON><PERSON> jste na mysli {0}: {1}?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Modifikátory parametrů se dají používat jen v typescriptových souborech.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "Parametr musí mít explicitní anotaci typu s možností „--isolatedDeclarations“.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Typ parametru veřejné metody setter {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Typ parametru veřejné metody setter {0} z exportované třídy má nebo používá privátní název {1}.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Typ parametru veřejné statické metody setter {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Typ parametru veřejné statické metody setter {0} z exportované třídy má nebo používá privátní název {1}.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Parsovat ve striktním režimu a generovat striktní používání pro každý zdrojový soubor", "Part_of_files_list_in_tsconfig_json_1409": "Součást seznamu files v souboru tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "Vzor {0} m<PERSON>že obsahovat nanej<PERSON><PERSON>š jeden znak * (hvězdička).", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Časování výkonu pro --diagnostics nebo --extendedDiagnostics nejsou v této relaci k dispozici. Nepovedlo se najít nativní implementace rozhraní Web Performance API.", "Platform_specific_6912": "Specifická pro platformu", "Prefix_0_with_an_underscore_90025": "Předpona {0} s podtrž<PERSON>tkem", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Před všechny nesprávné deklarace vlastností přidejte declare.", "Prefix_all_unused_declarations_with_where_possible_95025": "Přidat příponu _ ke všem nepoužívaným deklaracím tam, kde je to možné", "Prefix_with_declare_95094": "<PERSON><PERSON><PERSON><PERSON> declare", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Zachovejte nepoužívané importované hodnoty ve výstupu JavaScriptu, k<PERSON><PERSON> by se jinak odebral.", "Print_all_of_the_files_read_during_the_compilation_6653": "Vytiskněte si všechny soubory přečtené při kompilaci.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Vytiskněte si soubory přečtené při kompilaci, včetně dů<PERSON><PERSON> jejich zah<PERSON>.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Umožňuje vypsat názvy souborů a důvod, proč jsou součástí kompilace.", "Print_names_of_files_part_of_the_compilation_6155": "<PERSON><PERSON><PERSON>, při které se vypisují názvy souborů", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Vypsat názvy so<PERSON>, kter<PERSON> jsou souč<PERSON><PERSON>í kompilace, a pak ukončit zpracovávání", "Print_names_of_generated_files_part_of_the_compilation_6154": "<PERSON><PERSON><PERSON>, při které se vypisují názvy generovaných so<PERSON>", "Print_the_compiler_s_version_6019": "Vytisknout verzi kompilátoru", "Print_the_final_configuration_instead_of_building_1350": "Místo sestavení vypsat konečnou konfiguraci", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Po kompilaci vytiskněte názvy generovaný<PERSON>.", "Print_this_message_6017": "Vytisknout tuto zprávu", "Private_accessor_was_defined_without_a_getter_2806": "Privátní přístupový objekt se definoval bez metody getter.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "Privátní pole „{0}“ musí být deklarované v nadřazené třídě.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Privátní identifikátory se v deklaracích proměnných nepovolují.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Privátní identifikátory se mimo těla tříd <PERSON>.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Privátní identifikátory jsou povolené jenom v tělech třídy a smí se používat jenom jako součást deklarace člena třídy nebo přístupu k vlastnosti, případně na levé straně výrazu in.", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Privátní identifikátory jsou dostupné jen při cílení na ECMAScript 2015 a novější.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Privátní identifikátory se nedají použít jako parametry.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "K privátnímu nebo chráněnému <PERSON>u {0} se nedá přistupovat v parametru typu.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "Projekt {0} se nuceně vytváří <PERSON>.", "Project_0_is_out_of_date_because_1_6420": "Projekt „{0}“ je <PERSON>, proto<PERSON>e {1}.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "Projekt „{0}“ je <PERSON>, proto<PERSON>e soubor buildinfo „{1}“ oz<PERSON><PERSON><PERSON><PERSON>, že soubor „{2}“ byl kořenovým souborem kompilace, ale už není.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "Projekt {0} j<PERSON> <PERSON><PERSON><PERSON><PERSON>, proto<PERSON><PERSON> soubor buildinfo „{1}“ oz<PERSON><PERSON><PERSON><PERSON>, že program musí hlásit chyby.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "{0} projekt<PERSON> je <PERSON>, proto<PERSON>e soubor buildinfo {1} in<PERSON><PERSON><PERSON>, že se některé změny nevygenerovaly.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "Projekt „{0}“ je <PERSON><PERSON><PERSON>, proto<PERSON>e soubor buildinfo „{1}“ oz<PERSON>čuje, že došlo ke změně v možnosti „compilerOptions“.", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Projekt {0} je <PERSON><PERSON><PERSON><PERSON><PERSON>, proto<PERSON>e jeho <PERSON> {1} je zastaral<PERSON>.", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Projekt {0} je <PERSON><PERSON><PERSON>, proto<PERSON><PERSON> v<PERSON> {1} je star<PERSON><PERSON> než vstup {2}.", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Projekt {0} j<PERSON> <PERSON><PERSON>, proto<PERSON><PERSON> vý<PERSON><PERSON><PERSON><PERSON> so<PERSON>or {1} neexistuje.", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Projekt {0} je <PERSON><PERSON><PERSON>, proto<PERSON>e jeho výstup se vygeneroval pomocí verze {1}, kter<PERSON> se liší od aktuální verze {2}.", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Projekt {0} j<PERSON> <PERSON><PERSON><PERSON>, proto<PERSON><PERSON> p<PERSON>i <PERSON> souboru {1} do<PERSON><PERSON> k <PERSON>ě.", "Project_0_is_up_to_date_6361": "Projekt {0} je aktuální.", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Projekt {0} je a<PERSON><PERSON><PERSON><PERSON><PERSON>, proto<PERSON><PERSON> ne<PERSON><PERSON><PERSON> vstup {1} je star<PERSON><PERSON> než výstup {2}.", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Projekt {0} je a<PERSON><PERSON><PERSON><PERSON><PERSON>, ale musí aktualizovat časová razítka výstupn<PERSON><PERSON> so<PERSON>, kter<PERSON> jsou starš<PERSON> než vstupn<PERSON> soubory.", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Projekt {0} je aktualizovaný soubory .d.ts z jeho závislostí.", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Odkazy projektu nemůžou tvořit cyklický graf. Zjistil se cyklus: {0}", "Projects_6255": "Projekty", "Projects_in_this_build_Colon_0_6355": "Projekty v tomto sestavení: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Vlastnosti s modifikátorem accessor jsou k dispozici jen při cílení na ECMAScript 2015 a vyšší.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "Vlastnost {0} nem<PERSON>že mít inicializ<PERSON>, proto<PERSON>e je označená jako abstraktní.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "Vlastnost {0} pochází ze signatury indexu, proto je zapotřebí k ní přistupovat pomocí ['{0}'].", "Property_0_does_not_exist_on_type_1_2339": "Vlastnost {0} v typu {1} neexistuje.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "Vlastnost {0} v typu {1} neexistuje. <PERSON><PERSON><PERSON> jste na mysli {2}?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "Vlastnost {0} v typu {1} neexistuje. Chtěli jste místo toho přistoupit ke statickému členu {2}?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "Vlastnost ‚{0}‘ neexistuje u typu ‚{1}‘. Potřebujete změnit cílovou knihovnu? Zkuste změnit možnost kompilátoru ‚lib‘ na ‚{2}‘ nebo novější.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "Vlastnost „{0}“ pro typ „{1}“ neexistuje. Zkuste změnit možnost kompilátoru „lib“, aby zah<PERSON><PERSON> „dom“.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "Vlastnost „{0}“ nemá žádný inicializátor a není jednoznačně přiřazena ve statickém bloku třídy.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "Vlastnost {0} nemá žádný inicializátor a není jednoznačně přiřazena v konstruktoru.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "Vlastnost {0} má implicitně typ any, proto<PERSON><PERSON> jej<PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> objekt get nemá anotaci návratového typu.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "Vlastnost {0} má implicitně typ any, proto<PERSON><PERSON> jej<PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> objekt set nemá anotaci parametrového typu.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "Vlastnost {0} má implicitně typ any, ale je možn<PERSON>, že lep<PERSON> typ pro jeho přístupový objekt get by se vyvodil z použití.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "Vlastnost {0} má implicitně typ any, ale je možn<PERSON>, že lep<PERSON> typ pro jeho přístupový objekt set by se vyvodil z použití.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "Vlastnost {0} v typu {1} nejde přiřadit ke stejné vlastnosti v základním typu {2}.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "Vlastnost {0} v typu {1} nejde přiřadit typu {2}.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "Vlastnost {0} v typu {1} odkazuje na jiného člena, ke kterému není možné získat přístup z typu {2}.", "Property_0_is_declared_but_its_value_is_never_read_6138": "Deklaruje se vlastnost {0}, ale její hodnota se v<PERSON>bec neč<PERSON>.", "Property_0_is_incompatible_with_index_signature_2530": "Vlastnost {0} není kompatibilní se signaturou indexu.", "Property_0_is_missing_in_type_1_2324": "Vlastnost {0} v typu {1} chybí.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "Vlastnost {0} chybí v typu {1}, ale vyžaduje se v typu {2}.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "Vlastnost {0} není přístupná mimo třídu {1}, protože má privátní identifikátor.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "Vlastnost {0} je v typu {1} <PERSON><PERSON><PERSON><PERSON>, ale v<PERSON>žaduje se v typu {2}.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "Vlastnost {0} je privátní a dostupná jenom ve třídě {1}.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "Vlastnost {0} je v typu {1} priv<PERSON>tní, ale v typu {2} ne.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "Vlastnost {0} je chráněná a dá se k ní přistupovat jen přes instanci třídy {1}. Toto je instance třídy {2}.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "Vlastnost {0} je chráněná a je dostupná jenom ve třídě {1} a jej<PERSON><PERSON> pod<PERSON>ř<PERSON>.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "Vlastnost {0} je <PERSON><PERSON>, ale typ {1} nen<PERSON> tří<PERSON> odvozená od {2}.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "Vlastnost {0} je v typu {1} chráněná, ale v typu {2} veřejná.", "Property_0_is_used_before_being_assigned_2565": "Vlastnost {0} je pou<PERSON><PERSON><PERSON> před přiřazením.", "Property_0_is_used_before_its_initialization_2729": "Vlastnost {0} se používá dříve, než se inicializuje.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "<PERSON><PERSON><PERSON> <PERSON>, že vlastnost {0} v typu {1} neexistuje. M<PERSON>li jste na mysli {2}?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "Vlastnost {0} rozšířeného atributu JSX nejde přiřadit cílové vlastnosti.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "Vlastnost „{0}“ exportovaného anonymního typu třídy nesm<PERSON> být privátní ani ch<PERSON>.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "Vlastnost {0} exportovaného rozhraní má nebo používá název {1} z privátního modulu {2}.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "Vlastnost {0} exportovaného rozhraní má nebo používá privátní název {1}.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "Vlastnost {0} typu {1} se nedá přiřadit k {2} typu indexu {3}.", "Property_0_was_also_declared_here_2733": "Vlastnost {0} se deklarovala i tady.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "Vlastnost {0} přepíše základní vlastnost v {1}. <PERSON>ku<PERSON> je to <PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON>ide<PERSON>te inicializátor. <PERSON>ak přidejte modifikátor declare nebo odeberte <PERSON><PERSON><PERSON>.", "Property_assignment_expected_1136": "Očekává se přiřazení vlastnosti.", "Property_destructuring_pattern_expected_1180": "Očekává se vzor destruktoru vlastnosti.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "Vlastnost musí mít explicitní anotaci typu s možností „--isolatedDeclarations“.", "Property_or_signature_expected_1131": "Očekává se vlastnost nebo podpis.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Hodnota vlastnosti může být jenom řetězcový literál, číselný literál, true, false, null, literál objektu nebo literál pole.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "Při cílení na „ES5“ poskytněte plnou podporu iterovatelných proměnných v příkazu „for-of“, rozšíření a destrukturování.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Veřejná metoda {0} z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Veřejná metoda {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Veřejná metoda {0} z exportované třídy má nebo používá privátní název {1}.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "Veřejná vlastnost {0} exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "Veřejná vlastnost {0} exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "Veřejná vlastnost {0} exportované třídy má nebo používá privátní název {1}.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Veřejná statická metoda {0} z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Veřejná statická metoda {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Veřejná statická metoda {0} z exportované třídy má nebo používá privátní název {1}.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Veřejná statická vlastnost {0} exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Veřejná statická vlastnost {0} exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Veřejná statická vlastnost {0} exportované třídy má nebo používá privátní název {1}.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Kvalifikovaný název {0} se nepovoluje bez @param {object} {1} na začátku.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "<PERSON><PERSON>ž se parametr funkce nepřečte, nahlaste chybu.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Vyvolat chybu u výrazů a deklarací s implikovaným typem any", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Vyvolá chybu u výrazů this s implikovaným typem any.", "Range_out_of_order_in_character_class_1517": "Rozsa<PERSON> ve třídě znaků je mimo pořadí.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "Při opětovném exportu typu s povolenou možností „{0}“ je nutné použít možnost „export type“.", "React_components_cannot_include_JSX_namespace_names_2639": "Komponenty React nemůžou obsahovat názvy oborů názvů JSX.", "Redirect_output_structure_to_the_directory_6006": "Přesměrování výstupní struktury do adresáře", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Snižte počet projektů, které TypeScript načítá automaticky.", "Referenced_project_0_may_not_disable_emit_6310": "Odkazovaný projekt {0} nemůže zakazovat generování.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Odkazovaný projekt {0} musí mít nastavení \"composite\": true.", "Referenced_via_0_from_file_1_1400": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> {0} ze souboru {1}", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "Relativní cesty importu vyžadují explicitní přípony souborů v importech ECMAScriptu, k<PERSON>ž „--moduleResolution“ je „node16“ nebo „nodenext“. Zvažte přidání přípony do cesty importu.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "Relativní cesty importu vyžadují explicitní přípony souborů v importech ECMAScriptu, když „--moduleResolution“ je „node16“ nebo „nodenext“. Měli jste na mysli „{0}“?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Odeberte z procesu sledování seznam adresářů.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Ze zpracování režimu sledování odeberte seznam souborů.", "Remove_all_unnecessary_override_modifiers_95163": "Odebrat všechny nepotřebné modifikátory override", "Remove_all_unnecessary_uses_of_await_95087": "Odebrat všechna nepotřebná použití výrazu await", "Remove_all_unreachable_code_95051": "Odebrat veškerý nedosažitelný kód", "Remove_all_unused_labels_95054": "Odebrat všechny nepoužívané popisky", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Odeberte složené závorky ze všech těl funkcí <PERSON>, u kterých dochází k problémům.", "Remove_braces_from_arrow_function_95060": "Odebrat složené závorky z funkce šipky", "Remove_braces_from_arrow_function_body_95112": "Odebrat složené závorky z těla funkce šipky", "Remove_import_from_0_90005": "Odebrat import z {0}", "Remove_override_modifier_95161": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON> override", "Remove_parentheses_95126": "<PERSON><PERSON><PERSON><PERSON>", "Remove_template_tag_90011": "Odebrat značku šablonu", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Odeberte limit 20 MB pro celkovou velikost zdrojového kódu souborů JavaScriptu na jazykovém serveru TypeScriptu.", "Remove_type_from_import_declaration_from_0_90055": "Odebrat „type“ z deklarace importu z „{0}“", "Remove_type_from_import_of_0_from_1_90056": "<PERSON><PERSON><PERSON><PERSON> „type“ z importu {0} z „{1}“", "Remove_type_parameters_90012": "Odebrat parametry typů", "Remove_unnecessary_await_95086": "Odebrat nepotřebné výrazy await", "Remove_unreachable_code_95050": "Odebrat nedosažitelný kód", "Remove_unused_declaration_for_Colon_0_90004": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro {0}", "Remove_unused_declarations_for_Colon_0_90041": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro {0}", "Remove_unused_destructuring_declaration_90039": "<PERSON>de<PERSON><PERSON>ží<PERSON>é des<PERSON>ční <PERSON>", "Remove_unused_label_95053": "Odebrat ne<PERSON> popisek", "Remove_variable_statement_90010": "Odebrat p<PERSON>az proměnné", "Rename_param_tag_name_0_to_1_95173": "Přejmenovat značku @param {0} na {1}", "Replace_0_with_Promise_1_90036": "Místo {0} použijte Promise<{1}>", "Replace_all_unused_infer_with_unknown_90031": "Nahradit všechny nepoužívané příkazy infer za unknown", "Replace_import_with_0_95015": "Nahradí import použitím: {0}.", "Replace_infer_0_with_unknown_90030": "Nahradit infer {0} za unknown", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Oznámí se chyba, <PERSON><PERSON><PERSON> n<PERSON>er<PERSON> cesty kódu ve <PERSON>ci nevracejí hodnotu.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Oznámí se chyby v případech fallthrough v příkazu switch.", "Report_errors_in_js_files_8019": "Ohlásit chyby v souborech .js", "Report_errors_on_unused_locals_6134": "Umožňuje nahlásit chyby u nevyužitých místních hodnot.", "Report_errors_on_unused_parameters_6135": "Umožňuje nahlásit chyby u nevyužitých parametrů.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "Vyžadujte u exportů dostateč<PERSON>u anotaci, aby ostatní nástroje mohly triviálně generovat soubory deklarací.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby nedeklarované vlastnosti ze signatur indexů používaly přístupy k elementům", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Požadované parametry typu nemůžou být až za volitelnými parametry typu.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "Překlad pro modul {0} se našel v mezipaměti umístění {1}.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "Překlad pro direktivu odkazu na typ {0} se našel v mezipaměti umístění {1}.", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "Překlad nerelativního n<PERSON> se<PERSON>; z<PERSON><PERSON><PERSON><PERSON>me to se zakázanými moderními funkcemi překladu Node, a<PERSON><PERSON><PERSON>, jestli není potřeba aktualizovat konfiguraci knihovny npm.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "Překlad nerelativního názvu selhal. <PERSON><PERSON><PERSON><PERSON><PERSON>me to s možností „--moduleResolution bundler“, aby<PERSON><PERSON>, jestli projekt nepotřebuje aktualizaci konfigurace.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "keyof překládejte jen na názvy vlastností s hodnotami typu string (ne čísla ani symboly).", "Resolved_under_condition_0_6414": "Vyřešeno za podmínky „{0}“.", "Resolving_in_0_mode_with_conditions_1_6402": "Řešení v <PERSON><PERSON><PERSON><PERSON> {0} s podmínkami {1}.", "Resolving_module_0_from_1_6086": "======== Překládá se modul {0} z {1}. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Překládá se název modulu {0} relativní k základní adrese URL {1}–{2}.", "Resolving_real_path_for_0_result_1_6130": "Překládá se skutečná cesta pro {0}, v<PERSON><PERSON><PERSON> {1}.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Překládá se direktiva odkazu na typ {0} obsahující soubor {1}. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Překládá se direktiva reference typu {0}, obsa<PERSON><PERSON><PERSON> soubor {1}, ko<PERSON><PERSON><PERSON><PERSON> adresář {2}. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Překládá se direktiva reference typu {0}, obsa<PERSON><PERSON><PERSON> soubor {1}, ko<PERSON><PERSON><PERSON><PERSON> ad<PERSON> nen<PERSON>. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Překládá se direktiva reference typu {0}, obsažený soubor nen<PERSON>, ko<PERSON><PERSON><PERSON><PERSON> ad<PERSON> {1}. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Překládá se direktiva reference typu {0}, obsažený soubor není nasta<PERSON>, ko<PERSON><PERSON><PERSON><PERSON> adres<PERSON> není nastaven<PERSON>. ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "Překlad direktivy odkazu na typ pro program, který zadává vlastní hodnoty typeRoot, s přeskočením vyhledávání ve složce „node_modules“.", "Resolving_with_primary_search_path_0_6121": "Probíhá překlad pomocí primární cesty hledání {0}.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Parametr rest {0} implicitn<PERSON> obsahuje typ any[].", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Parametr rest {0} má implicitně typ any[], ale je možn<PERSON>, že lep<PERSON> typ by se vyvodil z použití.", "Rest_types_may_only_be_created_from_object_types_2700": "Typy rest se dají vytvářet jenom z typů object.", "Return_type_annotation_circularly_references_itself_2577": "Anotace návratového typu se cyklicky odkazuje sama na sebe.", "Return_type_must_be_inferred_from_a_function_95149": "Návratový typ musí být odvozen z funkce.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Návratový typ signatury volání z exportovaného rozhraní má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Návratový typ signatury volání z exportovaného rozhraní má nebo používá privátní název {0}.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Návratový typ signatury konstruktoru z exportovaného rozhraní má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Návratový typ signatury konstruktoru z exportovaného rozhraní má nebo používá privátní název {0}.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Návratový typ signatury konstruktoru musí jít přiřadit k typu instance třídy.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Návratový typ exportované funkce má nebo používá název {0} z externího modulu {1}, ale nedá se pojmenovat.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Návratový typ exportované <PERSON>ce má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Návratový typ exportované funkce má nebo používá privátní název {0}.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Návratový typ signatury indexu z exportovaného rozhraní má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Návratový typ signatury indexu z exportovaného rozhraní má nebo používá privátní název {0}.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Návratový typ metody z exportovaného rozhraní má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Návratový typ metody z <PERSON>ovaného rozhraní má nebo používá privátní název {0}.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Návratový typ veřejné metody getter {0} z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Návratový typ veřejné metody getter {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Návratový typ veřejné metody getter {0} z exportované třídy má nebo používá privátní název {1}.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Návratový typ veřejné metody z exportované třídy má nebo používá název {0} z externího modulu {1}, ale nedá se pojmenovat.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Návratový typ veřejné metody z exportované třídy má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Návratový typ veřejné metody z exportované třídy má nebo používá privátní název {0}.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Návratový typ veřejn<PERSON> statické metody getter {0} z exportované třídy má nebo používá název {1} z externího modulu {2}, ale nedá se pojmenovat.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Návratový typ veřejn<PERSON> statické metody getter {0} z exportované třídy má nebo používá název {1} z privátního modulu {2}.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Návratový typ veřejn<PERSON> statické metody getter {0} z exportované třídy má nebo používá privátní název {1}.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Návratový typ veřejné statické metody z exportované třídy má nebo používá název {0} z externího modulu {1}, ale nedá se pojmenovat.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Návratový typ veřejné statické metody z exportované třídy má nebo používá název {0} z privátního modulu {1}.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Návratový typ veřejné static<PERSON>é metody z exportované třídy má nebo používá privátní název {0}.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Opětovné použití překladu modulu {0} z {1} nalezeného v mezipaměti z umístění {2} se nevyřešilo.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Opětovné použití překladu modulu {0} z {1} nalezeného v mezipaměti z umístění {2} bylo úspěšně vyřešeno na {3}.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Opětovné použití překladu modulu {0} z {1} nalezeného v mezipaměti z umístění {2} bylo úspěšně vyřešeno na {3} s ID balíčku {4}.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Opětovné použití překladu modulu {0} z {1} starého programu se nevyřešilo.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Opětovné použití překladu modulu {0} z {1} starého programu bylo úsp<PERSON>š<PERSON>ě vyřešeno na {2}.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Opětovné použití překladu modulu {0} z {1} starého programu bylo ú<PERSON>š<PERSON>ě vyřešeno na {2} s ID balíčku {3}.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Opětovné použití překladu direktivy typu reference {0} z {1} nalezeného v mezipaměti z umístění {2} se nevyřešilo.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Opětovné použití překladu direktivy typu reference {0} z {1} nalezeného v mezipaměti z umístění {2} bylo úspěšně vyřešeno na {3}.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Opětovné použití překladu direktivy typu reference {0} z {1} nalezeného v mezipaměti z umístění {2} bylo úspěšně vyřešeno na {3} s ID balíčku {4}.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Opětovné použití překladu direktivy typu reference {0} z {1} starého programu se nevyřešilo.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Opětovné použití překladu direktivy typu reference {0} z {1} starého programu bylo <PERSON> vyřešeno na {2}.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Opětovné použití překladu direktivy typu reference {0} z {1} starého programu bylo <PERSON> vyřešeno na {2} s ID balíčku {3}.", "Rewrite_all_as_indexed_access_types_95034": "Přepsat vše jako indexované typy přístupu", "Rewrite_as_the_indexed_access_type_0_90026": "Přepsat jako index<PERSON>ný typ přístupu {0}", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "Přepište přípony souborů .ts, .tsx, .mts a .cts v relativních cestách importu na jejich javascriptový ekvivalent ve výstupních souborech.", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "Pravý operand ?? je ne<PERSON><PERSON><PERSON>, protože levý operand nemá nikdy hodnotu null.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Nedá se určit koř<PERSON><PERSON>ý <PERSON>, přeskakují se primární cesty hledání.", "Root_file_specified_for_compilation_1427": "<PERSON><PERSON><PERSON><PERSON><PERSON>, který se zadal pro kompilaci", "STRATEGY_6039": "STRATEGIE", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Uložte soubory .tsbuildinfo, aby byla možná přírůst<PERSON>á kompilace projektů.", "Saw_non_matching_condition_0_6405": "<PERSON><PERSON> neshodná podmínka {0}.", "Scoped_package_detected_looking_in_0_6182": "Zjištěn balíček v oboru, hledání v: {0}", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "Vyhledávají se záložní rozšíření ve všech nadřazených adresářích „node_modules“: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "Vyhledávají se upřednostňovaná rozšíření ve všech nadřazených adresářích „node_modules“: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "Výběr nepředstavuje platný příkaz (platné příkazy).", "Selection_is_not_a_valid_type_node_95133": "<PERSON><PERSON><PERSON><PERSON><PERSON> není plat<PERSON>ým uzlem typů.", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Nastavte verzi jazyka JavaScript pro generovaný JavaScript a zahrňte deklarace kompatibilních knihoven.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Nastavte jazyk posílání zpráv z TypeScriptu. Toto nastavení neovlivní generování.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Nastavte možnost module v konfiguračním souboru na {0}.", "Set_the_newline_character_for_emitting_files_6659": "Nastavte pro generované soubory znak nového řádku.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Nastavte možnost target v konfiguračním souboru na {0}.", "Setters_cannot_return_a_value_2408": "<PERSON><PERSON> setter nem<PERSON><PERSON><PERSON> vracet hodnotu.", "Show_all_compiler_options_6169": "Zobrazí všechny možnosti kompilátoru.", "Show_diagnostic_information_6149": "Zobrazí diagnostické informace.", "Show_verbose_diagnostic_information_6150": "Zobrazí podrobné diagnostické informace.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "<PERSON><PERSON><PERSON><PERSON>, co by se se<PERSON><PERSON>o (nebo ods<PERSON><PERSON><PERSON>, pokud je zadaná možnost --clean)", "Signature_0_must_be_a_type_predicate_1224": "Signatura {0} mus<PERSON> b<PERSON>t predi<PERSON>t typu.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "Deklarace signatur se dají používat jen v typescriptových souborech.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "Přeskočí vytváření podřízených projektů při chybě v nadřazeném projektu.", "Skip_type_checking_all_d_ts_files_6693": "Přeskočte kontrolu typů ve všech souborech .d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Při kontrole typů vynechte soubory .d.ts zahrnuté do TypeScriptu.", "Skip_type_checking_of_declaration_files_6012": "Přeskočit kontrolu typu souborů <PERSON>", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "Přeskakuje se modul „{0}“, který vypadá jako absolutní identifikátor URI. Cílové typy souborů: {1}.", "Source_from_referenced_project_0_included_because_1_specified_1414": "Zdr<PERSON>j z odkazovaného projektu {0}, k<PERSON><PERSON> se zahrnul, proto<PERSON>e je zadané {1}.", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "<PERSON>dr<PERSON>j z odkazovaného projektu {0}, k<PERSON><PERSON> se zahrnul, proto<PERSON>e možnost --module se nastavila na none.", "Source_has_0_element_s_but_target_allows_only_1_2619": "<PERSON><PERSON><PERSON><PERSON> má následují<PERSON><PERSON> počet element<PERSON>, ale cíl jich povoluje jen {1}: {0}", "Source_has_0_element_s_but_target_requires_1_2618": "<PERSON><PERSON><PERSON><PERSON> má následují<PERSON>í počet element<PERSON>, ale cíl jich vyžaduje {1}: {0}", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "Zdroj nenabízí v cíli pro element required na pozici {0} ž<PERSON>dn<PERSON> shodu.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "Zdroj nenabízí v cíli pro element variadic na pozici {0} žádnou shodu.", "Specify_ECMAScript_target_version_6015": "Zadejte cílovou verzi ECMAScriptu.", "Specify_JSX_code_generation_6080": "Zadejte generování kódu JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "<PERSON><PERSON><PERSON><PERSON> soubor, který sloučí všechny výstupy do jediného souboru JavaScriptu. Pokud má „declaration“ pravdivou hodnotu,, ur<PERSON><PERSON> soubor, který sloučí všechny výstupní soubory .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Zadejte seznam v<PERSON>ů glo<PERSON>, k<PERSON><PERSON> odpovídají souborům zahrnutým do kompilace.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Zadejte seznam zahrnutých pluginů jazykových služeb.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Zadejte sadu soub<PERSON><PERSON> spo<PERSON><PERSON><PERSON>čních knihoven, které popisují cílové b<PERSON><PERSON>é prostředí.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Zadejte sadu <PERSON>, kter<PERSON> se při importu znovu namapují na další nalezená místa.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Zadejte pole objektů, k<PERSON><PERSON> ur<PERSON>jí cesty pro projekty. Používá se v odkazech na projekt.", "Specify_an_output_folder_for_all_emitted_files_6678": "Zadejte výstupní složku pro všechny generované soubory.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Zadejte chování generování nebo kontroly pro importy, které se používají jen pro typy.", "Specify_file_to_store_incremental_compilation_information_6380": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>, do kterého se uloží informace o přírůstkové kompilaci.", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Zadejte, jak TypeScript v daném specifikátoru modulu najde soubor.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "<PERSON><PERSON><PERSON><PERSON>, jak sledovat adresáře v systémech, které nemají funkci rekurzivního sledování souborů.", "Specify_how_the_TypeScript_watch_mode_works_6715": "<PERSON><PERSON><PERSON><PERSON>, jak má fungovat re<PERSON><PERSON>n<PERSON>.", "Specify_library_files_to_be_included_in_the_compilation_6079": "<PERSON>adej<PERSON> so<PERSON> kn<PERSON>, k<PERSON><PERSON> se mají zahrnout do kompilace.", "Specify_module_code_generation_6016": "Určete generování kódu modulu.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Zadejte specifikátor modulu, kter<PERSON> se použije k naimportování továrních funkcí JSX při použití „jsx: react-jsx“.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Zadej<PERSON> v<PERSON><PERSON>, k<PERSON><PERSON> budou figurovat jako „node_modules/@types“.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Zadejte jednu nebo více cest nebo jeden či více odkazů na moduly uzlů se základními konfiguračními soubory, ze kterých se dědí nastavení.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Zadejte možnosti automatického získávání deklaračních <PERSON>ů.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Zadejte strategii pro vytvoření sledování načítání, k<PERSON>ž se ho nepovede vytvořit pomocí událostí souborového systému: FixedInterval (výchozí), PriorityInterval, DynamicPriority, FixedChunkSize", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Zadejte strategii pro sledování ad<PERSON>, k<PERSON><PERSON>ují nativně rekurzivní sledování: UseFsEvents (výchozí), FixedPollingInterval, DynamicPriorityPolling, FixedChunkSizePolling", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Zadejte strategii pro sledování souboru: FixedPollingInterval (výchozí), PriorityPollingInterval, DynamicPriorityPolling, FixedChunkSizePolling, UseFsEvents, UseFsEventsOnParentDirectory", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Zadejte odkaz na fragment JSX, který se použije pro fragmenty při cíleném generování React JSX, např. „React.Fragment“ nebo „Fragment“.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Zadejte funkci objektu pro vytváření JSX, která se použije při zaměření na generování JSX react, např. React.createElement nebo h.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Zadejte funkci objektu pro vytváření JSX použitou při cílení na generování React JSX, např. React.createElement nebo h.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Zadejte funkci objektu pro vytváření fragmentů JSX, která se použije při cílení na generování JSX react se zadanou možností kompilátoru jsxFactory, například Fragment.", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Zadejte základ<PERSON><PERSON>, kter<PERSON> se použije k řešení názvů modulů, kter<PERSON> nejsou relativní.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Zdejte sekvenci konce řádku, která se má použít při generování soub<PERSON>ů: CRLF (dos) nebo LF (unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Zadejte umístění, ve kterém by měl ladicí program najít soubory TypeScript namísto umístění zdroje.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Zadejte umístění, ve kterém by měl ladicí program najít soubory mapy namísto generovaných umístění.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Zadejte maximální hloubku složky, která se použije pro kontrolu souborů JavaScriptu z node_modules. Platí pouze pro allowJs.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Zadejte specifik<PERSON><PERSON> mod<PERSON>, k<PERSON><PERSON> se má použít k <PERSON>u tov<PERSON><PERSON><PERSON><PERSON> funkcí ‚jsx‘ a ‚jsxs‘ např. z <PERSON><PERSON> react.", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Zadejte objekt vyvolaný pro createElement. To platí pouze při cílení na generování JSX react.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Zadejte výstupní adresář pro generované deklarační soubory.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Zadejte cestu pro soubor přírůstkové kompilace .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Zadejte kořenový adresář vs<PERSON>p<PERSON><PERSON><PERSON>. Slouží ke kontrole struktury výstupního adresáře pomocí --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Zadejte kořenovou složku se zdrojovými soubory.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Zadejte pro ladicí programy kořenovou cestu, kde najdou referenční zdrojový kód.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Zadejte názvy typů b<PERSON>, kter<PERSON> se z<PERSON>, i když na ně neodkazuje zdrojový soubor.", "Specify_what_JSX_code_is_generated_6646": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> kód JSX se vygeneruje.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "<PERSON><PERSON><PERSON><PERSON>, jak m<PERSON> sledovací proces postupovat, <PERSON><PERSON><PERSON> sys<PERSON><PERSON> dojdou nativní sledovací procesy souborů.", "Specify_what_module_code_is_generated_6657": "<PERSON><PERSON><PERSON><PERSON>, pro jaký modul se kód generuje.", "Split_all_invalid_type_only_imports_1367": "Rozdělit všechny neplatné importy, p<PERSON>i k<PERSON>ch se importují jen typy", "Split_into_two_separate_import_declarations_1366": "Rozdělit na dvě samostatné deklarace importu", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "Operátor rozšíření ve výrazech new je dostupný jenom při cílení na verzi ECMAScript 5 a vyšší.", "Spread_types_may_only_be_created_from_object_types_2698": "Typy spread se dají vytvářet jenom z typů object.", "Starting_compilation_in_watch_mode_6031": "Spouští se kompilace v režimu sledování...", "Statement_expected_1129": "Očekává se příkaz.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Příkazy se nepovolují v ambientních kontextech.", "Static_members_cannot_reference_class_type_parameters_2302": "Statické členy nemůžou odkazovat na parametry typu třídy.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "Statická vlastnost {0} je v konfliktu s předdefinovanou vlastností Function.{0} funkce konstruktoru {1}.", "String_literal_expected_1141": "Očekává se řetězcový literál.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "Názvy importu a exportu řetězcového literálu se nepodporují, pokud je příznak „--module“ nastavený na „es2015“ nebo „es2020“.", "String_literal_with_double_quotes_expected_1327": "<PERSON><PERSON><PERSON><PERSON><PERSON> se řetězcový literál s dvojitými uvozovkami.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Stylizujte chyby a zprávy pomocí barev a kontextu (experimentální).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "V případě znaménka minus musí být uvedeny příznaky dílčích vzorů.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Deklarace následných vlastností musí obsahovat stejný typ. Vlastnost {0} musí být typu {1}, ale tady je typu {2}.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "<PERSON><PERSON><PERSON> n<PERSON>ých proměnných musí obsahovat stejný typ. Proměnná {0} musí být typu {1}, ale tady je typu {2}.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Nahrazení {0} za vzor {1} má nesprávný typ, o<PERSON><PERSON><PERSON><PERSON> se typ string, ob<PERSON><PERSON><PERSON><PERSON> je {2}.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "Nahrazení {0} ve vzoru {1} může obsahovat maximálně jeden znak * (hvězdička).", "Substitutions_for_pattern_0_should_be_an_array_5063": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> {0} by m<PERSON><PERSON><PERSON> pole.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Nahrazení vzoru {0} ne<PERSON><PERSON> b<PERSON><PERSON> pr<PERSON><PERSON><PERSON> pole.", "Successfully_created_a_tsconfig_json_file_6071": "Soubor tsconfig.json se úspěšně vytvořil.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Volání pomocí super se nepovolují mimo konstruktory a ve funkcích vnořených v konstruktorech.", "Suppress_excess_property_checks_for_object_literals_6072": "Potlačit nadměrné kontroly vlastností pro literály objektů", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Potlačit chyby noImplicitAny u objektů indexování bez signatur indexu", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Při indexování objektů bez podpisů indexování potlačte chyby „noImplicitAny“.", "Switch_each_misused_0_to_1_95138": "Přepnout každé chybně použité {0} na {1}", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Synchronně volejte zpětná volání a aktualizujte stav sledování adresářů i u platforem, které nativně nepodporují rekurzivní sledování.", "Syntax_Colon_0_6023": "Syntaxe: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "Značka {0} očekává určitý minimální počet argumentů ({1}), ale objekt pro vytváření JSX {2} jich poskytuje maximálně {3}.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Označené výrazy šablony se v nepovinném řetězu nepovolují.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "<PERSON><PERSON>l povoluje jen určitý počet elementů ({0}), ale zdroj jich může mít více.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "Cíl vyžaduje určitý počet elementů ({0}), ale zdroj jich může mít méně.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "Cílový podpis poskytuje pří<PERSON>š m<PERSON>lo <PERSON>ů. Oček<PERSON><PERSON><PERSON> se {0} nebo více, ale bylo obdr<PERSON> {1}.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Modifikátor {0} se dá používat jen v typescriptových souborech.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Operátor {0} nejde p<PERSON>žít u typu symbol.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "Operátor {0} není u logických typů povolený. Můžete ale použít {1}.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "Vlastnost {0} asynchronního iterátoru musí být metoda.", "The_0_property_of_an_iterator_must_be_a_method_2767": "Vlastnost {0} iterátoru musí být metoda.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "Typ Object se dá přiřadit jen k malému poč<PERSON> da<PERSON> typů. Nechtěli jste místo toho použít typ any?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "Příznaky Unicode (u) a Unicode Sets (v) nelze nastavit současně.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "Funkce s šipkou v ES5 nemůže odkazovat na objekt „arguments“. Zvažte použití standardního výrazu funkce.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "V ES5 se na objekt „arguments“ nedá odkazovat v asynchronní funkci nebo metodě. Zvažte možnost použít standardní funkci nebo metodu.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "Tělo příkazu if nemůže být prázdný příkaz.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "Volání by pro tuto implement<PERSON><PERSON>, ale signatury implementace pro přetížení nejsou externě k dispozici.", "The_character_set_of_the_input_files_6163": "<PERSON><PERSON>kov<PERSON> sada vs<PERSON><PERSON><PERSON><PERSON><PERSON>", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "Obsahující funkce šipky zachytává globální hodnotu pro this.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "Text obsahují<PERSON>í funkce nebo modulu je pro analýzu toku řízení p<PERSON>.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Aktuální soubor je modul CommonJS a na nejvyšší úrovni nemůže používat await.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Aktuální soubor je modul CommonJS, jehož importy vytvoří volání require. Odkazovaný soubor je však modul ECMAScript a nelze ho importovat pomocí příkazu require. Raději zvažte vytvoření dynamického volání import(\"{0}\").", "The_current_host_does_not_support_the_0_option_5001": "Aktuální hostitel nepodporuje možnost {0}.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "<PERSON><PERSON><PERSON> {0}, k<PERSON><PERSON> j<PERSON> prav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON>, je defino<PERSON> tady.", "The_declaration_was_marked_as_deprecated_here_2798": "Deklarace se tady oz<PERSON>č<PERSON> jako zastaralá.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Očekávaný typ pochází z vlastnosti {0}, k<PERSON>á je deklarovaná tady v typu {1}.", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Očekávaný typ pochází z návratového typu této signatury.", "The_expected_type_comes_from_this_index_signature_6501": "Očekávaný typ pochází z této signatury indexu.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "Výraz přiřazení exportu musí být identifikátor nebo kvalifikovaný název v ambientním kontextu.", "The_file_is_in_the_program_because_Colon_1430": "Soubor se nachází v programu, protože:", "The_files_list_in_config_file_0_is_empty_18002": "Seznam files v konfiguračním souboru {0} je prázdný.", "The_first_export_default_is_here_2752": "První výchozí nastavení exportu je tady.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "První parametr metody then příslibu musí být zpětné volání.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Globální typ JSX.{0} by ne<PERSON><PERSON><PERSON> mít více než jednu vlastnost.", "The_implementation_signature_is_declared_here_2750": "Signatura implementace se deklarovala tady.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "Meta-vlastnost import.meta není povolena v souborech, které se sestaví do výstupu CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "Metavlastnost import.meta se povoluje jen v případě, že možnost --module je nastavená na es2020, es2022, esnext, system, node16, node18 nebo nodenext.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Odvozený typ {0} se nedá pojmenovat bez odkazu na {1}. Pravděpodobně to nebude přenosné. Vyžaduje se anotace typu.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Odvozený typ {0} se odkazuje na typ s cyklickou strukturou, která se nedá triviálně serializovat. Musí se použít anotace typu.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Odvozený typ {0} odkazuje na nepřístupný typ {1}. Musí se použít anotace typu.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Odvozený typ tohoto uzlu přesahuje maximální d<PERSON>, k<PERSON>u kompilátor může serializovat. Je potřeba zadat explicitní anotaci typu.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "Iniciali<PERSON><PERSON><PERSON> „using“ musí být buď objekt s metodou „[Symbol.dispose]()“, nebo musí mít hodnotu „null“ nebo „undefined“.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "Inicializ<PERSON><PERSON> „await using“ musí být buď objekt s metodou „[Symbol.asyncDispose]()“ nebo „[Symbol.dispose]5D;()“, nebo musí mít hodnotu „null“ nebo „undefined“.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "Průnik {0} se omezil na never, protože vlastnost {1} existuje v několika konstituentech a v některých z nich je privátní.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "Průnik {0} se omezil na never, protože vlastnost {1} má v některých konstituentech konfliktní typy.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "Klíčové slovo intrinsic se dá použít jenom k deklaraci vnitřních typů poskytovaných kompilátorem.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Aby bylo možné p<PERSON>žít fragmenty JSX s možností kompilátoru jsxFactory, je třeba zadat možnost kompilátoru jsxFragmentFactory.", "The_last_overload_gave_the_following_error_2770": "Poslední přetížení vrátilo následující chybu.", "The_last_overload_is_declared_here_2771": "Poslední přetížení je deklarov<PERSON>é tady.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "Levá strana příkazu for...in nemůže být destrukturačním vzorem.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "Levá strana příkazu „for...in“ nemůže být deklarace „using“.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "Levá strana příkazu „for...in“ nemůže být deklarace „await using“.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "Levá strana příkazu for...in nemůže používat anotaci typu.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "Levá strana příkazu for...in nemůže představovat přístup k nepovinné vlastnosti.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "Levá strana příkazu for..n musí být proměnná nebo přístup k vlastnosti.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "<PERSON>á strana příkazu for...in musí být typu string nebo any.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "Levá strana příkazu for...of nemůže používat anotaci typu.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "Levá strana příkazu for...of nemůže představovat přístup k nepovinné vlastnosti.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "<PERSON><PERSON> strana příkazu for...of nemůže být async.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "Levá strana příkazu for...of musí být proměnná nebo přístup k vlastnosti.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "Levá strana aritmetické operace musí mít typ any, number, bigint nebo být typu výčtu.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "Levá strana výrazu přiřazení nemůže představovat přístup k nepovinné vlastnosti.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "Levá strana výrazu přiřazení musí být proměnná nebo přístup k vlastnosti.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "Levá strana výrazu „instanceof“ musí být přiřaditelná k prvnímu argumentu metody „[Symbol.hasInstance]“ na pravé straně.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "Levá strana výrazu instanceof musí být typu any, typem objektu nebo parametrem typu.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Národní prostředí, kter<PERSON> se používá při zobrazování zpráv uživateli (třeba cs-CZ)", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Maximální hloubka závislostí pro vyhledávání pod node_modules a načítání javascriptových souborů", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "Operandem operátoru delete nemůže být privátní identifikátor.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "Operandem operátoru delete nemůže být vlastnost určená jen pro čtení.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "Operandem operátoru delete musí být odkaz na vlastnost.", "The_operand_of_a_delete_operator_must_be_optional_2790": "Operand operátoru delete musí být <PERSON>.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "Operandem operátoru inkrementace nebo dekrementace nemůže být přístup k nepovinné vlastnosti.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "Operand operátoru inkrementace nebo dekrementace musí být proměnná nebo přístup k vlastnosti.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> najde token {1}, k<PERSON><PERSON> by od<PERSON><PERSON><PERSON><PERSON> tokenu {0} tady.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "Kořen projektu je ne<PERSON>dn<PERSON>, ale je vyžadován pro vyřešení <PERSON> {0} mapování exportu v souboru {1}. Pokud chcete zrušit dvojznačnost, zadejte možnost kompilátoru rootDir.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "Kořen projektu je ne<PERSON><PERSON>, ale je vyžadován pro vyřešení <PERSON> {0} mapování importu v souboru {1}. Pokud chcete zrušit dvojznačnost, zadejte možnost kompilátoru rootDir.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "K vlastnosti {0} se nedá přistupovat v typu {1} v této třídě, protože ho překrývá jiný privátní identifikátor se stejným zápisem.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Návratový typ funkce dekorátoru parametru funkce musí být void nebo any.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Návratový typ funkce dekorátoru vlastnosti musí být void nebo any.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Návratový typ asynchronní funkce musí být buď platný příslib, nebo nesmí obsahovat <PERSON>len then, který se dá volat.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "Návratový typ asynchronn<PERSON> funkce nebo metody musí být globální typ Promise<T>.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Návratový typ asynchronn<PERSON> funkce nebo metody musí být globální typ Promise<T>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jste napsat Promise<{0}>?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "Pravá strana příkazu for...in musí být typu any, typem objektu nebo parametrem typu, ale tady má typ {0}.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "Pravá strana aritmetické operace musí mít typ any, number, bigint nebo být typu výčtu.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "Pravá strana výrazu „instanceof“ musí být typ „any“, t<PERSON><PERSON><PERSON>, funkce nebo jiný typ, který se dá přiřadit k typu rozhraní „Function“, nebo typu objektu s metodou „Symbol.hasInstance“.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "Pravá strana výrazu „instanceof“ nesmí být výrazem vytvoření instance.", "The_root_value_of_a_0_file_must_be_an_object_5092": "Kořenová hodnota souboru {0} musí být objekt.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "Modul runtime vyvolá dekoratér s {1} argumenty, ale dekoratér o<PERSON> {0}.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "Modul runtime vyvolá dekoratér s {1} argumenty, ale dekoratér očekává alespoň {0}.", "The_shadowing_declaration_of_0_is_defined_here_18017": "Překrý<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON> {0} je defino<PERSON> tady.", "The_signature_0_of_1_is_deprecated_6387": "Signatura {0} pro {1} je zastaralá.", "The_specified_path_does_not_exist_Colon_0_5058": "Zadaná cesta neexistuje: {0}", "The_tag_was_first_specified_here_8034": "Značka se poprvé zadala tady.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "Cíl přiřazení rest objektu nemůže představovat přístup k nepovinné vlastnosti.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "Cílem přiřazení zbytku objektu musí být proměnná nebo přístup k vlastnosti.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "Kontext this typu {0} se nedá přiřadit k možnosti this metody typu {1}.", "The_this_types_of_each_signature_are_incompatible_2685": "Typy this jednotlivých signatur nejsou kompatibilní.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "Typ {0} je readonly a nedá se přiřadit k neměnnému typu {1}.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Pokud se v příkazu k exportu používá „export type“, nemůžete v pojmenovaném exportu použít modifikátor „type“.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Pokud se v příkazu k importu používá „import type“, nemůžete v pojmenovaném importu použít modifikátor „type“.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "<PERSON>p deklarace funkce musí odpovídat její signat<PERSON>.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "<PERSON><PERSON> tohoto typu nejde serializovat, protože nejde serializovat jeho vlastnost {0}.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Typ vrácený metodou {0}() asynchronního iterátoru musí být příslib pro typ s vlastností value.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Typ vrácený metodou {0}() iterátoru musí obsahovat vlastnost value.", "The_types_of_0_are_incompatible_between_these_types_2200": "Typy {0} nej<PERSON>u mezi těmito typy kompatibilní.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "<PERSON><PERSON> vr<PERSON><PERSON><PERSON><PERSON> met<PERSON> {0} nejsou mezi těmito typy kompatibilní.", "The_value_0_cannot_be_used_here_18050": "Hodnota „{0}“ se tady ned<PERSON> p<PERSON>.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "<PERSON><PERSON><PERSON> promě<PERSON><PERSON> p<PERSON> for...in nemůže obsahovat inicializátor.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "<PERSON><PERSON><PERSON> promě<PERSON><PERSON> p<PERSON> for...of nemůže obsahovat inicializátor.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "Př<PERSON>az with ne<PERSON><PERSON>. Všechny symboly s blokem with budou typu any.", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "V „{0}“ jsou typy, ale tento výsledek se v aktuálním nastavení „moduleResolution“ nepovedlo vyřešit. Zvažte aktualizaci na „node16“, „nodenext“ nebo „bundler“.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "V „{0}“ jsou typy, ale tento výsledek se při respektování pole „exports“ souboru package.json nepodařilo vyřešit. Knihovna „{1}“ bude pravděpodobně muset aktualizovat svůj soubor package.json nebo typings.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "V tomto regulárním výrazu není žádná zachycující skupina s názvem „{0}“.", "There_is_nothing_available_for_repetition_1507": "Není k dispozici nic pro opakování.", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "Tato značka JSX vyžaduje, aby objekt pro vytváření fragmentů {0} byl v oboru, ale nepovedlo se ho najít.", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "Tato značka JSX vyžaduje, aby existovala cesta k modulu {0}, ale žádná nebyla nalezena. Ujistěte se, že máte nainstalované typy pro příslušný balíček.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Vlastnost {0} této značky JSX očekává jeden podřízený objekt typu {1}, ale poskytlo se jich více.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Vlastnost {0} této značky JSX očekává typ {1}, který vyžaduje více podřízených objektů, ale zadal se jen jeden.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "Tento zpětný odkaz odkazuje na skupinu, která neexistuje. V tomto regulárním výrazu nejsou žádné zachytávací skupiny.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "Tento zpětný odkaz odkazuje na skupinu, která neexistuje. V tomto regulárním výrazu jsou pouze {0} zachytávací skupiny.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "Tento binární výraz nikdy nemá hodnotu null. Nechybí vám závorky?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "Tento znak nelze uvozovat v regulárním výrazu.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Toto porovnání se zd<PERSON> b<PERSON>, proto<PERSON>e typy {0} a {1} se nijak nepřekrývají.", "This_condition_will_always_return_0_2845": "Tato podmínka vždy vrátí {0}.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "<PERSON>to podmínka vždy vrátí „{0}“, protože JavaScript porovnává objekty pomocí odkazu, niko<PERSON> hodnot<PERSON>.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "<PERSON>to podmínka vždy vrátí hodnotu True, proto<PERSON>e tato {0} je v<PERSON><PERSON> definovan<PERSON>.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Tato podmínka vždy vrátí hodnotu True, protože tato funkce je v<PERSON><PERSON> defino<PERSON>. Chtěli jste ji místo toho nazvat?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Tato funkce konstruktoru se může převést na deklaraci třídy.", "This_expression_is_always_nullish_2871": "Tento výraz má vždy hodnotu null.", "This_expression_is_not_callable_2349": "Tento výraz se nedá zavolat.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Tento výraz se nedá volat, proto<PERSON><PERSON> je to přístupový objekt get. Nechtěli jste ho použít bez ()?", "This_expression_is_not_constructable_2351": "Tento výraz se nedá vytvořit.", "This_file_already_has_a_default_export_95130": "Tento soubor už má výchozí export.", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "Přepsání této cesty importu není <PERSON>, protože cesta se překládá na jiný projekt a relativní cesta mezi výstupními soubory projektů není stejná jako relativní cesta mezi příslušnými vstupními soubory.", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "Tento import používá k překladu na vstupní soubor TypeScript rozšíření {0}, ale během generování se nepřepíše, protože se nejedná o relativní cestu.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Toto je de<PERSON>, kter<PERSON> se rozšiřuje. Zvažte možnost přesunout rozšiřující deklaraci do stejného souboru.", "This_kind_of_expression_is_always_falsy_2873": "Tento druh výrazu je vž<PERSON>pra<PERSON>div<PERSON>.", "This_kind_of_expression_is_always_truthy_2872": "Tento druh výrazu je vždy pravdivý.", "This_may_be_converted_to_an_async_function_80006": "Toto je možné převést na asynchronní funkci.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Tento člen nemůže mít komentář JSDoc se znač<PERSON>u @override, protože není <PERSON> v základní třídě {0}.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Tento člen nemůže mít komentář JSDoc se znač<PERSON>u @override, protože není <PERSON> v základní třídě {0}. <PERSON><PERSON>li jste na mysli {1}?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Tento člen nemůže mít komentář <PERSON> se znač<PERSON>u @override, proto<PERSON>e třída {0}, kter<PERSON> ho obsahuje, nero<PERSON><PERSON><PERSON><PERSON><PERSON>je jinou třídu.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "Tento člen nemůže mít komentář JSDoc se znač<PERSON>u @override, protože jeho název je dynamic<PERSON>ý.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Tento člen nemůže mít modifikátor override, protože není deklarovaný v základní třídě {0}.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Tento člen nemůže mít modifikátor override, protože není dekla<PERSON>ý v základní třídě {0}. <PERSON><PERSON>li jste na mysli {1}?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Tento <PERSON><PERSON> nemůže mít modifik<PERSON><PERSON> override, proto<PERSON><PERSON> t<PERSON> {0}, k<PERSON><PERSON> ho obsahuje, ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jinou třídu.", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "<PERSON>to <PERSON><PERSON> ne<PERSON>ůže mít modifik<PERSON><PERSON> override, proto<PERSON>e jeho n<PERSON>zev je <PERSON>.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Tento člen musí mít komentář JSDoc se značkou @override, protože přepisuje člen v základní třídě {0}.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Tento člen musí mít modifik<PERSON><PERSON> override, protože přepisuje člen v základní třídě {0}.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Tento <PERSON><PERSON> musí mít modifik<PERSON><PERSON> override, proto<PERSON>e přepisuje abstraktní metodu, která je deklarovaná v základní třídě {0}.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Na tento modul je možné se pomocí importů nebo exportů ECMAScript odkazovat jen tak, že se zapne příznak {0} a odkáže se na výchozí export.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Tento modul se deklaroval pomocí export =, a dá se použít jenom s výchozím importem při použití příznaku {0}.", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "Tato operace se dá zjednodušit. Tento posun je totožný s {0} {1} {2}.", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "Toto přetížení implicitn<PERSON> vrací typ „{0}“, proto<PERSON>e postrádá anotaci návratového typu.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Tato signatura přetížení není kompatibilní se signaturou implementace.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Tento parametr se nepodporuje s direktivou use strict.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Tato vlastnost parametru musí mít komentář JSDoc se značkou @override, protože přepisuje člen v základní třídě {0}.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Tato vlastnost parametru musí mít modifikátor override, protože přepisuje člen v základní třídě {0}.", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "Tento příznak regulárního výrazu nelze přepnout v rámci dílčího vzoru.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "Tento příznak regulárního výrazu je k dispozici pouze při cílení na „{0}“ nebo novější.", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "Přepsání této relativní cesty importu není <PERSON>, protože cesta vypadá jako n<PERSON><PERSON>v souboru, ale ve skutečnosti se překládá na {0}.", "This_spread_always_overwrites_this_property_2785": "Tento roz<PERSON>h vždy přepíše tuto vlastnost.", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "Tato syntaxe není povolen<PERSON>, pokud je povolená možnost erasableSyntaxOnly.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Tato syntaxe je vyhrazená pro soubory s příponou .mts nebo .cts. Přidejte koncovou čárku nebo explicitní omezení.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Tato syntaxe je vyhrazená pro soubory s příponou .mts nebo .cts. Místo toho použijte výraz „as“.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Tato syntaxe vyžaduje importovanou podpůrnou aplikaci, ale modul {0} se nena<PERSON><PERSON>.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Tato syntaxe vyžaduje importovanou pomocnou rutinu s názvem {1}, která v {0} neexistuje. Zvažte možnost upgradovat verzi {0}.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Tato syntaxe vyžaduje importovanou pomocnou rutinu s názvem {1} a parametry {2}, k<PERSON><PERSON> není kompatibilní s tou v {0}. Zvažte upgrade verze {0}.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Tento parametr typu může potřebovat omezení extends {0}.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Toto použití importu není platné. Volání import() se dají zap<PERSON>, ale musí mít závorky a nemůžou mít typové argumenty.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Pokud chcete tento soubor převést na modul ECMAScript, přidejte pole \"type\": \"module\" do {0}.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Pokud chcete tento soubor převést na modul ECMAScript, změňte jeho příponu na {0}\" nebo přidejte pole \"type\": \"module\" do {1}.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Pokud chcete tento soubor převést na modul ECMAScript, změňte jeho příponu na {0} nebo vytvořte místní soubor package.json s {\"type\": \"module\"}.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Pokud chcete tento soubor převést na modul ECMAScript, vytvořte místní soubor package.json s { \"type\": \"module\" }.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Výrazy await nej<PERSON><PERSON><PERSON><PERSON> se povolují jen v případě, že možnost module je nastavená na es2022, esnext, system, node16, node18 nodenext nebo preserve a možnost target je nastavená na es2017 nebo vyšší.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "Výrazy await using nej<PERSON><PERSON><PERSON><PERSON> se povolují jen v případě, že možnost module je nastavená na es2022, esnext, system, node16, node18, nodenext nebo preserve a možnost target je nastavená na es2017 nebo vyšší.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Deklarace nejvyšší úrovně v souborech .d.ts musí začínat modifikátorem declare, nebo export.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "<PERSON><PERSON><PERSON><PERSON> for await nej<PERSON><PERSON><PERSON><PERSON> se povolují jen v případě, že možnost module je nastavená na es2022, esnext, system, node16, node18, nodenext nebo preserve a možnost target je nastavená na es2017 nebo vyšší.", "Trailing_comma_not_allowed_1009": "Čárka na konci není povolená.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transpiluje každý soubor jako sa<PERSON>ný modul (podobné jako ts.transpileModule).", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Vyzkoušejte deklaraci npm i --save-dev @types/{1}, pokud existuje, nebo přidejte nový soubor de<PERSON> (.d.ts) s dekla<PERSON><PERSON> declare module '{0}';.", "Trying_other_entries_in_rootDirs_6110": "Zkoušejí se další položky v rootDirs.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Zkouší se nahrazení {0}, umístění modulu kandidáta: {1}.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "Typ řazené kolekce členů {0} délky {1} nemá na indexu {2} žádný prvek.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Argumenty typů řazené kolekce členů cyklicky odkazují samy na sebe.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "Typem {0} se dá iterovat, pouze když se použije příznak --downlevelIteration nebo s možností --target nastavenou na es2015 nebo vyšší.", "Type_0_cannot_be_used_as_an_index_type_2538": "Typ {0} se ned<PERSON> použít jako typ indexu.", "Type_0_cannot_be_used_to_index_type_1_2536": "Typ {0} nejde p<PERSON>žít k indexování typu {1}.", "Type_0_does_not_satisfy_the_constraint_1_2344": "Typ {0} nevyhov<PERSON>je omezení {1}.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "Typ {0} nevyhovuje očekávanému typu {1}.", "Type_0_has_no_call_signatures_2757": "Typ {0} nemá žádné signatury volání.", "Type_0_has_no_construct_signatures_2761": "Typ {0} nemá žádné signatury konstruktu.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "Typ {0} nemá odpovídající signaturu indexu pro typ {1}.", "Type_0_has_no_properties_in_common_with_type_1_2559": "Typ {0} nemá žádné vlastnosti společné s typem {1}.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "U typu {0} ne<PERSON><PERSON><PERSON>, pro které platí se<PERSON>nam argumentů obecného typu.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "<PERSON>p „{0}“ je obecný a lze ho indexovat pouze pro čtení.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "V typu {0} chybí následující vlastnosti z typu {1}: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "V typu {0} chybí následující vlastnosti z typu {1}: {2} a ještě {3}", "Type_0_is_not_a_constructor_function_type_2507": "Typ {0} nen<PERSON> typ <PERSON>ce konstruktoru.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "Typ „{0}“ nepředstavuje platný návratový typ asynchronní funkce v ES5, protože neodkazuje na hodnotu konstruktoru kompatibilní s konstruktorem Promise.", "Type_0_is_not_an_array_type_2461": "<PERSON>p {0} nen<PERSON> typ pole.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Typ {0} není typem pole nebo řetězce.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "Typ {0} není typem pole nebo řetěz<PERSON>, nebo nem<PERSON> metodu [Symbol.iterator](), k<PERSON><PERSON> vrací iterátor.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "Typ {0} nen<PERSON> typem pole, nebo nem<PERSON> metodu [Symbol.iterator](), kter<PERSON> vrací iterátor.", "Type_0_is_not_assignable_to_type_1_2322": "Typ {0} nejde p<PERSON> typu {1}.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "Typ {0} se ned<PERSON> přiř<PERSON>t k typu {1}. <PERSON><PERSON><PERSON> jste na mysli {2}?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Typ {0} se ned<PERSON> přiř<PERSON>t typu {1}. Existují dva různé typy s tímto názvem, ale nesouvisí spolu.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Typ {0} nel<PERSON> přiř<PERSON> k typu {1}, jak je implik<PERSON> anotací o<PERSON>.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "Typ „{0}“ nelze přiřadit k typu „{1}“, jak je vyžadováno pro vypočítané hodnoty členů výčtu.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "Typ {0} se nedá přiřadit k typu {1} s hodnotou exactOptionalPropertyTypes: true. Zvažte možnost přidat hodnotu undefined do typů vlastností cíle.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "Typ {0} se nedá přiřadit k typu {1} s hodnotou exactOptionalPropertyTypes: true. Zvažte možnost přidat hodnotu undefined do typu cíle.", "Type_0_is_not_comparable_to_type_1_2678": "Typ {0} se ned<PERSON> porovnat s typem {1}.", "Type_0_is_not_generic_2315": "<PERSON>p {0} ne<PERSON><PERSON>.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Typ {0} m<PERSON>že představovat primitivní hodnot<PERSON>, k<PERSON><PERSON> není povolena jako pravý operand operátoru in.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Typ {0} musí mít metodu [Symbol.asyncIterator](), která vrací asynchronní iterátor.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Typ {0} musí mít metodu [Symbol.iterator](), k<PERSON><PERSON> vrací iterátor.", "Type_0_provides_no_match_for_the_signature_1_2658": "Typ {0} neposkytuje žádnou shodu pro podpis {1}.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Typ {0} odkazuje rekurzivně sám na sebe jako na základní typ.", "Type_Checking_6248": "<PERSON><PERSON><PERSON><PERSON>", "Type_alias_0_circularly_references_itself_2456": "<PERSON>as typu {0} odkazuje cyklicky sám na sebe.", "Type_alias_must_be_given_a_name_1439": "Alias typu musí mít název.", "Type_alias_name_cannot_be_0_2457": "<PERSON><PERSON><PERSON>v aliasu typu ne<PERSON>ů<PERSON> b<PERSON>t {0}.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "Aliasy typů se dají používat jen v typescriptových souborech.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "V deklaraci konstruktoru se nemůže objevit anotace typu.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Anotace typů se dají používat jen v typescriptových souborech.", "Type_argument_expected_1140": "Očekává se argument typu.", "Type_argument_list_cannot_be_empty_1099": "Seznam argumentů typu nemůže být prázdný.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Argumenty typů se dají používat jen v typescriptových souborech.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Argumenty typů pro {0} se cyklicky odkazují samy na sebe.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Kontrolní výrazy typů se dají používat jen v typescriptových souborech.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Typ na pozici {0} ve zdroji není kompatibilní s typem na pozici {1} v cíli.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Typ na pozicích {0} až {1} ve zdroji není kompatibilní s typem na pozici {2} v cíli.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "Typ obsahující privátní název „{0}“ nejde použít s možností --isolatedDeclarations.", "Type_declaration_files_to_be_included_in_compilation_6124": "<PERSON><PERSON><PERSON> t<PERSON>, k<PERSON><PERSON> se mají zahrnout do kompilace", "Type_expected_1110": "Očekával se typ.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Kontrolní výrazy importu typů by m<PERSON><PERSON> m<PERSON><PERSON> p<PERSON> jeden kl<PERSON> – resolution-mode – s hodnotou import nebo require.", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "Atributy importu typů by m<PERSON><PERSON> m<PERSON><PERSON> jeden k<PERSON> – „resolution-mode“ – s hodnotou „import“ nebo „require“.", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "Import typu modulu ECMAScript z modulu CommonJS musí mít atribut resolution-mode.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "Vytvoření instance typu je pří<PERSON>š hluboké a může být nekonečné.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Typ se přímo nebo nepřímo odkazuje ve zpětném volání jeho vlastní metody then při splnění.", "Type_library_referenced_via_0_from_file_1_1402": "<PERSON><PERSON><PERSON><PERSON> typů, na kterou se odkazuje přes {0} ze souboru {1}", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "<PERSON><PERSON><PERSON><PERSON> typů, na kterou se odkazuje přes {0} ze souboru {1} s packageId {2}", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Typ operandu await musí být buď platný příslib, nebo nesmí obsahovat člen then, který se dá volat.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Typ hodnoty počítané vlastnosti je {0} a nedá se př<PERSON>řadit do typu {1}.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Typ instance členské proměnné {0} nemůže odkazovat na identifikátor {1} deklarovaný v konstruktoru.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Typ iterovaných elementů yield* musí být buď platný příslib, nebo nesmí obsahovat člen then, který se dá volat.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Typ vlastnosti {0} cyklicky odkazuje sám na sebe v mapovaném typu {1}.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Typ operandu yield v asynchronním generátoru musí být buď platný příslib, nebo nesmí obsahovat člen then, který se dá volat.", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "I<PERSON>rt, p<PERSON>i k<PERSON>m se importuje pouze typ modulu ECMAScript z modulu CommonJS, musí mít atribut resolution-mode.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Typ pochází z tohoto importu. Import stylu oboru názvů není možné zavolat ani vytvořit a při běhu způsobí chybu. Zvažte možnost použít tady místo toho výchozí import nebo importovat require.", "Type_parameter_0_has_a_circular_constraint_2313": "Parametr typu {0} má cyklické omezení.", "Type_parameter_0_has_a_circular_default_2716": "Parametr typu {0} má cyklickou výchozí hodnotu.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Parametr typu {0} signatury volání z exportovaného rozhraní má nebo používá privátní název {1}.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Parametr typu {0} signatury konstruktoru z exportovaného rozhraní má nebo používá privátní název {1}.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Parametr typu {0} exportované třídy má nebo používá privátní název {1}.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Parametr typu {0} exportované <PERSON>ce má nebo používá privátní název {1}.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Parametr typu {0} exportovaného rozhraní má nebo používá privátní název {1}.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Parametr typu {0} exportovaného typu namapovaného objektu typu má nebo používá privátní název {1}.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Parametr typu {0} exportovaného aliasu typu má nebo používá privátní název {1}.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Parametr typu {0} metody z <PERSON>ovaného rozhraní má nebo používá privátní název {1}.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Parametr typu {0} veřejné metody z exportované třídy má nebo používá privátní název {1}.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Parametr typu {0} ve<PERSON><PERSON>né static<PERSON>é metody z exportované třídy má nebo používá privátní název {1}.", "Type_parameter_declaration_expected_1139": "Očekává se deklarace parametru typu.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Deklarace parametrů typů se dají používat jen v typescriptových souborech.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Výchozí parametry typů se můžou odkazovat jen na dříve deklarované parametry typů.", "Type_parameter_list_cannot_be_empty_1098": "Seznam parametrů typu nemůže být prázdný.", "Type_parameter_name_cannot_be_0_2368": "Název parametru typu nemůže být {0}.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Parametry typu se nemůžou vyskytovat v deklaraci konstruktoru.", "Type_predicate_0_is_not_assignable_to_1_1226": "Predik<PERSON><PERSON> typu {0} nejde přiř<PERSON> {1}.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "Typ tvoří typ řazené kolekce členů, k<PERSON><PERSON> se ned<PERSON> reprezent<PERSON>t, proto<PERSON>e je p<PERSON><PERSON><PERSON> velk<PERSON>.", "Type_reference_directive_0_was_not_resolved_6120": "======== Direktiva odkazu na typ {0} se nepřeložila. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== Direktiva odkazu na typ {0} se úspěšně přeložila na {1}, primární: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== Direktiva odkazu na typ {0} se úspěšně přeložila na {1} s ID balíčku {2}, primární: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Kontrolní výrazy typů se dají používat jen v typescriptových souborech.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Typy se v deklaracích exportu v souborech JavaScriptu nemůžou vyskytovat.", "Types_have_separate_declarations_of_a_private_property_0_2442": "<PERSON>py mají samostatné deklarace privátní vlastnosti {0}.", "Types_of_construct_signatures_are_incompatible_2419": "Typy signatur konstruktorů nejsou kompatibilní.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Typy parametrů {0} a {1} jsou nekompatibilní.", "Types_of_property_0_are_incompatible_2326": "Typy vlastnosti {0} nejsou kompatibilní.", "Unable_to_open_file_0_6050": "<PERSON><PERSON><PERSON> {0} nej<PERSON> o<PERSON>.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "<PERSON><PERSON>ž se podpis dekorátoru třídy volá jako výraz, nejde přeložit.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "<PERSON><PERSON><PERSON> se podpis dekorátoru metody volá jako výraz, nejde přeložit.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "<PERSON><PERSON>ž se podpis dekorátoru parametru volá jako výraz, nejde přeložit.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "<PERSON><PERSON>ž se podpis dekorátoru vlastnosti volá jako výraz, nejde přeložit.", "Undetermined_character_escape_1513": "Neurčený řídicí znak.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: „{0}“. Nechtěli jste ho uvést zpětným lomítkem?", "Unexpected_end_of_text_1126": "Neočekávaný konec textu", "Unexpected_keyword_or_identifier_1434": "Neoček<PERSON><PERSON><PERSON> k<PERSON>čové slovo nebo identifikátor.", "Unexpected_token_1012": "<PERSON><PERSON><PERSON><PERSON><PERSON> token", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Neočekávaný token. <PERSON><PERSON><PERSON><PERSON><PERSON> se konstruktor, metoda, přístupový objekt nebo vlastnost.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Neočekávaný token. Očekával se název parametru typu bez složených závorek.", "Unexpected_token_Did_you_mean_or_gt_1382": "Neočekávaný token. <PERSON><PERSON><PERSON> jste na mysli {'>'} nebo &gt;?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Neočekávaný token. <PERSON><PERSON><PERSON> jste na mysli {'}'} nebo &r<PERSON>ce;?", "Unexpected_token_expected_1179": "Neočekávaný token. Očekává se znak {.", "Unicode_escape_sequence_cannot_appear_here_17021": "Řídicí sekvence Unicode se tady nemůže vyskytovat.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "Řídicí sekvence Unicode jsou k dispozici pouze v případě, že je nastaven příznak Unicode (u) nebo Unicode Sets (v).", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "Výrazy hodnoty vlastnosti Unicode jsou k dispozici pouze v případě, že je nastaven příznak Unicode (u) nebo Unicode Sets (v).", "Unknown_Unicode_property_name_1524": "Neznámý název vlastnosti Unicode.", "Unknown_Unicode_property_name_or_value_1529": "Neznámý název nebo hodnota vlastnosti Unicode.", "Unknown_Unicode_property_value_1526": "Neznámá hodnota vlastnosti Unicode.", "Unknown_build_option_0_5072": "Neznámá možnost sestavení {0}", "Unknown_build_option_0_Did_you_mean_1_5077": "Neznámá možnost sestavení {0}. <PERSON><PERSON><PERSON> j<PERSON> na mysli {1}?", "Unknown_compiler_option_0_5023": "Neznámá možnost kompilátoru {0}", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Neznámá možnost kompilátoru {0}. <PERSON><PERSON><PERSON> j<PERSON> na mysli {1}?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Neznámé klíčové slovo nebo identifikátor. Neměli jste na mysli „{0}“?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Neznámá možnost excludes. <PERSON><PERSON><PERSON> j<PERSON> na mysli exclude?", "Unknown_regular_expression_flag_1499": "Neznámý příznak regulárního výrazu.", "Unknown_type_acquisition_option_0_17010": "Neznámá možnost získání typu {0}", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Neznámá možnost získání typu {0}. <PERSON><PERSON><PERSON> j<PERSON> na mysli {1}?", "Unknown_watch_option_0_5078": "Neznámá možnost sledování {0}", "Unknown_watch_option_0_Did_you_mean_1_5079": "Neznámá možnost sledování {0}. <PERSON><PERSON><PERSON> j<PERSON> na mysli {1}?", "Unreachable_code_detected_7027": "Zjistil se nedosažitelný kód.", "Unterminated_Unicode_escape_sequence_1199": "Neukončená řídicí sekvence Unicode", "Unterminated_quoted_string_in_response_file_0_6045": "Neukončený řetězec v uvozovkách v souboru odezvy {0}", "Unterminated_regular_expression_literal_1161": "Neukončený literál regulárního výrazu", "Unterminated_string_literal_1002": "Neukončený řetězcový literál", "Unterminated_template_literal_1160": "Neukončený literál š<PERSON>lony", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Volání netypové funkce nemusí přijmout argumenty typu.", "Unused_label_7028": "Nepoužívan<PERSON> popisek", "Unused_ts_expect_error_directive_2578": "Nepoužitá direktiva @ts-expect-error", "Update_import_from_0_90058": "Aktualizovat import z: {0}", "Update_modifiers_of_0_90061": "Aktualizujte modifikátory „{0}“", "Updating_output_timestamps_of_project_0_6359": "Aktualizují se výstupní časová razítka projektu {0}...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Aktualizují se nezměněná výstupní časová razítka projektu {0}...", "Use_0_95174": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "Use_0_instead_5106": "<PERSON><PERSON><PERSON> toho použijte možnost „{0}“.", "Use_Number_isNaN_in_all_conditions_95175": "Ve všech podmínkách použijte Number.isNaN.", "Use_element_access_for_0_95145": "Použít přístup k elementům pro {0}", "Use_element_access_for_all_undeclared_properties_95146": "Použít přístup k elementům pro všechny nedeklarované vlastnosti", "Use_import_type_95180": "Použijte „import type“.", "Use_synthetic_default_member_95016": "Použije syntetického výchozího člena.", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "Při překladu import<PERSON> balí<PERSON> použijte pole „exports“ souboru package.json.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "Při řešení importů použijte pole „imports“ v souboru package.json.", "Use_type_0_95181": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „type {0}„.", "Using_0_subpath_1_with_target_2_6404": "Používá se {0} dílčí cesta {1} s cílem {2}.", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "Použití fragmentů JSX vyžaduje, aby objekt pro vytváření fragmentů {0} byl v oboru, ale nepovedlo se ho najít.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Použití řetězce v příkazu for...of se podporuje jenom v ECMAScript 5 nebo vyšší verzi.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Použití --build, -b <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že se tsc bude chovat spíše jako orchestrátor sestavení než kompilátor. Pomocí této možnosti můžete aktivovat vytváření složených projektů, o kterých se můžete dozvědět více {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Using compiler options of project reference redirect '{0}'.", "VERSION_6036": "VERZE", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "Hodnota typu {0} nemá žádné vlastnosti společné s typem {1}. Chtěli jste ji volat?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Hodnota typu {0} se nedá volat. Nechtěli jste zahrnout new?", "Variable_0_implicitly_has_an_1_type_7005": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0} má implicitně typ {1}.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "<PERSON>m<PERSON><PERSON><PERSON> {0} má implicitně typ {1}, ale je možn<PERSON>, že lep<PERSON> typ by se vyvodil z využití.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "Proměnná {0} má na některých místech implicitně typ {1}, ale je mož<PERSON>, že lep<PERSON> typ by se vyvodil z využití.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "<PERSON> někt<PERSON><PERSON><PERSON>, kde se nedá určit typ proměnn<PERSON>, má proměnná {0} implicitně typ {1}.", "Variable_0_is_used_before_being_assigned_2454": "<PERSON>m<PERSON><PERSON><PERSON> {0} je pou<PERSON><PERSON><PERSON> před přiřazením.", "Variable_declaration_expected_1134": "Očekává se deklarace proměnné.", "Variable_declaration_list_cannot_be_empty_1123": "Seznam deklarací proměnných nemůže být prázdný.", "Variable_declaration_not_allowed_at_this_location_1440": "Deklarace proměnné není v tomto umístění povolená.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "Proměnná musí mít explicitní anotaci typu s možností --isolatedDeclarations.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "Proměnné s více deklaracemi nemohou být vložené.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "Element variadic na pozici {0} ve zdroji neodpovídá elementu na pozici {1} v cíli.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Poznámky Variance se podporují pouze u <PERSON><PERSON> typů pro typy objekt<PERSON>, <PERSON><PERSON><PERSON>, konstr<PERSON><PERSON><PERSON> a mapování.", "Version_0_6029": "Verze {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Další informace o tomto souboru si můžete přečíst na https://aka.ms/tsconfig", "WATCH_OPTIONS_6918": "MOŽNOSTI SLEDOVÁNÍ", "Watch_and_Build_Modes_6250": "Re<PERSON><PERSON>y sledování a sestavování", "Watch_input_files_6005": "<PERSON><PERSON>ovat vstupn<PERSON> so<PERSON>ory", "Watch_option_0_requires_a_value_of_type_1_5080": "Možnost sledování {0} vyžaduje hodnotu typu {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Pro {0} m<PERSON>ž<PERSON>e napsat typ jenom tak, že sem přidáme typ pro celý parametr.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Při přiřazování funkcí zkontrolujte a zajistěte, aby parametry a vrácené hodnoty měly kompatibilní podtypy.", "When_type_checking_take_into_account_null_and_undefined_6699": "Při kontrole typů berte v potaz i hodnoty „null“ a „undefined“.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jestli se místo vymazání obrazovky má zachovat zastaralý výstup konzoly v režimu sledování.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Zabalit všechny neplatné znaky do kontejneru výrazu", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "Uzavřít všechny neplatné výrazy dekoratéru do závorek", "Wrap_all_object_literal_with_parentheses_95116": "Uzavřít všechny literály objektů do závorek", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Zabalit všechny JSX bez nadřazených položek ve fragmentu JSX", "Wrap_in_JSX_fragment_95120": "Zabalit ve fragmentu JSX", "Wrap_in_parentheses_95194": "Uzavřít do závorek", "Wrap_invalid_character_in_an_expression_container_95108": "Zabalit neplatný znak do kontejneru výrazu", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, k<PERSON><PERSON> by m<PERSON><PERSON> b<PERSON>t literál objektu, do <PERSON><PERSON><PERSON>", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Informace o všech možnostech kompilátoru najdete na {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Přes globální import se modul nedá přejmenovat.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Nelze přejmenovat elementy definované ve složce node_modules.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Nelze přejmenovat elementy definované v jiné složce node_modules.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Nejde přejmenovat elementy definované ve standardní knihovně Type<PERSON>u.", "You_cannot_rename_this_element_8000": "Tento element nejde přejmenovat.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "Objekt {0} přijí<PERSON><PERSON> málo argumentů k tomu, aby se dal použ<PERSON>t jako de<PERSON>. Nechtěli jste ho nejprve volat a napsat @{0}()?", "_0_and_1_index_signatures_are_incompatible_2330": "Signatury indexu {0} a {1} jsou nekompatibilní.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Operace {0} a {1} se neda<PERSON>í kombinovat bez závorek.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "Položka {0} je zadána dvakr<PERSON>. Atribut s názvem {0} se přepíše.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "„{0}“ na konci typu není platná syntaxe TypeScriptu. Nechtěli jste napsat „{1}“?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "„{0}“ na začátku typu není platná syntaxe TypeScriptu. Nechtěli jste napsat „{1}“?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "{0} se dá importovat jen zapnutím příznaku esModuleInterop a pomocí výchozího importu.", "_0_can_only_be_imported_by_using_a_default_import_2595": "{0} se dá importovat jen pomocí výchozího importu.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "{0} se dá importovat jen pomocí volání require nebo zapnutím příznaku esModuleInterop a pomocí výchozího importu.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "{0} se dá importovat jen pomocí volání require nebo pomocí výchozího importu.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "{0} se dá importovat jen pomo<PERSON> import {1} = require({2}) nebo výchozího importu.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "{0} se dá importovat jen pomocí import {1} = require({2}) nebo zapnutím příznaku esModuleInterop a pomocí výchozího importu.", "_0_cannot_be_used_as_a_JSX_component_2786": "{0} se nedá použít jako součást JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "{0} se nedá používat jako hodnota, protože se exportovalo pomocí export type.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "{0} se nedá používat jako hodn<PERSON>, protože se importovalo pomocí import type.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Komponenty {0} nepřijímají text jako podří<PERSON> prvky. Text v JSX má typ string, ale očekávaný typ {1} je {2}.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "Instanci {0} by by<PERSON> <PERSON><PERSON><PERSON> s libovolným typem, k<PERSON><PERSON> by ne<PERSON><PERSON> souviset s {1}.", "_0_declarations_can_only_be_declared_inside_a_block_1156": "<PERSON><PERSON><PERSON> „{0}“ je mo<PERSON>n<PERSON> de<PERSON>at jenom uvnitř bloku.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "Deklarace {0} se da<PERSON><PERSON> p<PERSON>žívat jen v typescriptových souborech.", "_0_declarations_may_not_have_binding_patterns_1492": "<PERSON><PERSON><PERSON> „{0}“ nesmí mít vzory s vazbami.", "_0_declarations_must_be_initialized_1155": "<PERSON><PERSON><PERSON> „{0}“ se musejí inicializovat.", "_0_expected_1005": "<PERSON><PERSON><PERSON><PERSON><PERSON> se: {0}.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "„{0}“ má typ řetězce, ale pokud je povolená možnost isolatedModules, musí mít syntakticky rozpoznatelnou syntaxi řetězce.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "{0} nemá žádný exportovaný člen s názvem {1}. Neměli jste na mysli {2}?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "{0} má implicitně návratový typ {1}, ale je možn<PERSON>, že lepš<PERSON> typ by se vyvodil z využití.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "{0} obsahu<PERSON> implicitně návratový typ any, protože neobsahuje anotaci návratového typu a přímo nebo nepřímo se odkazuje v jednom ze svých návratových výrazů.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "{0} má implicitně typ any, protože nemá anotaci typu a odkazuje se přímo nebo nepřímo v jeho vlastním inicializátoru.", "_0_index_signatures_are_incompatible_2634": "Signatury indexu {0} jsou nekompatibilní.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "{0} Typ indexu {1} se ned<PERSON> př<PERSON>ř<PERSON>t k {2} typu indexu {3}.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "{0} je primitivum, ale {1} je ob<PERSON><PERSON><PERSON><PERSON> objekt. Pokud je to mo<PERSON><PERSON><PERSON>, použ<PERSON><PERSON><PERSON> raději {0}.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "{0} je typ a nedá se importovat do javascriptových souborů. V poznámce typu JSDoc použijte {1}.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "„{0}“ je typ a musí se importovat pomocí importu „pouze typ“, pokud je povolená možnost verbatimModuleSyntax.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "{0} je ne<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ní {1}. Chtěli jste ji použít jako poz<PERSON>mku typu?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "{0} se dá přiřadit k omezení typu {1}, ale pro {1} se dala vytvořit instance s jiným podtypem omezení {2}.", "_0_is_automatically_exported_here_18044": "{0} se sem automaticky exportuje.", "_0_is_declared_but_its_value_is_never_read_6133": "<PERSON><PERSON><PERSON><PERSON> se {0}, ale jeho hodnota se v<PERSON><PERSON><PERSON> neč<PERSON>.", "_0_is_declared_but_never_used_6196": "{0} se <PERSON><PERSON><PERSON><PERSON><PERSON>, ale ne<PERSON>.", "_0_is_declared_here_2728": "{0} je <PERSON><PERSON><PERSON><PERSON> tady.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "{0} je definované jako vlast<PERSON>t ve třídě {1}, ale v {2} se tady přepisuje jako přístupový objekt.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "{0} je definované jako p<PERSON>pový objekt ve třídě {1}, ale v {2} se tady přepisuje jako vlastnost instance.", "_0_is_deprecated_6385": "{0} je <PERSON><PERSON>.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "{0} není platnou metavlastností pro klíčové slovo {1}. <PERSON><PERSON><PERSON> jste na mysli {2}?", "_0_is_not_allowed_as_a_parameter_name_1390": "{0} nen<PERSON> povolen jako n<PERSON>zev parametru.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "{0} se nepovoluje jako n<PERSON><PERSON>v deklarace proměnné.", "_0_is_of_type_unknown_18046": "„{0}“ je typ unknown", "_0_is_possibly_null_18047": "„{0}“ je pravděpodobně typ null.", "_0_is_possibly_null_or_undefined_18049": "„{0}“ je typ null nebo undefined", "_0_is_possibly_undefined_18048": "„{0}“ je prav<PERSON><PERSON><PERSON>dobně typ undefined.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "Na {0} se přímo nebo nepřímo odkazuje ve vlastním základním výrazu.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "Na {0} se odkazuje přímo nebo nepřímo v jeho vlastní anotaci typu.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "{0} se zadalo více než jednou, proto se toto použ<PERSON>í p<PERSON>.", "_0_list_cannot_be_empty_1097": "Seznam {0} nemůže být prázdný.", "_0_modifier_already_seen_1030": "Modifikátor {0} se už jednou vyskytl.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "Modifikátor {0} se může vyskytovat jenom u parametru typu aliasu třídy, rozhraní nebo typu.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "<PERSON>di<PERSON><PERSON><PERSON><PERSON> „{0}“ se může vyskytovat jenom u parametru typu funkce, metody nebo třídy.", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "Modifikátor {0} se nemůže objevit v deklaraci konstruktoru.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "Modifikátor {0} se nemůže objevit v elementu modulu nebo oboru názvů.", "_0_modifier_cannot_appear_on_a_parameter_1090": "Modifikátor {0} se nemůže objevit v parametru.", "_0_modifier_cannot_appear_on_a_type_member_1070": "Modifikátor {0} se nemůže objevit u člena typu.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "Modifikátor {0} se nemůže objevit u parametru typu.", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "Modifikátor „{0}“ se nemůže vyskytovat v deklaraci „using“.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "Modifikátor {0} se nemůže vyskytovat v deklaraci „await using“.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "Modifikátor {0} se nemůže objevit v signatuře indexu.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "Modifikátor {0} se nemůže objevit u elementů třídy tohoto typu.", "_0_modifier_cannot_be_used_here_1042": "Modifik<PERSON><PERSON> {0} tady nej<PERSON>.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "Modifikátor {0} nejde použít v ambientním kontextu.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "Modifik<PERSON>tor {0} nej<PERSON> p<PERSON>žít s modifikátorem {1}.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "Modifikátor {0} se nedá použít s privátním identifikátorem.", "_0_modifier_must_precede_1_modifier_1029": "Modifikátor {0} se musí vyskytovat před modifikátorem {1}.", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "<PERSON> „\\{0}“ musí následovat výraz s hodnotou vlastnosti Unicode uzavřený do složených závorek.", "_0_needs_an_explicit_type_annotation_2782": "{0} vyžaduje explicitní anotaci typu.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "{0} j<PERSON><PERSON> o<PERSON> na typ, ale tady se používá jako obor n<PERSON>vů.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "{0} o<PERSON><PERSON><PERSON><PERSON> j<PERSON> na typ, ale p<PERSON>žívá se tady jako hodnota.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "{0} o<PERSON><PERSON><PERSON><PERSON> jeno<PERSON> na typ, ale tady se používá jako hodnota. Nechtěli jste použít {1} v {0}?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "‚{0}‘ odka<PERSON><PERSON> jen na typ, ale tady se používá jako hodnota. Potřebujete změnit cílovou knihovnu? Zkuste změnit možnost kompilátoru ‚lib‘ na es2015 nebo novější.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "{0} odkazuje na globální UMD, ale aktuální soubor je modul. Zvažte raději přidání importu.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "{0} od<PERSON><PERSON><PERSON> na hodnotu, ale tady se používá jako typ. Měli jste na mysli typeof {0}?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "„{0}“ se překládá na typ a musí být v tomto souboru označen jako „pouze typ“, než se znovu exportuje, k<PERSON>ž je povolená možnost „{1}“. Zvažte možnost použít „import type“, kde se importuje „{0}“.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "„{0}“ se překládá na typ a musí být v tomto souboru označen jako „pouze typ“, než se znovu exportuje, k<PERSON>ž je povolená možnost „{1}“. Zvažte možnost použít „export type { {0} as default }“.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "„{0}“ se překládá na deklaraci „pouze typ“ a musí se exportovat pomocí importu „pouze typ“, k<PERSON><PERSON> je povolena možnost „verbatimModuleSyntax“.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "„{0}“ se překládá na deklaraci „pouze typ“ a musí být v tomto souboru označen jako „pouze typ“, než se znovu exportuje, k<PERSON>ž je povolená možnost „{1}“. Zvažte možnost použít „import type“, kde se importuje „{0}“.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "„{0}“ se překládá na deklaraci „pouze typ“ a musí být v tomto souboru označen jako „pouze typ“, než se znovu exportuje, k<PERSON><PERSON> je povolená možnost „{1}“. Zvažte možnost použít „export type { {0} as default }“.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "Hodnota „{0}“ se překládá na deklaraci „pouze typ“ a musí se znovu exportovat pomocí zpětného exportu „pouze typ“, k<PERSON><PERSON> je povoleno {1}.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> slovo {0} by m<PERSON><PERSON> b<PERSON><PERSON> nastaveno uvnitř objektu compilerOptions konfiguračního souboru JSON.", "_0_tag_already_specified_1223": "Značka {0} se u<PERSON> specifikovala.", "_0_was_also_declared_here_6203": "{0} se dekla<PERSON>alo i tady.", "_0_was_exported_here_1377": "{0} se <PERSON><PERSON><PERSON> tady.", "_0_was_imported_here_1376": "{0} se <PERSON><PERSON><PERSON> tady.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "{0} s chybějící anotací návratového typu má implicitně návratový typ {1}.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "{0} s chybějící anotací návratového typu má implicitně typ yield {1}.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Modifikátor abstract se může objevit jenom v deklaraci třídy, metody nebo vlastnosti.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Modifikátor accessor se může objevit jenom v deklaraci vlastnosti.", "and_here_6204": "a tady.", "arguments_cannot_be_referenced_in_property_initializers_2815": "Na argumenty nejde odkazovat v inicializátorech vlastností.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "auto: Považovat soubory s importy, exporty, import.meta, jsx (s jsx: react-jsx) nebo formátem esm (s modulem node16+) za moduly.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "Výraz „await“ nelze použít uvnitř statického bloku třídy.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Výrazy await se tady povolují jen na nejvyšší úrovni souboru, k<PERSON><PERSON> je daný soubor modul, ale tento soubor nemá žádné importy ani exporty. Zvažte možnost přidat export {}, aby se tento soubor převedl na modul.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Výrazy await se povolují jen v asynchronních funkcích na nejvyšší úrovni modulů.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Výrazy await nejdou použít v inicializátoru parametru.", "await_has_no_effect_on_the_type_of_this_expression_80007": "Výraz await nemá žádný vliv na typ tohoto výrazu.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "Příkazy „await using“ jsou povoleny jen na nejvyšší úrovni souboru, k<PERSON><PERSON> je daný soubor modul, ale tento soubor nemá žádné importy ani exporty. Zvažte možnost přidat „export {}“, aby se tento soubor převedl na modul.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "Výrazy „await“ se povolují jen v asynchronních funkcích na nejvyšší úrovni modulů.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "Příkazy „await using“ nelze použít uvnitř statického bloku třídy.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "Možnost baseUrl je nastavená na {0}, pomo<PERSON><PERSON> této hodnoty se přeloží název modulu {1}, který není relativní.", "c_must_be_followed_by_an_ASCII_letter_1512": "<PERSON> „\\c“ musí následovat písmeno v ASCII.", "can_only_be_used_at_the_start_of_a_file_18026": "#! se dá použít jen na začátku souboru.", "case_or_default_expected_1130": "Očekává se case nebo default.", "catch_or_finally_expected_1472": "Očekávalo se catch nebo finally.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "Inicializátor <PERSON> výčtu const se vyhodnotil na nekonečnou hodnotu.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "Inicializátor <PERSON> výčtu const se vyhodnotil na nepovolenou hodnotu NaN.", "const_enum_member_initializers_must_be_constant_expressions_2474": "Inicializátory členů konstantního výčtu musí být konstantní výrazy.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Výčty const se daj<PERSON> p<PERSON>t jenom ve výrazech přístupu k vlastnosti nebo indexu nebo na pravé straně deklarace importu, přiřazení exportu nebo dotazu na typ.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "constructor se nedá použít jako název vlastnosti parametru.", "constructor_is_a_reserved_word_18012": "#constructor je rezervo<PERSON><PERSON> slovo.", "default_Colon_6903": "výchozí:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "Příkaz delete nejde volat u identifikátoru ve striktním režimu.", "export_Asterisk_does_not_re_export_a_default_1195": "export * neprovádí opakovaný export výchozí hodnoty.", "export_can_only_be_used_in_TypeScript_files_8003": "export = se dá používat jen v typescriptových souborech.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Modifikátor export se nedá použít u ambientních modulů a rozšíření modulů, protože jsou vždy viditelné.", "extends_clause_already_seen_1172": "Klauzule extends se už jednou vyskytla.", "extends_clause_must_precede_implements_clause_1173": "Klauzule extends se musí vyskytovat před klauzulí implements.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "Klauzule extends exportované třídy {0} má nebo používá privátní název {1}.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "Klauzule extends exportované třídy má nebo používá privátní název {0}.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "Klauzule extends exportovaného rozhraní {0} má nebo používá privátní název {1}.", "false_unless_composite_is_set_6906": "„false“, pokud není nastavené „composite“.", "false_unless_strict_is_set_6905": "„false“, pokud není nastavená hodnota „strict“.", "file_6025": "soubor", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "<PERSON><PERSON><PERSON><PERSON> for await se tady povolují jen na nejvyšší úrovni souboru, k<PERSON><PERSON> je daný soubor modul, ale tento soubor nemá žádné importy ani exporty. Zvažte možnost přidat export {}, aby se tento soubor převedl na modul.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "<PERSON><PERSON><PERSON><PERSON> for await se povolují jen v asynchronních funkcích na nejvyšší úrovni modulů.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "<PERSON><PERSON><PERSON><PERSON> „for await“ nelze použít uvnitř statického bloku třídy.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "Přístupové objekty get a set nemůžou deklarovat parametry this.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "[], pokud je z<PERSON> „soubory“, jinak [\"**/*\"]5D;", "implements_clause_already_seen_1175": "Klauzule implements se už jednou vyskytla.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Klauzule implements se dají p<PERSON>žívat jen v typescriptových souborech.", "import_can_only_be_used_in_TypeScript_files_8002": "import = se dá používat jen v typescriptových souborech.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Deklarace infer jsou povolené jenom v klauzuli extends podmíněného typu.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "Po „\\k“ musí následovat název zachycující skupiny uzavřený do ostrých závorek.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "Nepovoluje se používat let jako název v deklaracích let nebo const.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === `AMD` or `UMD` or `System` or `ES6`, then `Classic`, Otherwise `Node`", "module_system_or_esModuleInterop_6904": "module === \"system\" or esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "Výraz new s chybějící signaturou konstruktoru v cíli má implicitně typ any.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "[\"node_modules\", \"bower_components\", \"jspm_packages\"] a hodnotu „outDir“, pokud je zadána.", "one_of_Colon_6900": "jeden z:", "one_or_more_Colon_6901": "1 nebo více:", "options_6024": "m<PERSON><PERSON><PERSON><PERSON>", "or_JSX_element_expected_1145": "Očekával se element { nebo JSX.", "or_expected_1144": "Očekává se znak { nebo ;.", "package_json_does_not_have_a_0_field_6100": "Soubor package.j<PERSON> neo<PERSON><PERSON> pole {0}.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "package.json nemá položku typesVersions, která by od<PERSON><PERSON><PERSON><PERSON><PERSON> verzi {0}.", "package_json_had_a_falsy_0_field_6220": "Soubor package.json obsahoval neplatné pole {0}.", "package_json_has_0_field_1_that_references_2_6101": "Soubor package.json m<PERSON> pole {0} {1}, k<PERSON><PERSON> od<PERSON> {2}.", "package_json_has_a_peerDependencies_field_6281": "Soubor package.json m<PERSON> „peerDependencies“.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "package.json má položku typesVersions {0}, k<PERSON><PERSON> není platný roz<PERSON>h semver.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "package.json má položku typesVersions {0}, kter<PERSON> odpovídá verzi kompilátoru {1}. <PERSON><PERSON><PERSON> se vzor, kter<PERSON> bude odpovídat názvu modulu {2}.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "package.json má pole typesVersions s mapováními cesty specifickými pro verzi.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "package.json scope {0} implicitně mapuje specifikátor {1} na hodnotu null.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "package.json scope {0} má neplatný typ pro cíl specifikátoru {1}.", "package_json_scope_0_has_no_imports_defined_6273": "package.json scope {0} nemá definovány žádné importy.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "Je zadaná možnost paths, hled<PERSON> se vzor, k<PERSON><PERSON> odpovídá názvu modulu {0}.", "q_is_only_available_inside_character_class_1511": "„\\q“ je k dispozici pouze uvnitř tří<PERSON> z<PERSON>.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "Po „\\q“ musí následovat řetězcové alternativy uzavřené ve složených závorkách.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "Modifikátor readonly se může objevit jenom v deklaraci vlastnosti nebo signatuře indexu.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Modifikátor typu readonly se povoluje jen pro typy literálů pole a řazené kolekce členů.", "require_call_may_be_converted_to_an_import_80005": "Volání require se dá převést na import.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "resolution-mode se dá nastavit pouze pro importy type-only.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "resolution-mode je jediný platný klíč pro kontrolní výrazy importu typů.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "„resolution-mode“ je jediný platný klíč pro atributy importu typů.", "resolution_mode_should_be_either_require_or_import_1453": "resolution-mode by m<PERSON><PERSON> b<PERSON><PERSON> b<PERSON> require, nebo import.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "Je nastavená možnost rootDirs, použije se k překladu relativního názvu modulu {0}.", "super_can_only_be_referenced_in_a_derived_class_2335": "Na vlastnost super se dá odkazovat jenom v odvozené třídě.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "Na možnost super je možné odkazovat jenom ve členech odvozených tříd nebo výrazů literálu objektu.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Na vlastnost super se nedá odkazovat v názvu počítané vlastnosti.", "super_cannot_be_referenced_in_constructor_arguments_2336": "Na vlastnost super se nedá odkazovat v argumentech konstruktoru.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "Možnost super je povolená jenom ve členech výrazů literálu objektu, pokud je možnost target ES2015 nebo vyšší.", "super_may_not_use_type_arguments_2754": "super nemůže používat argumenty typů.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "Před přístupem k vlastnosti super v konstruktoru odvozené třídy se musí zavolat super.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "Možnost super se musí volat před přístupem k this v konstruktoru odvozené třídy.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "Po vlastnosti super musí následovat seznam argumentů nebo přístup ke členu.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "Přístup k vlastnostem pomocí super je povolený jenom v konstruktoru, členské funkci nebo členském přístupovém objektu odvozené třídy.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "Na vlastnost this se nedá odkazovat v názvu počítaného prostředku.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "Na vlastnost this se nedá odkazovat v modulu nebo těle oboru názvů.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "Na vlastnost this se nedá odkazovat v inicializátoru statické vlastnosti.", "this_cannot_be_referenced_in_current_location_2332": "Na vlastnost this se nedá odkazovat v aktuálním umístění.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "<PERSON>ž<PERSON>t this má implicitně typ any, protože nemá anotaci typu.", "true_for_ES2022_and_above_including_ESNext_6930": "„true“ pro ES2022 a vyšší, včetně ESNext.", "true_if_composite_false_otherwise_6909": "„true“, pokud „composite“, „false“ jinak", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "Má hodnotu True, k<PERSON>ž „moduleResolution“ je „node16“, „nodenext“ nebo „bundler“; v opačném případě má hodnotu False.", "tsc_Colon_The_TypeScript_Compiler_6922": "TSC: kom<PERSON><PERSON><PERSON><PERSON>u", "type_Colon_6902": "typ:", "unique_symbol_types_are_not_allowed_here_1335": "Typy unique symbol tady nejsou povolené.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Typy unique symbol jsou povolené jen u proměnných v příkazu proměnné.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Typy unique symbol nejde použít v deklaraci proměnné s názvem vazby.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "Direktiva use strict se nedá použít se seznamem parametrů, které nejsou jednodu<PERSON>.", "use_strict_directive_used_here_1349": "Direktiva use strict se použila tady.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "Příkazy with se ve funkčním bloku async nepovolují.", "with_statements_are_not_allowed_in_strict_mode_1101": "Příkazy with se ve striktním režimu nepovolují.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Implicitním výsledkem výrazu yield je typ any, protože v jeho obsahujícím generátoru chybí anotace návratového typu.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Výrazy yield nejde použít v inicializátoru parametru."}