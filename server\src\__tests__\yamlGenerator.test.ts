/**
 * Tests for YAML Generator Service
 */

import { YamlGeneratorService } from '../services/yamlGenerator'
import { WorkflowDefinition } from '../types/moveworks'

describe('YamlGeneratorService', () => {
  let yamlGenerator: YamlGeneratorService

  beforeEach(() => {
    yamlGenerator = new YamlGeneratorService()
  })

  describe('generateYaml', () => {
    it('should generate valid YAML for a simple workflow', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        description: 'A test workflow',
        steps: [
          {
            id: 'step1',
            type: 'action',
            position: { x: 0, y: 0 },
            config: {
              action_name: 'mw.get_user_by_email',
              output_key: 'user_info',
              input_args: {
                email: 'data.user_email'
              }
            },
            connections: []
          }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const yaml = yamlGenerator.generateYaml(workflow)

      expect(yaml).toContain('steps:')
      expect(yaml).toContain('action:')
      expect(yaml).toContain('action_name: mw.get_user_by_email')
      expect(yaml).toContain('output_key: user_info')
      expect(yaml).toContain('email: data.user_email')
    })

    it('should generate YAML with comments when requested', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        description: 'A test workflow',
        steps: [],
        created_at: new Date(),
        updated_at: new Date()
      }

      const yaml = yamlGenerator.generateYaml(workflow, true)

      expect(yaml).toContain('# Moveworks Compound Action: Test Workflow')
      expect(yaml).toContain('# Description: A test workflow')
      expect(yaml).toContain('# Generated by Moveworks Compound Action Assistant')
    })

    it('should handle script steps correctly', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        steps: [
          {
            id: 'step1',
            type: 'script',
            position: { x: 0, y: 0 },
            config: {
              output_key: 'processed_data',
              input_args: {
                raw_data: 'data.input'
              },
              code: 'return {"processed": True}'
            },
            connections: []
          }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const yaml = yamlGenerator.generateYaml(workflow)

      expect(yaml).toContain('script:')
      expect(yaml).toContain('output_key: processed_data')
      expect(yaml).toContain('code:')
    })
  })

  describe('calculateComplexityScore', () => {
    it('should calculate correct complexity for simple workflow', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        steps: [
          { id: '1', type: 'action', position: { x: 0, y: 0 }, config: {}, connections: [] },
          { id: '2', type: 'script', position: { x: 0, y: 0 }, config: {}, connections: [] }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const score = yamlGenerator.calculateComplexityScore(workflow)
      expect(score).toBe(2) // 1 for action + 1 for script
    })

    it('should calculate higher complexity for control flow steps', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        steps: [
          { id: '1', type: 'switch', position: { x: 0, y: 0 }, config: {}, connections: [] },
          { id: '2', type: 'for', position: { x: 0, y: 0 }, config: {}, connections: [] },
          { id: '3', type: 'parallel', position: { x: 0, y: 0 }, config: {}, connections: [] }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const score = yamlGenerator.calculateComplexityScore(workflow)
      expect(score).toBe(12) // 3 + 4 + 5
    })
  })

  describe('estimateExecutionTime', () => {
    it('should estimate execution time for workflow', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        steps: [
          { id: '1', type: 'action', position: { x: 0, y: 0 }, config: {}, connections: [] },
          { id: '2', type: 'script', position: { x: 0, y: 0 }, config: {}, connections: [] }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const time = yamlGenerator.estimateExecutionTime(workflow)
      expect(time).toBe(450) // 300 for action + 150 for script
    })

    it('should handle parallel steps correctly', () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        steps: [
          { id: '1', type: 'parallel', position: { x: 0, y: 0 }, config: {}, connections: [] }
        ],
        created_at: new Date(),
        updated_at: new Date()
      }

      const time = yamlGenerator.estimateExecutionTime(workflow)
      expect(time).toBe(50) // Just coordination overhead
    })
  })
})
