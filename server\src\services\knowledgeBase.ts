/**
 * Knowledge Base Service
 * Manages Moveworks built-in actions, DSL functions, and validation rules
 */

import { MoveworksBuiltinAction } from '../types/moveworks';

export class KnowledgeBaseService {
  private readonly builtinActions: Map<string, MoveworksBuiltinAction> = new Map();
  private readonly dslFunctions: Map<string, any> = new Map();

  constructor() {
    this.initializeBuiltinActions();
    this.initializeDSLFunctions();
  }

  /**
   * Get all built-in Moveworks actions
   */
  public getBuiltinActions(): MoveworksBuiltinAction[] {
    return Array.from(this.builtinActions.values());
  }

  /**
   * Get specific built-in action by name
   */
  public getBuiltinAction(name: string): MoveworksBuiltinAction | undefined {
    return this.builtinActions.get(name);
  }

  /**
   * Check if action is a Moveworks built-in
   */
  public isBuiltinAction(actionName: string): boolean {
    return this.builtinActions.has(actionName);
  }

  /**
   * Get DSL functions
   */
  public getDSLFunctions(): any[] {
    return Array.from(this.dslFunctions.values());
  }

  /**
   * Initialize built-in Moveworks actions based on documentation
   */
  private initializeBuiltinActions(): void {
    // User management actions
    this.builtinActions.set('mw.get_user_by_email', {
      name: 'mw.get_user_by_email',
      description: 'Retrieve user information by email address',
      input_args: [
        {
          name: 'email',
          type: 'string',
          required: true,
          description: 'Email address of the user to retrieve'
        }
      ],
      output_structure: {
        user_id: 'string',
        email: 'string',
        display_name: 'string',
        department: 'string',
        location: 'string',
        manager_email: 'string'
      }
    });

    // Notification actions
    this.builtinActions.set('mw.send_plaintext_chat_notification', {
      name: 'mw.send_plaintext_chat_notification',
      description: 'Send a plain text chat notification to a user',
      input_args: [
        {
          name: 'user_record_id',
          type: 'string',
          required: true,
          description: 'User record ID from mw.get_user_by_email'
        },
        {
          name: 'message',
          type: 'string',
          required: true,
          description: 'Message content to send'
        }
      ],
      output_structure: {
        notification_id: 'string',
        status: 'string',
        sent_at: 'string'
      }
    });

    // Email actions
    this.builtinActions.set('mw.send_email', {
      name: 'mw.send_email',
      description: 'Send an email to specified recipients',
      input_args: [
        {
          name: 'to',
          type: 'array',
          required: true,
          description: 'Array of recipient email addresses'
        },
        {
          name: 'subject',
          type: 'string',
          required: true,
          description: 'Email subject line'
        },
        {
          name: 'body',
          type: 'string',
          required: true,
          description: 'Email body content'
        },
        {
          name: 'cc',
          type: 'array',
          required: false,
          description: 'Array of CC recipient email addresses'
        },
        {
          name: 'bcc',
          type: 'array',
          required: false,
          description: 'Array of BCC recipient email addresses'
        }
      ],
      output_structure: {
        email_id: 'string',
        status: 'string',
        sent_at: 'string'
      }
    });

    // Approval actions
    this.builtinActions.set('mw.create_generic_approval_request', {
      name: 'mw.create_generic_approval_request',
      description: 'Create a generic approval request',
      input_args: [
        {
          name: 'approver_user_record_id',
          type: 'string',
          required: true,
          description: 'User record ID of the approver'
        },
        {
          name: 'title',
          type: 'string',
          required: true,
          description: 'Title of the approval request'
        },
        {
          name: 'description',
          type: 'string',
          required: true,
          description: 'Description of what needs approval'
        },
        {
          name: 'requester_user_record_id',
          type: 'string',
          required: false,
          description: 'User record ID of the requester'
        }
      ],
      output_structure: {
        approval_request_id: 'string',
        status: 'string',
        created_at: 'string'
      }
    });

    this.builtinActions.set('mw.get_approval_request_status', {
      name: 'mw.get_approval_request_status',
      description: 'Get the status of an approval request',
      input_args: [
        {
          name: 'approval_request_id',
          type: 'string',
          required: true,
          description: 'ID of the approval request'
        }
      ],
      output_structure: {
        approval_request_id: 'string',
        status: 'string',
        approved_at: 'string',
        approver_comments: 'string'
      }
    });

    // Ticket management actions
    this.builtinActions.set('mw.create_ticket', {
      name: 'mw.create_ticket',
      description: 'Create a new ticket in the ticketing system',
      input_args: [
        {
          name: 'title',
          type: 'string',
          required: true,
          description: 'Ticket title'
        },
        {
          name: 'description',
          type: 'string',
          required: true,
          description: 'Ticket description'
        },
        {
          name: 'priority',
          type: 'string',
          required: false,
          description: 'Ticket priority (low, medium, high, critical)'
        },
        {
          name: 'assignee_email',
          type: 'string',
          required: false,
          description: 'Email of the assignee'
        }
      ],
      output_structure: {
        ticket_id: 'string',
        ticket_number: 'string',
        status: 'string',
        created_at: 'string'
      }
    });

    this.builtinActions.set('mw.update_ticket', {
      name: 'mw.update_ticket',
      description: 'Update an existing ticket',
      input_args: [
        {
          name: 'ticket_id',
          type: 'string',
          required: true,
          description: 'ID of the ticket to update'
        },
        {
          name: 'status',
          type: 'string',
          required: false,
          description: 'New ticket status'
        },
        {
          name: 'comment',
          type: 'string',
          required: false,
          description: 'Comment to add to the ticket'
        },
        {
          name: 'assignee_email',
          type: 'string',
          required: false,
          description: 'New assignee email'
        }
      ],
      output_structure: {
        ticket_id: 'string',
        status: 'string',
        updated_at: 'string'
      }
    });

    this.builtinActions.set('mw.get_ticket_status', {
      name: 'mw.get_ticket_status',
      description: 'Get the current status of a ticket',
      input_args: [
        {
          name: 'ticket_id',
          type: 'string',
          required: true,
          description: 'ID of the ticket'
        }
      ],
      output_structure: {
        ticket_id: 'string',
        ticket_number: 'string',
        status: 'string',
        assignee: 'string',
        created_at: 'string',
        updated_at: 'string'
      }
    });
  }

  /**
   * Initialize DSL functions based on documentation
   */
  private initializeDSLFunctions(): void {
    // String functions
    this.dslFunctions.set('$CONCAT', {
      name: '$CONCAT',
      description: 'Concatenate multiple strings',
      syntax: '$CONCAT(string1, string2, ...)',
      example: '$CONCAT("Hello ", data.user.name)',
      category: 'string'
    });

    this.dslFunctions.set('$LOWERCASE', {
      name: '$LOWERCASE',
      description: 'Convert string to lowercase',
      syntax: '$LOWERCASE(string)',
      example: '$LOWERCASE(data.user.email)',
      category: 'string'
    });

    this.dslFunctions.set('$UPPERCASE', {
      name: '$UPPERCASE',
      description: 'Convert string to uppercase',
      syntax: '$UPPERCASE(string)',
      example: '$UPPERCASE(data.department)',
      category: 'string'
    });

    this.dslFunctions.set('$TITLECASE', {
      name: '$TITLECASE',
      description: 'Convert string to title case',
      syntax: '$TITLECASE(string)',
      example: '$TITLECASE(data.user.name)',
      category: 'string'
    });

    // Array functions
    this.dslFunctions.set('$MAP', {
      name: '$MAP',
      description: 'Map over array elements',
      syntax: '$MAP(array, expression)',
      example: '$MAP(data.users, item.email)',
      category: 'array'
    });

    this.dslFunctions.set('$FILTER', {
      name: '$FILTER',
      description: 'Filter array elements',
      syntax: '$FILTER(array, condition)',
      example: '$FILTER(data.users, item.active == true)',
      category: 'array'
    });

    // Math functions
    this.dslFunctions.set('$ADD', {
      name: '$ADD',
      description: 'Add numbers',
      syntax: '$ADD(number1, number2, ...)',
      example: '$ADD(data.price, data.tax)',
      category: 'math'
    });

    this.dslFunctions.set('$SUBTRACT', {
      name: '$SUBTRACT',
      description: 'Subtract numbers',
      syntax: '$SUBTRACT(number1, number2)',
      example: '$SUBTRACT(data.total, data.discount)',
      category: 'math'
    });

    // Date functions
    this.dslFunctions.set('$NOW', {
      name: '$NOW',
      description: 'Get current timestamp',
      syntax: '$NOW()',
      example: '$NOW()',
      category: 'date'
    });

    this.dslFunctions.set('$FORMAT_DATE', {
      name: '$FORMAT_DATE',
      description: 'Format date string',
      syntax: '$FORMAT_DATE(date, format)',
      example: '$FORMAT_DATE(data.created_at, "YYYY-MM-DD")',
      category: 'date'
    });
  }

  /**
   * Get action suggestions based on context
   */
  public getActionSuggestions(context: string): MoveworksBuiltinAction[] {
    const suggestions: MoveworksBuiltinAction[] = [];
    const contextLower = context.toLowerCase();

    // Simple keyword-based suggestions
    if (contextLower.includes('user') || contextLower.includes('email')) {
      const getUserAction = this.builtinActions.get('mw.get_user_by_email');
      if (getUserAction) suggestions.push(getUserAction);
    }

    if (contextLower.includes('notification') || contextLower.includes('message')) {
      const notifyAction = this.builtinActions.get('mw.send_plaintext_chat_notification');
      if (notifyAction) suggestions.push(notifyAction);
    }

    if (contextLower.includes('approval') || contextLower.includes('approve')) {
      const approvalAction = this.builtinActions.get('mw.create_generic_approval_request');
      if (approvalAction) suggestions.push(approvalAction);
    }

    if (contextLower.includes('ticket') || contextLower.includes('issue')) {
      const ticketAction = this.builtinActions.get('mw.create_ticket');
      if (ticketAction) suggestions.push(ticketAction);
    }

    return suggestions;
  }
}
